<?php

use Illuminate\Foundation\Inspiring;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->describe('Display an inspiring quote');

Artisan::command('mc:web {controller}', function (){
   $controller = 'Web\\Front\\'.$this->argument('controller');
   $this->call("make:controller", ["name" => $controller]);
});

Artisan::command('mc:admin {controller}', function (){
   $controller = 'Web\\Admin\\'.$this->argument('controller');
   $this->call("make:controller", ["name" => $controller]);
});

Artisan::command('mc:api {controller}', function (){
    $controller = 'Api\\v1\\'.$this->argument('controller');
    $this->call("make:controller", ["name" => $controller]);
});
Artisan::command('mc:v2-api {controller}', function () {
    $controller = 'Api\\SpaV2\\'.$this->argument('controller');
    $this->call('make:controller', ['name' => $controller]);
});

Artisan::command('mm {model} {--m | migration}', function (){
    $model = 'Models\\'.$this->argument('model');
    $params = ["name" => $model];

    if ($this->option('migration')) {
        $params['-m'] = true;
    }

    $this->call("make:model", $params);
});

Artisan::command('package:set:user', function (){
    $packages = \App\Models\Package::all();
    $pkg = $packages->first();
    $users = \App\Models\User::all();
    foreach ($users as $user){
        $user->package_id = $pkg->id;
        $user->package_rules = $pkg->rule;
        $user->save();
    }
});

Artisan::command('test', function (){
    $users = \App\Models\User::whereHas('package', function ($pack){
        $pack->where('plan', \App\Enums\PackagePlan::MONTHLY);
    })->whereNull('stripe_id')->where('package_validity', '<', \Carbon\Carbon::now())->get();

    $users->each->update([
       'package_validity' => \Carbon\Carbon::now()->addYear()
    ]);

    echo "{{$users->count()}} users package extended".PHP_EOL;
});



//Coupon generated easyjobs-spc-1 to 100

Artisan::command("coupon:special", function (){
    $couponGenerate = function ($name,$code){
        return [
            'name' => $name,
            'code' => $code,
            'scope' => 'appsumo',
            'amount' => 49,
            'starts_at' => \Carbon\Carbon::now(),
            'ends_at' => \Carbon\Carbon::parse("2020-06-01")
        ];
    };
    $coupons = [];
    foreach (range(1,100) as $val) {
        $coupons[] = $couponGenerate('easyjobs-spc-'.$val,random_strings(20));
    }
    \App\Models\Coupon::insert($coupons);
});

Artisan::command('mail:check', function (){
    $user = \App\Models\User::where('email','<EMAIL>')->first();
    event(new \App\Events\UserRegistered($user));
});

Artisan::command('parse-resume', function (){
    $file = "https://content.easy.jobs/assets/1768/Jenat-ara-_UIUX-_Resume-new%281%29.pdf";
    $cs = new \App\Services\CandidateService();
    dd($cs->getCandidateAiScore(141,$file));
});
