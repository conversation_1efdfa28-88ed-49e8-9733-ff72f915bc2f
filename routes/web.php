<?php

use App\Http\Controllers\Api\AdminV1\Report\CompanyReportController;
use App\Http\Controllers\Api\AdminV1\Report\TestUserReportController;
use App\Http\Controllers\Api\AdminV1\Report\CustomerReportController;
use App\Http\Controllers\Api\AdminV1\Report\TransactionReportController;
use App\Http\Controllers\Web\Company\ReportController;
use App\Models\JobPost;

$webRoutes = function () {
    Route::group(['middleware' => ['admin.token.valid'], ], function () {
        Route::get('customer-report', [CustomerReportController::class, 'customerReport']);
        Route::get('company-report', [CompanyReportController::class, 'companyReport']);
        Route::get('transaction-report', [TransactionReportController::class, 'exportTransactions']);
        Route::get('test-user-report', [TestUserReportController::class, 'index']);
    });

    Route::get('/top-companies-by-number-of-visitor', [ReportController::class, 'topCompaniesDownloadByNumberOfVisits']);
    Route::get('/top-companies-download-by-jobs-count', [ReportController::class, 'topCompaniesDownloadByJobsCount']);

    Route::post('stripe/hooks', 'PaymentController@stripeWebHook')->name('stripe.hooks');
    Route::post('paypal/hooks', 'PaymentController@paypalWebHook')->name('paypal.hooks');
    Route::post('slack/hooks', 'SlackController@slackWebhook')->name('slack.hooks');
    Route::post('slack/interactive-action', 'SlackController@slackInteractiveAction');
    Route::post('calendly/hooks', 'WebhookController@calendlyWebHook')->name('calendly.hooks');

    Route::group(['prefix' => 'payment', 'as' => 'payment.'], function () {
        Route::get('complete', 'PaymentController@paymentComplete')->name('complete');
    });

    Route::namespace ('Sso')->group(function () {
        Route::get('{job}/apply', 'LoginController@authVerify')->name('easyjobs.auth.verify');
        Route::get('auth/sso/candidate', 'LoginController@ssoLoginForCustomDomain')->name('easyjobs.login')->middleware(['sso.validate.login.url']);
        Route::post('auth/sso/candidate', 'LoginController@ssoPostLoginForCustomDomain');
        Route::get('auth/sso/candidate/registration', 'LoginController@ssoRegistrationForCustomDomain')->name('easyjobs.registration')->middleware(['sso.validate.login.url']);
        Route::post('auth/sso/candidate/registration', 'LoginController@ssoPostRegistrationForCustomDomain');
        Route::get('/sign-in-oauth/{provider}', 'LoginController@loginViaSocial')->where('provider', 'google|linkedin')->name('sign-in-social')->middleware('social.domain.company');
        Route::get('api/v2/sign-in-oauth/{provider}/callback', 'LoginController@loginSocialGet')->where('provider', 'google|linkedin')->middleware('social.domain.company');

        Route::get('sso/locale/{locale?}', 'LoginController@setLocale')->where('provider', 'google|linkedin')->name('easyjobs.set-locale');
    });

    //Custom domain validation
    Route::get('/allowed-domain', 'CustomDomainController@checkDomainTls');
    Route::post('/redeem-coupons', 'AppSumoUserController@redeem');
    Route::post('/ajax/get-coupon-by-code', 'CouponController@getCouponByCode');

    Route::get('sitemap_index.xml', 'UtilityController@generateSiteMapIndex')->name('app.sitemap');
    Route::get('sitemap/index.xml', 'UtilityController@generateSiteMapIndexChild')->name('app.sitemap.child');
    Route::get('{company}/sitemap.xml', 'UtilityController@generateCompanySiteMap')->name('app.company.sitemap');
    Route::get('talent/jobs.xml', 'UtilityController@generateTalentXMLFeed');

    Route::get('/job/{job}/candidate/export', '\App\Http\Controllers\Api\SpaV2\JobCandidateController@exportCandidate');
    /*
     *  Routes for legacy support before v3.0.3 version.
     *  When we had changed job related id based route parameters urls to slug based.
     * */

    Route::get('/job/{jobId}/candidate', function ($jobId) {
        $jobPost = JobPost::findOrFail($jobId);
        return redirect()->route('job.candidate.legacy', ['jobId' => $jobPost->slug]);
    })->where('jobId', '[0-9]+')->name('job.candidate.legacy');

    Route::get('/job/{jobId}/pipeline', function ($jobId) {
        $jobPost = JobPost::findOrFail($jobId);
        return redirect()->route('job.pipeline.legacy',['jobId' => $jobPost->slug]);
    })->where('jobId', '[0-9]+')->name('job.pipeline.legacy');

    Route::get('/job/{jobId}/edit', function ($jobId) {
        $jobPost = JobPost::findOrFail($jobId);
        return redirect()->route('job.edit.legacy', ['jobId' => $jobPost->slug]);
    })->where('jobId', '[0-9]+')->name('job.edit.legacy');

    Route::get('/job/applicant/{applicant}/attachment/{attachment}/download', '\App\Http\Controllers\Api\SpaV2\ApplicantController@downloadAttachment');
    Route::get('/wp/job/{job}/candidate/export', '\App\Http\Controllers\Api\WpV1\JobController@exportCandidate')->name('wp.job.export');

    Route::get('/{any}', function () {
        return view('app-easy-jobs.spa-index');
    })->where('any', '.*');

    Route::namespace ('Sso')->group(function () {
        Route::get('login', function () {})->name('login');
        Route::get('password/reset/{token?}', 'VerificationController@showResetForm')->name('password.reset');
        Route::post('password/email', 'VerificationController@sendResetLinkEmail');
        Route::post('password/reset', 'VerificationController@reset');
    });

    Route::get('verify/email/{id}', function ($email) {})->name('auth.email.verify');

    // just for route matching routes with vue
    Route::get('/candidate/applied-jobs', function () {})->name('candidate.applied-jobs');
    Route::get('/candidate/assessment/{assessment}', function () {})->name('candidate.assessment.login');

    Route::get('/calendly/invite/{uuid}', function () {})->name('calendly.invitation.link');

    Route::get('/job/invitation-respond', function () {})->name('job.invitations.respond');

};



$APP_DOMAIN = 'app.' . config('easyjob.app.route');
if (config('app.env', 'local') == 'staging') {
    $APP_DOMAIN = "staging.easy.jobs";
}

// app.easy.jobs
Route::group(['domain' => $APP_DOMAIN, 'middleware' =>  ['set.domain.company', 'company.domain.remove']], $webRoutes);

// for enterprise admin panel
Route::group(['domain' => 'app.{domain}', 'middleware' =>  ['set.domain.company', 'company.admin.domain','company.domain.remove']], $webRoutes);
