<?php

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::group(['middleware' => ['wp.auth', 'wp.version', 'wp.api.validate.state-version']], function () {

    Route::get('subscription/packages', 'CompanyController@subscriptionPackages')->name('subscription.packages');
    Route::group(['prefix' => 'agency'], function () {
        Route::get('status', 'AgencyController@agencyStatus');
        Route::get('job/published', 'AgencyController@agencyPublishedJobs');
        Route::get('job/categories', 'AgencyController@getJobCategories');
        Route::get('job/locations', 'AgencyController@getJobLocations');
        Route::get('stats', 'AgencyController@agencyInfo');
        Route::get('featured-jobs', 'AgencyController@featuredJobs');
    });

    Route::get('job/all', 'JobController@allList');
    Route::get('job/published', 'JobController@publishedJobs')->name('job.published');;
    Route::get('job/active', 'JobController@activeJobs');
    Route::get('job/locations', 'JobController@getJobLocations');
    Route::get('job/draft', 'JobController@draftJobs');
    Route::get('job/archive', 'JobController@archiveJobs');
    Route::get('job/templates', 'JobController@getJobTemplates');
    Route::get('job/{job}/details', 'JobController@jobDetails');
    Route::get('job/{job}/pipeline', 'JobController@pipelineDetails');
    Route::get('job/{job}/candidates', 'JobController@jobCandidates');
    Route::get('job/{job}/candidates/export', 'JobController@exportCandidateLink');
    Route::post('job/{job}/pipeline/update', 'JobController@pipelineUpdate');
    Route::post('job/{job}/candidate/pipeline', 'JobController@changePipelineForCandidate');


    // Job Applicants
    Route::get('job/applicants/{appId}', 'CandidateController@getCandidateDetails');
    Route::post('job/applicants/{applicant}/rating', 'CandidateController@saveRating');

    // Applicant Notes
    Route::get('job/applicants/{applicant}/note', 'CandidateController@getNotes');
    Route::post('job/applicants/{applicant}/note', 'CandidateController@saveNote');
    Route::post('job/applicants/{applicant}/note/{note}/delete', 'CandidateController@deleteNote');

    // Applicant Details Message
    Route::get('job/applicants/{applicant}/message', 'CandidateController@getMessages');
    Route::post('job/applicants/{applicant}/message', 'CandidateController@saveMessage');
    Route::post('job/applicants/{applicant}/draft-message', 'CandidateController@saveDraftMessage');


    Route::group(['prefix' => 'job'], function () {
        Route::post('create', 'JobController@jobCreate');
        Route::get('{job}/basic', 'JobController@jobBasicInfo');
        Route::post('{job}/update', 'JobController@jobUpdate');
        Route::post('{job}/pinned', 'JobController@updatePinnedJob');
        Route::post('{job}/delete', 'JobController@destroyJob');
        Route::post('{job}/change-status', 'JobController@changeJobStatus');
        Route::get('{job}/required-fields', 'JobController@jobRequiredFieldData');
        Route::post('{job}/required-fields', 'JobController@saveRequiredFields');
        Route::post('{job}/duplicate', 'JobPostController@duplicateJob')->name('job.duplicate');

        // Job Screening Question
        Route::get('{job}/screening', 'JobEvaluationController@jobScreeningQuestion');
        Route::post('{job}/screening', 'JobEvaluationController@saveScreeningQuestion');
        Route::delete('{job}/screening', 'JobEvaluationController@deleteScreeningQuestion');
        Route::get('screening-meta-data', 'JobEvaluationController@jobScreeningQuestionMetaData');

        // Job Quiz Question
        Route::get('{job}/quiz', 'JobEvaluationController@jobQuizQuestion');
        Route::post('{job}/quiz', 'JobEvaluationController@saveQuizQuestion');
        Route::delete('{job}/quiz', 'JobEvaluationController@deleteQuizQuestion');
        Route::get('quiz-meta-data', 'JobEvaluationController@jobQuizMetaData');

        // Job Candidate
        Route::post('{job}/candidate/onboard', 'JobController@onboardCandidate');
        Route::post('{job}/candidate/delete', 'JobController@deleteCandidate');


        // Candidate Invitation
        Route::get('{job}/invitations', 'JobCandidateController@getInvitations');
        Route::post('{job}/invitations/delete', 'JobCandidateController@deleteInvitation');
        Route::post('{job}/candidate/add', 'JobCandidateController@addCandidate');

        // Pending Candidate
        Route::get('{job}/candidate/pending', 'JobCandidateController@pendingCandidates');
        Route::post('{job}/candidate/pending/invite', 'JobCandidateController@invitePendingCandidates');
        Route::post('{job}/candidate/delete', 'JobCandidateController@deleteCandidate');
    });

    Route::group(['prefix' => 'company'], function () {
        /*agency plugin related route*/
        Route::get('details', 'CompanyController@details')->name('company.details');
        Route::get('info', 'CompanyController@info')->name('company.info');
        Route::get('details-info', 'CompanyController@detailsInfo');
        Route::get('stats', 'CompanyController@dashboardInfo');
        Route::get('recent-applicants', 'CompanyController@recentCandidates');
        Route::get('/recent-jobs/analytics', 'CompanyController@recentJobsAnalytics');
        Route::get('recent-jobs', 'CompanyController@recentJobs');
        Route::get('jobs', 'CompanyController@getJobs');
        Route::get('candidates', 'CompanyController@getCandidates');
        Route::get('analytics', 'CompanyController@getAnalytics');

        Route::group(['prefix' => 'setting'], function () {
            Route::get('basic-info', 'CompanySettingController@getCompanySettings');
            Route::post('basic-info', 'CompanySettingController@storeCompanySettings');

            // Company image manage
            Route::get('company-photo', 'CompanySettingController@getCompanySettingsPhoto');
            Route::post('company-photo', 'CompanySettingController@storeCompanySettingsPhoto');
            Route::delete('company-photo', 'CompanySettingController@removeCompanySettingsPhoto');

            Route::get('ai-setup', 'CompanySettingController@getAiSetup');
            Route::post('ai-setup', 'CompanySettingController@updateAiSetup');

            Route::get('verification-code', 'CompanySettingController@getVerificationCode');
            Route::post('verify', 'CompanySettingController@verifyCompany');
            Route::post('remove-powered-by', 'CompanySettingController@removePoweredBy');
            Route::post('remove-cover-photo', 'CompanySettingController@removeCoverPhoto');
            Route::post('show-life', 'CompanySettingController@storeShowLifeMeta');
            Route::post('brand-color', 'CompanySettingController@updateCompanyBrandColor');


            // Company Pipeline Setup Api
            Route::get('pipeline', 'PipelineSetupController@getPipelineList');
            Route::post('pipeline', 'PipelineSetupController@savePipeline');
            Route::post('pipeline/{pipeline}/update', 'PipelineSetupController@updatePipeline');
            Route::post('pipeline/{pipeline}/delete', 'PipelineSetupController@deletePipeline');


            Route::get('category', 'CompanySettingController@getCategories');
            Route::post('category/save', 'CompanySettingController@saveCategory');
            Route::post('category/{category}/delete', 'CompanySettingController@deleteCategory');

            Route::get('skill', 'CompanySettingController@getSkills');
            Route::post('skill/save', 'CompanySettingController@saveSkill');
            Route::post('skill/{skill}/delete', 'CompanySettingController@deleteSkill');

            Route::post('analytics-url', 'CompanySettingController@updateCompanyAnalyticsUrl');

            Route::get('apply-setting', 'CompanySettingController@getCompanyApplySettings');
            Route::post('apply-setting', 'CompanySettingController@storeCompanyApplySettings');
            Route::post('apply-setting/{fieldId}/delete', 'CompanySettingController@deleteCompanyApplySettingsCustomFields');
            Route::post('apply-setting/update', 'CompanySettingController@updateCompanyApplySettings');

            Route::get('templates', 'CompanySettingController@getCompanyTemplates');
            Route::post('templates', 'CompanySettingController@storeCompanyTemplate');
        });


        Route::prefix('question')->group(function () {
            Route::get('groups', 'QuestionController@allGroups');
            Route::get('group/{group}/edit', 'QuestionController@questionGroupData');
            Route::post('group/create', 'QuestionController@saveQuestionGroup');
            Route::post('group/{group}/update', 'QuestionController@updateQuestionGroup');
            Route::get('group/{group}/duplicate', 'QuestionController@duplicateQuestionGroup');
            Route::post('group/{group}/delete', 'QuestionController@deleteQuestionGroup');
        });


        Route::prefix('assessments')->group(function () {
            Route::get('/', 'QuestionController@allAssessments');
            Route::get('/{assessment}', 'QuestionController@assessmentData');
            Route::post('/create', 'QuestionController@saveAssessment');
            Route::post('/{assessment}/update', 'QuestionController@updateAssessmentData');
            Route::post('{assessment}/delete', 'QuestionController@deleteAssessment');
        });
    });
});


Route::post('/sign-in', 'CompanyController@signIn');
Route::post('/sign-up', 'CompanyController@signUp');
Route::post('/create-company', 'CompanyController@createCompany')->middleware('auth.basic');
Route::get('/company-meta-data', 'CompanyController@getCompanyCreateData');

Route::prefix('candidate')->group(function () {
    Route::post('sign-up', 'CandidateController@signUp');
    Route::post('sign-in', 'CandidateController@signIn');
    Route::post('login-social', 'CandidateController@loginSocial');
    Route::get('navigation/{jobId}', 'CandidateController@applicantNavigation');
});

Route::group(['middleware' => ['wp.auth', 'wp.version']], function () {
    Route::get('category/{subcategory?}', 'JobController@searchCategory');
    Route::get('skill', 'JobController@searchSkill');
    Route::get('screening-meta-data', 'JobEvaluationController@jobScreeningQuestionMetaData');
    Route::get('quiz-meta-data', 'JobEvaluationController@jobQuizMetaData');
});

Route::group(['middleware' => ['wp.auth', 'wp.version']], function () {
    Route::get('/state/custom/{country}', 'GlobalSearchController@searchStateForCustom');
    Route::post('/state/{country}', 'GlobalSearchController@saveCompanyState');
    Route::get('/city/custom/{country?}/{state?}', 'GlobalSearchController@searchCityForCustom');
    Route::post('/city/{country?}/{state?}', 'GlobalSearchController@saveCompanyCity');
});

Route::get('/country', 'GlobalSearchController@searchCountry');
Route::get('/state/{country}', 'GlobalSearchController@searchState');
Route::get('/city/{country?}/{state?}', 'GlobalSearchController@searchCity');
Route::get('job/meta', 'GlobalSearchController@jobInfoMetaData');

/*Web hooks*/
Route::post('/webhook/wpdev/subscription', 'AgencyBundleController@subscribe');
