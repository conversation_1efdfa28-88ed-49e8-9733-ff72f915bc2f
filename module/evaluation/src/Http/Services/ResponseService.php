<?php

namespace EasyJobs\Evaluation\Http\Services;

use App\Enums\ResponseStatus;
use App\Models\JobApplicant;
use App\Models\JobPost;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class ResponseService
{
    public function response($array): JsonResponse
    {
        return $this->successResponse($array);
    }

    public function jobPostMessage(JobPost $jobPost)
    {
        if ($jobPost->is_expired) {
            return $this->errorResponse([], __('responses.job.expired', ['job' => 'Job Post']));
        }
    }

    public function applicantQuizFinishedMessage(JobApplicant $applicant)
    {
        if (!$applicant->quiz_started_at) {
            return $this->errorResponse([], __('responses.job.quiz.not_started'));
        }

        if ($applicant->quiz_ends_at) {
            return $this->errorResponse([], __('responses.job.quiz.completed'));
        }
    }

    public function applicantQuizStartedMessage(JobApplicant $applicant): JsonResponse
    {
        if ($applicant->quiz_ends_at) {
            return $this->errorResponse([], __('responses.job.quiz.completed'));
        }

        return $this->errorResponse([], __('responses.job.quiz.already_started'));
    }

    /**
     * Response Common Error Message
     * @return JsonResponse
     */
    function commonErrorResponseMessage(): JsonResponse
    {
        return $this->errorResponse([], __('responses.something_went_wrong'));
    }

    /**
     * @param string $criteria
     * @return JsonResponse
     */
    public function sendErrorResponseForAssessment(string $criteria): JsonResponse
    {
        $responses = [
            'started_at' => $this->errorResponse([], __('responses.assessment.candidate.already_started')),
            'is_finished' => $this->errorResponse([], __('responses.assessment.candidate.completed')),
            'is_time_over' => $this->errorResponse([], __('responses.assessment.candidate.time_over')),
            'hasRunningAssessment' => $this->errorResponse([], __('responses.assessment.candidate.other_assessment_running')),
            'is_not_started' => $this->errorResponse([], __('responses.assessment.candidate.not_started')),
        ];

        return $responses[$criteria] ?? $this->commonErrorResponseMessage();
    }

    /**
     * @param string $criteria
     * @param $applicant
     * @return JsonResponse
     */
    public function sendErrorResponseForApplicantQuiz(string $criteria, $applicant): JsonResponse
    {
        $responses = [
            'isApplicantQuizFinish' => $this->applicantQuizFinishedMessage($applicant),
            'isApplicantQuizTimeOver' => $this->errorResponse([], __('responses.job.quiz.time_over')),
            'isApplicantQuizAlreadyStarted' => $this->applicantQuizStartedMessage($applicant),
            'otherQuizRunning' => $this->errorResponse([], __('responses.job.quiz.other_quiz_running'))
        ];

        return $responses[$criteria] ?? $this->commonErrorResponseMessage();
    }

    /**
     * @param array $data
     * @param string $message
     * @param int $responseCode
     * @return JsonResponse
     */
    public function successResponse(array $data, string $message = '', int $responseCode = ResponseAlias::HTTP_OK): JsonResponse
    {
        return response()->json([
            'status' => ResponseStatus::SUCCESS,
            'data' => $data,
            'message' => $message
        ], $responseCode);
    }


    /**
     * @param array $data
     * @param String $message
     * @param int $responseCode
     * @return JsonResponse
     */
    public function errorResponse(array $data, string $message, int $responseCode = ResponseAlias::HTTP_BAD_REQUEST): JsonResponse
    {
        return response()->json([
            'status' => ResponseStatus::FAILED,
            'data' => $data,
            'message' => $message,
        ], $responseCode);
    }

}