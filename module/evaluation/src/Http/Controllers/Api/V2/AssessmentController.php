<?php

namespace EasyJobs\Evaluation\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\HelperService;
use App\Services\NotificationService;
use EasyJobs\Evaluation\Enums\ApplicantAssessmentStatus;
use EasyJobs\Evaluation\Http\Resources\ApplicantAssessmentResource;
use EasyJobs\Evaluation\Http\Services\{AssessmentService, QuestionAnswerService, QuestionService, ResponseService};
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AssessmentController extends Controller
{
    private AssessmentService $assessmentService;
    private QuestionService $questionService;
    private ResponseService $responseService;
    private QuestionAnswerService $questionAnswerService;

    /**
     * @param AssessmentService $assessmentService
     * @param QuestionService $questionService
     * @param ResponseService $responseService
     * @param QuestionAnswerService $questionAnswerService
     */
    public function __construct(
        AssessmentService     $assessmentService,
        QuestionService       $questionService,
        ResponseService       $responseService,
        QuestionAnswerService $questionAnswerService
    )
    {
        $this->assessmentService = $assessmentService;
        $this->questionService = $questionService;
        $this->responseService = $responseService;
        $this->questionAnswerService = $questionAnswerService;
    }

    /**
     * Get Applicant Assessment Details By Assessment Id
     * @param Request $request
     * @param int $assessmentId
     * @return JsonResponse
     */
    public function getAssessmentDetails(Request $request, int $assessmentId)
    {
        try {
            $assessment = $this->assessmentService->getApplicantAssessmentById(auth()->user()->id, $assessmentId);

            $mismatchCriteria = $this->assessmentService->setSelectedCriteria(['is_finished', 'is_time_over', 'hasRunningAssessment'])
                ->findMismatchedCriteria($assessment);

            if (!blank($mismatchCriteria)) {
                return $this->responseService->sendErrorResponseForAssessment($mismatchCriteria);
            }

            if ($assessment->is_not_started) {
                return $this->responseService->successResponse((new ApplicantAssessmentResource($assessment))->toArray($request));
            }

            return $this->responseService->successResponse([
                'currentQuestionId' => $assessment->last_question_id,
            ]);

        } catch (Exception $e) {
            Log::error($e);
            return $this->responseService->commonErrorResponseMessage();
        }
    }

    /**
     * Start Applicant Assessment
     * Fire An Job Event To Complete Unfinished Assessment
     * @param int $assessmentId
     * @return JsonResponse
     */
    public function startAssessment(int $assessmentId)
    {
        try {
            $assessment = $this->assessmentService->getApplicantAssessmentById(auth()->user()->id, $assessmentId);

            $mismatchCriteria = $this->assessmentService->setSelectedCriteria(['started_at', 'is_finished', 'is_time_over', 'hasRunningAssessment'])
                ->findMismatchedCriteria($assessment);

            if (!blank($mismatchCriteria)) {
                return $this->responseService->sendErrorResponseForAssessment($mismatchCriteria);
            }

            $question = $this->questionService->getQuestionByOrder($assessment?->applicant?->company_id, $assessment->question_group_id, 1);
            if (blank($question)) {
                return $this->responseService->commonErrorResponseMessage();
            }

            // Start Applicant Assessment
            $this->assessmentService->updateApplicantAssessment($assessment, [
                'status' => ApplicantAssessmentStatus::IN_PROGRESS,
                'started_at' => now(),
                'last_question_id' => $question->id,
            ]);

            // Fire An Job Event To Complete Unfinished Assessment
            app(AssessmentService::class)->completeApplicantAssessment($assessment);

            return $this->responseService->successResponse([
                'currentQuestionId' => $question->id,
            ]);
        } catch (Exception $e) {
            Log::error($e);
            return $this->responseService->commonErrorResponseMessage();
        }
    }

    /**
     * Get Assessment Question By Question ID
     * @param int $assessmentId
     * @param int $questionId
     * @return JsonResponse
     */
    public function getQuestion(int $assessmentId, int $questionId)
    {
        try {
            $assessment = $this->assessmentService->getApplicantAssessmentById(auth()->user()->id, $assessmentId);

            $mismatchCriteria = $this->assessmentService->setSelectedCriteria(['is_finished', 'is_time_over'])
                ->findMismatchedCriteria($assessment);

            if (!blank($mismatchCriteria)) {
                return $this->responseService->sendErrorResponseForAssessment($mismatchCriteria);
            }

            $question = $this->questionService->getQuestionById($assessment->applicant->company_id, $assessment->question_group_id, $questionId);

            $exitsQuestionAnswers = $this->questionAnswerService->existsAnswer([
                'question_id' => $question->id,
                'user_id' => auth()->user()->id,
                'job_applicant_id' => $assessment->job_applicant_id,
            ]);

            if ($exitsQuestionAnswers) {
                return $this->responseService->commonErrorResponseMessage();
            }

            // Hide question answer from meta answer
            $question = $question->toArray();
            unset($question['meta']['answers']);

            $nextQuestion = $this->questionService->getQuestionByOrder($assessment->applicant->company_id, $assessment->question_group_id, $question['order'] + 1);

            $questions = $this->questionService->getQuestionsByQuestionGroup($assessment->question_group_id);

            return $this->responseService->successResponse([
                'question' => $question,
                'question_id' => $nextQuestion?->id,
                'totalQuestionCount' => $questions->count()
            ]);
        } catch (Exception $e) {
            Log::error($e);
            return $this->responseService->commonErrorResponseMessage();
        }
    }


    /**
     * Get Applicant Assessment Left Time
     * @param int $assessmentId
     * @return JsonResponse
     */
    public function getTime(int $assessmentId)
    {
        try {
            $assessment = $this->assessmentService->getApplicantAssessmentById(auth()->user()->id, $assessmentId);

            $mismatchCriteria = $this->assessmentService->setSelectedCriteria(['is_finished', 'is_time_over'])
                ->findMismatchedCriteria($assessment);

            if (!blank($mismatchCriteria)) {
                return $this->responseService->sendErrorResponseForAssessment($mismatchCriteria);
            }

            // Get Assessment Left Time in duration
            $durationLeftSeconds = $this->assessmentService->durationLeft($assessment);

            return $this->responseService->successResponse([
                'time' => __("Time left ") . gmdate("H:i:s", $durationLeftSeconds),
                'timeSecond' => $durationLeftSeconds
            ]);
        } catch (Exception $e) {
            Log::error($e);
            return $this->responseService->commonErrorResponseMessage();
        }
    }

    /**
     * Submit Applicant Assessment Answer By Question
     * Runtime Applicant Assessment Mark Calculate
     * Send Notification Manager When Applicant Completed Assessment
     * @param Request $request
     * @param int $assessmentId
     * @return JsonResponse
     */
    public function submitAnswer(Request $request, int $assessmentId)
    {
        try {
            $assessment = $this->assessmentService->getApplicantAssessmentById(auth()->user()->id, $assessmentId);

            $mismatchCriteria = $this->assessmentService->setSelectedCriteria(['is_not_started', 'is_finished', 'is_time_over', 'hasRunningAssessment'])
                ->findMismatchedCriteria($assessment);

            if (!blank($mismatchCriteria)) {
                return $this->responseService->sendErrorResponseForAssessment($mismatchCriteria);
            }

            $question = $this->questionService->getQuestionById($assessment->applicant->company_id, $assessment->question_group_id, $request->get('questionId'));

            // Save Assessment Question Answer
            $this->questionAnswerService->createAssessmentAnswer($request, $question, $assessment, auth()->user()->id);

            /**
             * Questions with applicant answer after joining answer
             * Also contain answer is correct or not
             * Update Assessment marks obtain
             */
            $questionWithAnswers = HelperService::getQuestionsWithAnswers($assessment->applicant, $assessment->questionGroup);
            $this->assessmentService->updateApplicantAssessment($assessment, [
                'marks_obtain' => floor($questionWithAnswers->sum('marks')),
                'status' => ApplicantAssessmentStatus::IN_PROGRESS
            ]);

            // If Next Question Not exits Then assessment Complete
            // Send Assessment complete notification to manager
            $nextQuestion = $this->questionService->getQuestionByOrder($assessment->applicant->company_id, $assessment->question_group_id, $question->order + 1);
            if (blank($nextQuestion)) {
                $this->assessmentService->updateApplicantAssessment($assessment, [
                    'ends_at' => now(),
                    'status' => ApplicantAssessmentStatus::COMPLETE
                ]);

                $ns = new NotificationService($assessment->job->company);
                $ns->assessmentCompleteNotificationToManager($assessment);

                return $this->responseService->successResponse([
                    'finished' => true,
                ]);
            }

            $this->assessmentService->updateApplicantAssessment($assessment, [
                'last_question_id' => $nextQuestion->id,
            ]);

            return $this->responseService->successResponse([
                'last_question_id' => $nextQuestion->id,
            ]);
        } catch (Exception $e) {
            Log::error($e);
            return $this->responseService->commonErrorResponseMessage();
        }
    }
}
