<?php

namespace EasyJobs\SlackNotificationBuilder\Element;

class Button implements IElement
{
    protected string $type = "plain_text";
    protected string $buttonText = "Click Me";
    protected bool $emoji = false;
    protected ?string $value = null;
    protected ?string $style = null;
    protected string $url;
    protected ?string $actionId = null;

    /**
     * @param string $type
     */
    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string|null $value
     */
    public function setValue(?string $value): self
    {
        $this->value = $value;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getValue(): ?string
    {
        return $this->value;
    }

    /**
     * @param string|null $style
     */
    public function setStyle(?string $style): self
    {
        $this->style = $style;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStyle(): ?string
    {
        return $this->style;
    }

    /**
     * @param string $buttonText
     */
    public function setButtonText(string $buttonText): self
    {
        $this->buttonText = $buttonText;
        return $this;
    }

    /**
     * @return string
     */
    public function getButtonText(): string
    {
        return $this->buttonText;
    }

    /**
     * @param bool $emoji
     */
    public function setEmoji(bool $emoji): self
    {
        $this->emoji = $emoji;
        return $this;
    }

    public function getEmoji(): bool
    {
        return $this->emoji;
    }

    /**
     * @param string $url
     */
    public function setUrl(string $url): self
    {
        $this->url = $url;
        return $this;
    }

    /**
     * @return string
     */
    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @param string|null $actionId
     */
    public function setActionId(?string $actionId): self
    {
        $this->actionId = $actionId;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getActionId(): ?string
    {
        return $this->actionId;
    }

    public function build(): array
    {
        $data = [
            "type" => "button",
            "text" => [
                "type" => $this->getType(),
                "text" => $this->getButtonText(),
                "emoji" => $this->getEmoji()
            ],
            "url" => $this->getUrl(),
        ];

        if ($this->value) $data['value'] = $this->getValue();
        if ($this->style) $data['style'] = $this->getStyle();
        if ($this->actionId) $data['action_id'] = $this->getActionId();

        return $data;
    }
}
