<?php

namespace App\Events;

use App\Enums\PackageType;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserRegistered
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public $user;
    public $company;
    public $userType;
    public string $verificationLink;

    public function __construct(User $user, $company = null, $verificationLink = '', $userType = PackageType::REGULAR)
    {
        $this->user = $user;
        $this->userType = $userType;
        $this->company = $company;
        $this->verificationLink = $verificationLink;
    }
}
