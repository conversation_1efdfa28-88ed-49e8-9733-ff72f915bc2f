<?php

namespace App\Facades;

use Illuminate\Routing\UrlGenerator as BaseUrlGenerator;

class UrlGenerator extends BaseUrlGenerator
{
    public function route($name, $parameters = [], $absolute = true): string
    {
        if (is_array($parameters)) {
            $parameters['domain'] = $parameters['domain'] ?? $this->request->getHost();
        }

        $baseRoute = parent::route($name, $parameters, $absolute); // TODO: Change the autogenerated stub

        // domain params
        $baseRoute = $this->removeQueryParam($baseRoute, 'domain');


        // Split the base route into path and query string parts
//        [$routePath, $queryString] = array_pad(explode('?', $baseRoute, 2), 2, null);


//        $subdomains = explode('.', $routePath);
//        if (count($subdomains) === 4) {
//            unset($subdomains[1]); // Remove the second subdomain
//            $routePath = implode('.', $subdomains);
//        }

        // Rebuild the route with the modified path and query string
//        $baseRoute = $routePath . ($queryString ? "?$queryString" : '');

        // Replace any occurrence of 'app.app.' with 'app.'
        return str_replace('app.app.', 'app.', $baseRoute);
    }

    private function removeQueryParam($url, $param): string
    {
        $parsedUrl = parse_url($url);
        parse_str($parsedUrl['query'] ?? '', $query);
        unset($query[$param]);

        return ($parsedUrl['scheme'] ?? 'https') . '://' .
            ($parsedUrl['host'] ?? '') .
            ($parsedUrl['path'] ?? '') .
            ($query ? '?' . http_build_query($query) : '');
    }
}
