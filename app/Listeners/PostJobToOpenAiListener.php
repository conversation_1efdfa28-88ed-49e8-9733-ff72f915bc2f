<?php

namespace App\Listeners;

use App\Enums\JobStatus;
use App\Events\JobPostUpdate;
use App\Services\CompanySettingService;
use App\Services\JobPostService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

class PostJobToOpenAiListener implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, SerializesModels;

    private JobPostService $jobPostService;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(JobPostService $jobPostService)
    {
        $this->jobPostService = $jobPostService;
    }

    /**
     * Handle the event.
     *
     * @param  JobPostUpdate  $event
     * @return void
     */
    public function handle(JobPostUpdate $event): void
    {
        $openAi = app(CompanySettingService::class)->getCompanySetting($event->company, CompanySettingService::OPENAI);
        $appKey = data_get($openAi, 'config.app_key', null);

        $headers = [];

        if ($appKey) {
            $headers['x-open-api-key'] = $appKey;
        }

        if (! in_array($event->meta['status'], [JobStatus::PUBLISHED, JobStatus::REPUBLISHED])) return;

        try {
            Http::withToken(config('easyjob.resume_parser.open_ai_token'))
                ->withHeaders($headers)
                ->asForm()
                ->post(config('easyjob.resume_parser.open_ai_host') . '/v1/job_description', [
                    'job_id' => $event->jobPost->id,
                    'job_description' => $this->jobPostService->getJobCustomDescription($event->jobPost->id)
                ]);
        } catch (Throwable $th) {
            Log::error($th);
        }
    }
}
