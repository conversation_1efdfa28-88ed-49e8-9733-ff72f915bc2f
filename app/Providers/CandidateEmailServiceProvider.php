<?php

namespace App\Providers;

use Illuminate\Mail\Mailer;
use Illuminate\Support\Arr;
use Illuminate\Support\ServiceProvider;
use Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportFactory;
use Symfony\Component\Mailer\Transport\Dsn;

class CandidateEmailServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('candidate.smtp.mailer', function ($app, $parameters) {
            $smtpHost = Arr::get($parameters, 'host');
            $smtpPort = Arr::get($parameters, 'port');
            $smtpUsername = Arr::get($parameters, 'username');
            $smtpPassword = Arr::get($parameters, 'password');
            $smtpEncryption = Arr::get($parameters, 'encryption');
            $fromEmail = Arr::get($parameters, 'from_email');
            $fromName = Arr::get($parameters, 'from_name');
            $replyToEmail = Arr::get($parameters, 'from_email');
            $replyToName = Arr::get($parameters, 'from_name');

            $transportFactory = new EsmtpTransportFactory();
            $transport = $transportFactory->create(new Dsn('smtp', $smtpHost, $smtpUsername, $smtpPassword, $smtpPort));

            $mailer = new Mailer('candidate-smtp-mailer', $app->get('view'), $transport, $app->get('events'));
            $mailer->alwaysFrom($fromEmail, $fromName);
            $mailer->alwaysReplyTo($replyToEmail, $replyToName);
            $mailer->setQueue($app['queue']);
            return $mailer;
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
