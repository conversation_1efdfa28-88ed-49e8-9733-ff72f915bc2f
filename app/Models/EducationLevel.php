<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EducationLevel extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'short_name',
        'name',
        'company_id'
    ];

    public $timestamps = false;

    public function degrees()
    {
        return $this->hasMany(Degree::class);
    }

    public function educations()
    {
        return $this->hasMany(Education::class, 'level_id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
