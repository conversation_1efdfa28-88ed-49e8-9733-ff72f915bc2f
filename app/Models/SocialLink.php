<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SocialLink extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'social_profile_id',
        'link'
    ];

    public $timestamps = false;

    /**
     *  Get social profile
     */

    public function socialProfile()
    {
        return $this->belongsTo(SocialProfile::class);
    }
}
