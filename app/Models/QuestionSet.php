<?php

namespace App\Models;

use App\Enums\ExamType;
use App\Traits\LastUpdated;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QuestionSet extends Model
{
    use SoftDeletes;
    use LastUpdated;

    protected $table = "question_sets";

    protected $fillable = ['name', 'note', 'internal_note', 'company_id', 'exam_type', 'exam_duration', 'marks_per_question', 'created_by', 'updated_by', 'updated_at'];

    public function questions()
    {
        return $this->hasMany(QuestionSetQuestion::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'updated_by', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function getQuestionTypeNameAttribute()
    {
        if ($this->exam_type) {
            return getEnumValue(ExamType::class, $this->exam_type, true);
        }
    }
}
