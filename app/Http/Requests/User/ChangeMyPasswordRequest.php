<?php

namespace App\Http\Requests\User;

use App\Http\Requests\BaseRequest;

class ChangeMyPasswordRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'old_password' => ['required', 'min:6'],
            'new_password' => ['required', 'min:8'],
            'confirm_password' => ['required', 'min:8', 'same:new_password'],
        ];
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        return [
            'old_password.required' => __('validation.attributes.context_please_enter', ['context' => 'current password']),
            'old_password.min' => __('validation.attributes.context_please_enter', ['context' => 'a valid password']),
            'new_password.required' => __('validation.attributes.context_please_enter', ['context' => 'new password']),
            'confirm_password.required' => __('validation.attributes.context_please_enter', ['context' => 'confirm password']),
            'new_password.min' => __('validation.attributes.context_should_be_character_long', ['context' => 'new password', 'number' => 8]),
            'confirm_password.min' => __('validation.attributes.context_should_be_character_long', ['context' => 'confirm password', 'number' => 8]),
            'confirm_password.same' => __('validation.attributes.password_didnt_match')
        ];
    }
}
