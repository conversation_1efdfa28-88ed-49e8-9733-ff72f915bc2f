<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class HackerRankAssessmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return $this->getValidationRules();
    }

    private function getValidationRules(): array
    {
        if ($this->routeIs('v2.hackerrank.candidate.invite')) {
            return [
                'testId' => 'required',
                'candidates' => 'required|array',
                'expireDate' => 'required|date',
            ];
        }

        return [
            'testId' => 'required',
            'candidateId' => 'required',
            'expireDate' => 'required|date',
        ];

    }
}
