<?php

namespace App\Http\Requests\Company;

use App\Http\Requests\BaseRequest;

class ConversationTemplateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'templates' => 'required',
        ];
    }
}
