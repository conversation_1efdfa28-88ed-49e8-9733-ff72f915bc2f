<?php

namespace App\Http\Requests\Evaluation;

use App\Http\Requests\BaseRequest;

class QuestionAssessmentRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function skips()
    {
        return [
            'note',
            'questions.*',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'assessment_name' => 'required|max:100',
            'set_type' => 'required',
            'exam_duration' => "required|numeric",
            'marks_per_question' => "required|numeric",
            'note' => 'nullable|string|max:5000',
            'internal_note' => 'nullable|string|max:250',
            'questions' => 'required|array',
            'questions.*.title' => 'string|required|max:250',
            'questions.*.options' => 'required|array',
        ];
    }

    public function messages()
    {
        return [
            'required' => __('validation.attributes.required'),
            'required_if' => __('validation.attributes.required'),
            'array' => __('validation.attributes.array'),
            'string' => __('validation.attributes.string'),
            'assessment_name.required' => __('validation.attributes.context_please_provide', ['context' => 'assessment name']),
            'assessment_name.max' => __('validation.attributes.context_provide_within_number_characters', ['context' => 'assessment name', 'number' => 100]),
            'questions.*.title.required' => __('validation.attributes.context_please_provide', ['context' => 'question title']),
            'questions.*.title.string' => __('validation.attributes.context_please_provide', ['context' => 'question title']),
            'questions.*.title.max' => __('validation.attributes.context_provide_within_number_characters', ['context' => 'question title', 'number' => 250]),
            'internal_note.max' => __('validation.attributes.context_provide_within_number_characters', ['context' => 'description', 'number' => 250]),
            'note.max' => __('validation.attributes.context_provide_within_number_characters', ['context' => 'instruction', 'number' => 5000]),
        ];
    }
}
