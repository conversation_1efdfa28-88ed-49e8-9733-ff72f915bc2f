<?php

namespace App\Http\Controllers\Api\WpV1;

use App\Enums\CustomResponseCode;
use App\Enums\ExamType;
use App\Enums\QuestionType;
use App\Enums\UserPermission;
use App\Http\Controllers\Api\WpV1\ApiController;
use App\Http\Requests\Evaluation\QuestionAssessmentRequest;
use App\Http\Requests\Evaluation\QuestionSetRequest;
use App\Models\ApplicantAssessment;
use App\Models\JobApplicant;
use App\Models\Question;
use App\Models\QuestionAnswer;
use App\Models\QuestionGroup;
use App\Models\QuestionSet;
use App\Models\QuestionSetQuestion;
use Carbon\Carbon;
use Illuminate\Http\Request;

class QuestionController extends ApiController
{

    const QUESTION_TYPES = [
        [
            'id' => QuestionType::SHORT_ANSWER,
            'name' => "Text",
        ],
        [
            'id' => QuestionType::MULTIPLE_CHOICE,
            'name' => "Multiple Choice",
        ]
    ];

    const SET_TYPES = [
        ["id" => ExamType::SCREENING, "name" => "Screening Test"],
        ["id" => ExamType::QUIZ, "name" => "Quiz Test"]
    ];

    const SORT_BY_NAME = 'name';
    const SORT_BY_TYPE = 'type';
    const SORT_BY_CREATED = 'created';
    const SORT_BY_UPDATED = 'updated';
    const SORT_BY_QUESTION = 'question';

    public function allGroups(Request $request)
    {
        $getQuizType = [];
        $getQuizType[] = ExamType::QUIZ;
        $getQuizType[] = ExamType::SCREENING;
        $company = auth('app')->user();

        $questionGroup = QuestionSet::with('user')->withCount('questions')->where('company_id', $company->id)
            ->whereIn('exam_type', $getQuizType)
            ->where(function ($query) use ($request) {
                $name = $request->get('name');
                if ($name) {
                    $query->where('name', 'like', "%{$name}%");
                }
            });

        $sortBy = $request->get('sort_by');
        $orderBy = $request->get('order_by', 'DESC');
        $questionGroup = $this->questionGroupSort($questionGroup, $sortBy, $orderBy);

        $questionGroup = $questionGroup->get()->map(function ($group) {
            return $this->questionGroupSerializer($group);
        });

        return $this->sendSuccessResponse($questionGroup);
    }

    private function questionGroupSerializer($group)
    {
        $data = $group->only('id', 'name', 'company_id');

        $data['exam_type'] = [
            'id' => $group->exam_type,
            'name' => \Str::title(getEnumValue(ExamType::class, $group->exam_type, true))
        ];

        $data['total_questions'] = $group->questions->count();
        $data['last_update'] = $group->updated_at->format('d M, Y');
        $data['updated_by'] = optional($group->user)->name;
        $data['created_by'] = optional($group->createdBy)->name;
        return $data;
    }

    public function questionGroupData($group)
    {
        $getQuizType = [];
        $getQuizType[] = ExamType::QUIZ;
        $getQuizType[] = ExamType::SCREENING;
        $company = auth('app')->user();

        $group = QuestionSet::whereIn('exam_type', $getQuizType)->find($group);
        if (blank($group) || $group->company_id !== $company->id) {
            return $this->sendFailedResponse('unauthorized_access');
        }

        $groupData = [
            'id' => $group->id,
            'set_type' => [
                'id' => $group->exam_type,
                'name' => \Str::title(getEnumValue(ExamType::class, $group->exam_type, true))
            ],
            'set_name' => $group->name,
            'note' => $group->note,
            'internal_note' => $group->internal_note,
            'exam_duration' => $group->exam_duration,
            'marks_per_question' => $group->marks_per_question,
            'questions' => $this->serializeQuestionData($group->questions()->orderBy('order', 'asc')->orderBy('id', 'asc')->get())
        ];

        return $this->sendSuccessResponse($groupData);
    }

    private function serializeQuestionData($questions)
    {
        $serializedQuestions = [];

        if ($questions) {
            foreach ($questions as $question) {
                $questionData = [
                    'id' => $question->id,
                    'type' => [
                        'id' => $question->type,
                        'name' => 'Text',
                    ],
                    'title' => $question->name,
                    'options' => [],
                    'answers' => [],
                    'isValid' => false,
                    'errors' => [],
                    'isMultiple' => ($question->type == QuestionType::MULTIPLE_CHOICE)
                ];

                if ($question->type == QuestionType::MULTIPLE_CHOICE) {
                    $options = data_get($question, 'meta.options', []);
                    $answers = data_get($question, 'meta.answers');
                    $questionData['answers'] = $answers;
                    $questionData['type']['name'] = 'Multiple Choice';
                    foreach ($options as $key => $option) {
                        $questionData['options'][] = [
                            'id' => $key,
                            'title' => $option,
                        ];
                    }
                }
                $serializedQuestions[] = $questionData;
            }
        }

        return $serializedQuestions;
    }

    public function saveQuestionGroup(QuestionSetRequest $request)
    {
        $setType = $request->get('set_type')['id'];
        $company = auth('app')->user();
        $user = auth('app')->user()->creator;

        $setData = [];
        $setData['name'] = $request->get('set_name');
        $setData['note'] = $request->get('note');
        $setData['internal_note'] = $request->input('internal_note');
        $setData['exam_type'] = $setType;
        $setData['company_id'] = $company->id;
        $setData['updated_by'] = $user->id;

        $questions = $request->get('questions', []);
        if ($setType == ExamType::QUIZ) {
            $setData['exam_duration'] = $request->get('exam_duration');
            $setData['marks_per_question'] = $request->get('marks_per_question');
            $questionData = $this->transformQuestionsObject($questions, $company->id, ExamType::QUIZ);
        } elseif ($setType == ExamType::SCREENING) {
            $questionData = $this->transformQuestionsObject($questions, $company->id, ExamType::SCREENING);
        }

        \DB::beginTransaction();

        try {
            $questionSet = QuestionSet::create($setData)->questions()->saveMany($questionData);
            \DB::commit();

            return $this->sendSuccessResponse([], __('responses.saved'));
        } catch (\Exception $exception) {
            \Log::error($exception);
            \DB::rollBack();
            return $this->sendFailedResponse('Question set can not be saved', []);
        }
    }

    public function updateQuestionGroup(QuestionSetRequest $request, $groupId)
    {
        $company = auth('app')->user();
        $user = auth('app')->user()->creator;

        $questionSet = QuestionSet::find($groupId);
        if (blank($questionSet) || $questionSet->company_id !== $company->id) {
            return $this->sendFailedResponse('unauthorized_access');
        }

        \DB::beginTransaction();

        $questions = collect($request->get('questions'))->map(function($question) {
            if (! array_key_exists('id', $question)) {
                $question['id'] = null;
            }
            return $question;
        })->toArray();

        try {
//            return response()->json([
//                'data' => $request->all()
//            ]);

            $setType = $request->get('set_type')['id'];
            $setData = [];
            $setData['name'] = $request->get('set_name');
            $setData['note'] = $request->get('note');
            $setData['internal_note'] = $request->input('internal_note');
            $setData['exam_type'] = $setType;
            $setData['company_id'] = $company->id;
            $setData['updated_by'] = $user->id;
            $setData['updated_at'] = Carbon::now();

            $questions = array_map(function ($question, $index) {
                $question['order'] = $index + 1;
                return $question;
            }, $questions , array_keys($questions));


            $newIds = array_column($questions, 'id');
            $oldIds = $questionSet->questions->pluck('id')->toArray();
            $deletedIds = array_values(array_diff($oldIds, $newIds));

            if (count($deletedIds) > 0) {
                $questionSet->questions()->whereIn('id', $deletedIds)->delete();
            }

            if ($setType == ExamType::QUIZ) {
                $setData['exam_duration'] = $request->get('exam_duration');
                $setData['marks_per_question'] = $request->get('marks_per_question');
                $this->updateQuestionsObject($questionSet, $questions, $company->id, ExamType::QUIZ);
            } elseif ($setType == ExamType::SCREENING) {
                $this->updateQuestionsObject($questionSet, $questions, $company->id, ExamType::SCREENING);
            }

            $questionSet->fill($setData)->save();

            \DB::commit();
            return $this->sendSuccessResponse([], 'Saved');
        } catch (\Exception $exception) {
            \Log::error($exception);
            \DB::rollBack();
            return $this->sendFailedResponse($exception->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public function duplicateQuestionGroup(Request $request, $groupId)
    {
        $company = auth('app')->user();
        $user = auth('app')->user()->creator;
        $questionSet = QuestionSet::find($groupId);
        if (blank($questionSet) || $questionSet->company_id !== $company->id) {
            return $this->sendFailedResponse('Unauthorized access');
        }

        \DB::beginTransaction();

        try {
            $newQuestionSet = $questionSet->replicate();
            $newQuestionSet->company_id = $company->id;
            $newQuestionSet->updated_by = $user->id;
            $newQuestionSet->created_at = Carbon::now();
            $newQuestionSet->updated_at = Carbon::now();
            $newQuestionSet->save();

            $questionSet->questions->each(function ($question) use ($newQuestionSet) {
                $replicate = $question->replicate()->fill([
                    'question_set_id' => $newQuestionSet->id
                ]);
                $replicate->save();
            });

            \DB::commit();
            return $this->sendSuccessResponse($newQuestionSet, __('responses.context_duplicated', ['context' => 'Question set']));
        } catch (\Exception $exception) {
            \Log::error($exception);
            \DB::rollBack();
            return $this->sendFailedResponse('Question set can not be saved');
        }
    }

    public function deleteQuestionGroup($id)
    {
        $getQuizType = [];
        $getQuizType[] = ExamType::QUIZ;
        $getQuizType[] = ExamType::SCREENING;
        $company = auth('app')->user();

        $group = QuestionSet::whereIn('exam_type', $getQuizType)->find($id);
        if (blank($group) || $group->company_id !== $company->id) {
            return $this->sendFailedResponse('Unauthorized access');
        }

        \DB::beginTransaction();
        try {
            $group->questions()->delete();
            $group->delete();
            \DB::commit();
            return $this->sendSuccessResponse([], __('responses.deleted'));
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->sendFailedResponse('Question set can not be deleted');
        }
    }

    public function deleteAssessment($id)
    {
        $company = auth('app')->user();
        $group = QuestionGroup::find($id);
        if (blank($group) || $group->company_id !== $company->id) {
            return $this->sendFailedResponse(__('responses.unauthorized_access'));
        }

        \DB::beginTransaction();
        try {
            $questions = $group->questions->pluck('id');
            QuestionAnswer::whereIn('question_id', $questions)->delete();
            ApplicantAssessment::where('question_group_id', $group->id)->delete();
            $group->questions()->delete();
            $group->delete();
            \DB::commit();
            return $this->sendSuccessResponse([], __('responses.context_deleted', ['context' => 'Assessment']));
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->sendFailedResponse(__('responses.something_went_wrong'));
        }
    }

    public function allAssessments(Request $request)
    {
        $applicants = $request->get('applicants', []);
        $company = auth('app')->user();

        $alreadyAssignedAssessments = [];
        if (count($applicants) === 1) {
            $applicant = JobApplicant::findByGeneratedId($applicants[0]);
            $alreadyAssignedAssessments = $applicant->assessments()->pluck('question_group_id');
        }

        $query = QuestionGroup::with('user')->withCount('questions')->where('company_id', $company->id)
            ->where('exam_type', ExamType::ASSESSMENT)
            ->where(function ($query) use ($request) {
                $name = $request->get('name');
                if ($name) {
                    $query->where('name', 'like', "%{$name}%");
                }
            });
        if (!blank($alreadyAssignedAssessments)) {
            $query->whereNotIn('id', $alreadyAssignedAssessments);
        }

        $sortBy = $request->get('sort_by');
        $orderBy = $request->get('order_by', 'DESC');
        $query = $this->questionGroupSort($query, $sortBy, $orderBy);
        $questionGroup = $query->get()
            ->map(function ($group) {
                return $this->assessmentSerializer($group);
            });

        return $this->sendSuccessResponse($questionGroup);

    }

    public function updateAssessmentData(QuestionAssessmentRequest $request, $assessment)
    {
        $company = auth('app')->user();
        $user = auth('app')->user()->creator;
        $questionSet = QuestionGroup::find($assessment);

        if (blank($questionSet) || $questionSet->company_id !== $company->id) {
            return $this->sendFailedResponse(__('responses.unauthorized_access'));
        }

        $hasAttendedInAssessments = ApplicantAssessment::where('question_group_id', $questionSet->id)->whereNotNull('started_at')->count();
        if ($hasAttendedInAssessments > 0) {
            return $this->sendFailedResponse(__('responses.assessment.cant_update_assessment'));
        }

        $assessmentName = $request->assessment_name;
        if ($assessmentName !== $questionSet->name && $this->isDuplicateName($assessmentName)) {
            return $this->sendFailedResponse(__('responses.assessment.already_exits'));
        }


        \DB::beginTransaction();

        $questions = collect($request->get('questions'))->map(function($question) {
            if (! array_key_exists('id', $question)) {
                $question['id'] = null;
            }
            return $question;
        })->toArray();

        try {
            $setData = [];
            $setData['name'] = $request->get('assessment_name');
            $setData['note'] = $request->get('note');
            $setData['internal_note'] = $request->input('internal_note');
            $setData['exam_type'] = ExamType::ASSESSMENT;
            $setData['company_id'] = $company->id;
            $setData['updated_by'] = $user->id;
            $setData['updated_at'] = Carbon::now();

            $questions = array_map(function ($question, $index) {
                $question['order'] = $index + 1;
                return $question;
            }, $questions , array_keys($questions));


            $newIds = array_column($questions, 'id');
            $oldIds = $questionSet->questions->pluck('id')->toArray();
            $deletedIds = array_values(array_diff($oldIds, $newIds));

            if (count($deletedIds) > 0) {
                $questionSet->questions()->whereIn('id', $deletedIds)->delete();
            }

            $setData['exam_duration'] = $request->get('exam_duration');
            $setData['marks_per_question'] = $request->get('marks_per_question');
            $this->updateAssessmentQuestion($questionSet, $questions);
            $questionSet->fill($setData)->save();

            \DB::commit();
            return $this->sendSuccessResponse([], __('responses.saved'));
        } catch (\Exception $e) {
            \Log::error($e);
            \DB::rollBack();
            return $this->sendFailedResponse(__('responses.context_cannot_be_saved', ['context' => 'Question set']));
        }
    }


    private function assessmentSerializer($group)
    {
        $data = $group->only('id', 'name', 'company_id');
        $data['exam_type'] = [
            'id' => $group->getRawOriginal('exam_type'),
            'name' => \Str::title(getEnumValue(ExamType::class, $group->getRawOriginal('exam_type'), true))
        ];
        $data['total_questions'] = $group->questions->count();
        $data['last_update'] = $group->updated_at->format('d M, Y');
        $data['updated_by'] = optional($group->user)->name;
        $data['created_by'] = optional($group->createdBy)->name;
        return $data;
    }

    public function saveAssessment(QuestionAssessmentRequest $request)
    {
        $company = auth('app')->user();
        $user = auth('app')->user()->creator;
        if ($this->isDuplicateName($request->assessment_name)) {
            return $this->sendFailedResponse(__('responses.assessment.already_exits'));
        }
        $setData = [];
        $setData['name'] = $request->get('assessment_name');
        $setData['note'] = $request->get('note');
        $setData['internal_note'] = $request->input('internal_note');
        $setData['exam_type'] = ExamType::ASSESSMENT;
        $setData['company_id'] = $company->id;
        $setData['updated_by'] = $user->id;
        $setData['exam_duration'] = $request->get('exam_duration');
        $setData['marks_per_question'] = $request->get('marks_per_question');

        $questions = $request->get('questions', []);
        $questionData = $this->transformQuestionsForAssessmeent($questions);
        \DB::beginTransaction();

        try {
            QuestionGroup::create($setData)->questions()->saveMany($questionData);
            \DB::commit();
            return $this->sendSuccessResponse([],'Assessment created');
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            \DB::rollBack();
            return $this->sendFailedResponse($e->getMessage());
        }
    }

    public function assessmentData($id)
    {
        $company = auth('app')->user();

        $assessment = QuestionGroup::where(['id' => $id, 'company_id' => $company->id])->first();

        if (blank($assessment)) {
            return $this->sendFailedResponse('Unauthorized access');
        }

        $assessmentData = [
            'id' => $assessment->id,
            'set_type' => [
                'id' => $assessment->getRawOriginal('exam_type'),
                'name' => \Str::title(getEnumValue(ExamType::class, $assessment->getRawOriginal('exam_type'), true))
            ],
            'assessment_name' => $assessment->name,
            'exam_duration' => $assessment->exam_duration,
            'marks_per_question' => $assessment->marks_per_question,
            'note' => $assessment->note,
            'internal_note' => $assessment->internal_note,
            'questions' => $this->serializeQuestionData($assessment->questions()->orderBy('order', 'asc')->orderBy('id', 'asc')->get())
        ];

        return $this->sendSuccessResponse($assessmentData);


    }

    private function isDuplicateName($name)
    {
        $company = auth('app')->user();
        $assessments = QuestionGroup::where('company_id', $company->id)
            ->where('name', $name)
            ->where('exam_type', ExamType::ASSESSMENT)
            ->get();
        return !blank($assessments);
    }

    private function transformQuestionsForAssessmeent($questions)
    {
        $company = auth('app')->user();
        $data = [];

        foreach ($questions as $index => $question) {
            $answers = data_get($question, 'answers', null);
            $options = array_column(data_get($question, 'options', []), 'title');
            $data[] = new Question([
                'name' => data_get($question, 'title'),
                'type' => QuestionType::MULTIPLE_CHOICE,
                'meta' => [
                    'options' => $options,
                    'answers' => $answers,
                ],
                'company_id' => $company->id,
                'order' => $index + 1
            ]);
        }
        return $data;
    }

    private function transformQuestionsObject($questions, $companyId, $examType)
    {
        $data = [];

        foreach ($questions as $index => $question) {
            $type = data_get($question, 'type.id', QuestionType::MULTIPLE_CHOICE);

            $answers = null;
            $options = [];

            if ($examType == ExamType::SCREENING) {
                $answers = data_get($question, 'answers', []);
            } elseif ($examType == ExamType::QUIZ) {
                $answers = (int) data_get($question, 'answers', null);
            }
            if ($question['options'] && $type == QuestionType::MULTIPLE_CHOICE) {
                $options = array_column(data_get($question, 'options', []), 'title');
            }

            $data[] = new QuestionSetQuestion([
                'title' => data_get($question, 'title'),
                'type' => $type,
                'meta' => [
                    'options' => $options,
                    'answers' => $answers,
                ],
                'order' => $index + 1
            ]);
        }

        return $data;
    }

    private function updateQuestionsObject($questionSet, $questions, $companyId, $examType)
    {
        return array_map(function ($data) use ($companyId, $examType, $questionSet) {
            $type = data_get($data, 'type.id', QuestionType::MULTIPLE_CHOICE);
            $answers = null;
            $options = [];
            if ($examType == ExamType::SCREENING) {
                $answers = data_get($data, 'answers', []);
            } elseif ($examType == ExamType::QUIZ) {
                $answers = (int) data_get($data, 'answers', null);
                $type = QuestionType::MULTIPLE_CHOICE;
            }
            if ($data['options'] && $type == QuestionType::MULTIPLE_CHOICE) {
                $options = array_column(data_get($data, 'options', []), 'title');
            }

            $where = [
                'id' => $data['id'],
            ];

            $questionData = [
                'title' => data_get($data, 'title'),
                'type' => $examType == ExamType::QUIZ ? QuestionType::MULTIPLE_CHOICE : $type,
                'updated_at' => Carbon::now(),
                'meta' => [
                    'options' => $options,
                    'answers' => $answers,
                ],
                'order' => data_get($data, 'order')
            ];

            $questionSet->questions()->updateOrCreate($where, $questionData);

        }, $questions);
    }

    private function updateAssessmentQuestion($questionSet, $questions)
    {
        $company = auth('app')->user();
        return array_map(function ($data) use ($questionSet, $company) {
            $answers = data_get($data, 'answers', 0);
            $options = array_column(data_get($data, 'options', []), 'title');
            $where = [
                'id' => $data['id'],
            ];
            $questionData = [
                'name' => data_get($data, 'title'),
                'type' => QuestionType::MULTIPLE_CHOICE,
                'updated_at' => Carbon::now(),
                'meta' => [
                    'options' => $options,
                    'answers' => $answers,
                ],
                'company_id' => $company->id,
                'order' => data_get($data, 'order')
            ];

            $questionSet->questions()->updateOrCreate($where, $questionData);

        }, $questions);
    }

    private function questionGroupSort($query, $sortBy, $orderBy)
    {
        switch ($sortBy) {
            case self::SORT_BY_NAME:
                $query->orderBy('name', $orderBy);
                break;
            case self::SORT_BY_TYPE:
                $query->orderBy('exam_type', $orderBy);
                break;
            case self::SORT_BY_CREATED:
                $query->orderBy('created_at', $orderBy);
                break;
            case self::SORT_BY_UPDATED:
                $query->orderBy('updated_at', $orderBy);
                break;
            case self::SORT_BY_QUESTION:
                $query->orderBy('questions_count', $orderBy);
                break;
            default;
                $query->orderBy('created_at', $orderBy);
        }
        return $query;
    }

}
