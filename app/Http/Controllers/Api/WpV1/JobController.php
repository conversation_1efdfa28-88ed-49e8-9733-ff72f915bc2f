<?php

namespace App\Http\Controllers\Api\WpV1;

use App\Enums\CustomResponseCode;
use App\Enums\EmploymentType;
use App\Enums\JobApplyStatus;
use App\Enums\JobApplyStep;
use App\Enums\JobStatus;
use App\Enums\JobType;
use App\Enums\NotificationType;
use App\Enums\PackageRule;
use App\Enums\PipelineType;
use App\Enums\SalaryRangeType;
use App\Enums\Status;
use App\Enums\CompanyType as Type;
use App\Events\JobPipelineChanged;
use App\Events\JobPostDelete;
use App\Events\JobPostUpdate;
use App\Exports\JobApplicantExport;
use App\Facades\Validator;
use App\Models\ApplicantAssessment;
use App\Models\Category;
use App\Models\CompanyType;
use App\Models\CustomApplyField;
use App\Models\JobApplicant;
use App\Models\JobPipeline;
use App\Models\JobPost;
use App\Models\JobPostTemplate;
use App\Models\Package;
use App\Models\QuestionAnswer;
use App\Models\Skill;
use App\Models\User;
use App\Services\CareerService;
use App\Services\CategoryService;
use App\Services\CompanyEmailService;
use App\Services\CompanySettingService;
use App\Services\JobPermissionService;
use App\Services\NotificationService;
use App\Services\PackageService;
use App\Services\WpV1\JobPostService;
use App\Transformer\ApplicantTransformer;
use App\Transformer\JobPostTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\JobPostService as JobService;

class JobController extends ApiController
{

    private JobPostService $jobPostService;
    private CareerService $careerService;

    public function __construct(JobPostService $jobPostService, CareerService $careerService)
    {
        $this->jobPostService = $jobPostService;
        $this->careerService = $careerService;

        $this->middleware(function ($request, $next){
            if (!auth('app')->check()) {
                return response()->json([
                    'status' => 'failed',
                    'data'   => [
                        'message' => 'Not logged in',
                    ],
                ], Response::HTTP_UNAUTHORIZED);
            }
            return $next($request);

        })->only(['publishedJobs', 'draftJobs', 'archiveJobs', 'pipelineDetails', 'changePipelineForCandidate']);
    }

    const FILTER_NEW_CANDIDATE = 1;
    const FILTER_RATED_CANDIDATE = 2;
    const FILTER_NON_RATED_CANDIDATE = 3;

    const SORT_BY_EXPERIENCE = "SORT_BY_EXPERIENCE";
    const SORT_BY_SKILL_MATCH = "SORT_BY_SKILL_MATCH";
    const SORT_BY_EDUCATION_MATCH = "SORT_BY_EDUCATION_MATCH";
    const SORT_BY_EXPERIENCE_MATCH = "SORT_BY_EXPERIENCE_MATCH";
    const SORT_BY_TOTAL_AI_SCORE = "SORT_BY_TOTAL_AI_SCORE";
    const SORT_BY_QUIZ_SCORE = "SORT_BY_QUIZ_SCORE";

    public function allList(Request $request)
    {
        $company = auth('app')->user();
        $user = auth('app')->user()->creator;

        $managerUnFollowJobIds = $this->jobPostService->getManagerUnFollowJobIds(data_get($company, 'id'), data_get($user, 'id'));
        $jobs = $this->jobPostService->getAllJobs($company, $request->all(), $user, $managerUnFollowJobIds);

        $jobs->load('applicants.user.media', 'category', 'media');

        $jobsData = $this->jobPostService->jobPostList($jobs, $managerUnFollowJobIds);

        return $this->sendSuccessResponse($jobsData);
    }

    public function publishedJobs(Request $request)
    {
        if (!isWpOlderVersion($request->header('x-plugin-version'), "2.4.5") && $request->boolean('paginate', false)) {
            return $this->publishedJobsWithFilter($request);
        }

        return $this->getPublishedJobs($request);
    }

    private function getPublishedJobs($request)
    {
        $orderColumns = ['title', 'expire_at', 'published_at'];
        $orders = ['asc', 'desc'];
        $searchString = $request->get('search');
        $orderByRequest = $request->get('orderby', 'title');
        $orderRequest = $request->get('order', 'asc');
        $rows = $request->get('rows', 10);
        $orderBy = in_array($orderByRequest, $orderColumns) ? $orderByRequest : 'title';
        $order = in_array($orderRequest, $orders)? $orderRequest: 'asc';
        $status = $request->get('status');

        $company = auth('app')->user();
        $setting = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::SHOW_LOCATION_FILTER_ON_COMPANY_PAGE);


        $query   = JobPost::with('applicants.user.media', 'category', 'company', 'media')
            ->where('company_id', $company->id)
            ->where('status', JobStatus::PUBLISHED)
            ->select([
                'id', 'title', 'slug', 'created_at', 'published_at', 'vacancies', 'meta',
                'updated_at', 'expire_at', 'category_id', 'status', 'company_id', 'is_pinned', 'job_type',
                \DB::raw("CASE WHEN expire_at IS NULL OR expire_at >= CURDATE() THEN 1 ELSE 0 END as is_expired")
            ]);

        if (!blank($searchString)) {
            $query->where('title', 'like', "%{$searchString}%");
        }

        if ($status == 'active') {
            $query->whereDate('expire_at', '>=', now());
        }


        if ($status == 'expired') {
            $query->whereDate('expire_at', '<', now());
        }

        $query->orderByDesc('is_expired');
        $jobs = $query->latest()->paginate($rows);


        $query->orderByDesc('is_pinned')->orderBy($orderBy, $order);
        $jobs = $query->paginate($rows);

        $jobs->transform(function ($job) use ($company, $setting) {
            $jobLink = jobDetailsRoute($job);
            return $this->transformJobData($job, $jobLink, $setting);
        });

        return response()->json([
            'status' => 'success',
            'data'   => $jobs,
        ], Response::HTTP_OK);
    }

    private function publishedJobsWithFilter(Request $request)
    {
        $company = auth('app')->user();
        $rows = $request->integer('rows');
        $status = $request->get('status');

        $title = $request->get('title');
        $location = $request->get('location');
        $categoryId = $request->get('category');
        $companySettings = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::SHOW_LOCATION_FILTER_ON_COMPANY_PAGE);

        $orderColumns = ['title', 'expire_at', 'published_at'];
        $orders = ['asc', 'desc'];

        $orderByRequest = $request->get('orderby', 'title');
        $orderRequest = $request->get('order', 'asc');

        $orderBy = in_array($orderByRequest, $orderColumns) ? $orderByRequest : 'title';
        $order = in_array($orderRequest, $orders) ? $orderRequest: 'asc';


        $query = JobPost::with('category', 'address.city', 'address.state', 'address.country', 'company.creator')
            ->where('status', JobStatus::PUBLISHED)->where('company_id', $company->id);

        if ($status == 'active') {
            $query->actives()->nonExpired();
        }

        if (!isWpOlderVersion($request->header('x-plugin-version'), "2.5.9")) {
            $query = $query->whereNot('show_on_career_page', 0);
        }

        $jobs = $query->orderBy($orderBy, $order)->get();

        $categories = [];
        if (!blank($jobs)) {
            $categories = $jobs->map(function ($job) use ($categoryId) {
                return [
                    'id' => data_get($job, 'category.id'),
                    'name' => data_get($job, 'category.name'),
                    'selected' => data_get($job, 'category.id') == $categoryId,
                ];
            })->unique(function ($category) {
                return $category['id'];
            })->sortBy('name')->values()->all();
        }

        $locations = [];
        if (!blank($jobs)) {
            $locations = $this->careerService->locationFilterOptions($jobs, $companySettings, $location);
        }

        if (!blank($title) && !blank($jobs)) {
            $jobs = $jobs->where(function ($job) use ($title) {
                return stripos(data_get($job, 'title'), $title) !== false;
            })->values();
        }

        if (!blank($categoryId) && !blank($jobs)) {
            $jobs = $jobs->where('category_id', $categoryId);
        }

        // :Todo need refactor this portion.
        if (!blank($location) && !blank($jobs)) {
            if ($location === JobType::REMOTE) {
                $jobs = $jobs->where('job_type', $location);
            } else {
                $location = explode('-', $location);
                $cityId = $location[0] ?? null;
                $stateId = $location[1] ?? null;
                $countryId = $location[2] ?? null;

                if ($cityId) {
                    $jobs = $jobs->filter(function ($job) use ($cityId) {
                        return data_get($job, 'address.city_id') === (int) $cityId;
                    });
                } else if ($stateId) {
                    $jobs = $jobs->filter(function ($job) use ($stateId) {
                        return data_get($job, 'address.state_id') === (int) $stateId;
                    });
                } else if ($countryId) {
                    $jobs = $jobs->filter(function ($job) use ($countryId) {
                        return data_get($job, 'address.country_id') === (int) $countryId;
                    });
                }
            }
        }

        $perPage = (int) app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::JOBS_PER_PAGE);
        $perPage = empty($rows) ? $perPage : $rows;

        if ($perPage) {
            $jobs = $this->careerService->getPaginatedCollection($jobs, $perPage);
        }

        $jobs->transform(function ($job) use ($company, $companySettings) {
            $jobLink = jobDetailsRoute($job);
            return $this->transformJobData($job, $jobLink, $companySettings);
        });

        $data = [
            "jobs" => $jobs,
            "categories" => $categories,
            "locations" => $locations,
        ];

        return response()->json([
            'status' => 'success',
            'data'   => $data,
        ], Response::HTTP_OK);
    }

    private function transformJobData($job, $jobLink, $setting)
    {
        $data = $job->getAttributes();

        $data['created_at'] = $job->created_at->format(config('easyjob.user.date_format'));
        $data['expire_at'] = $job->expire_at->format(config('easyjob.user.date_format'));
        $data['posted_at'] = $job->posted_at->format(config('easyjob.user.date_format'));
        $data['category'] = data_get($job, 'category');
        $data['banner_image'] = $job->image;
        $data['applicants'] = [];
        $data['apply_url'] = jobApplyRoute($job);
        $data['vacancies'] = data_get($job, 'vacancies');

        $applicants = $job->applicants->whereIn('status', [JobApplyStatus::COMPLETE, JobApplyStatus::SELECTED, JobApplyStatus::REJECT])->take(4);
        foreach ($applicants as $applicant) {
            $data['applicants'][] = [
                'name' => data_get($applicant, 'user.name'),
                'image' => data_get($applicant, 'user.profile_image'),
            ];
        }

            $data['applicant_count']     = $job->applicants->whereIn('status', [JobApplyStatus::COMPLETE, JobApplyStatus::SELECTED, JobApplyStatus::REJECT])->count();
            $data['days_left']           = $job->expire_at->diffForHumans();
            $data['is_expired']          = $job->is_expired;
            $data['status']              = $job->status;
            $data['social_links']        = [
                'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=' . $jobLink . '/#',
                'linkedIn' => 'https://www.linkedin.com/sharing/share-offsite/?url=' . $jobLink,
                'twitter'  => 'https://twitter.com/intent/tweet?text=' . $jobLink . '/#',
            ];
            $data['job_link']            = $jobLink;
            $data['job_address']         = [
                'country' => $job->address ? $job->address->country()->first(['id', 'name']) : null,
                'state'   => $job->address ? $job->address->state()->first(['id', 'name']) : null,
                'city'    => $job->address ? $job->address->city()->first(['id', 'name']) : null,
            ];
            $data['job_address_id']      = isRemoteJob($job) ? 'remote' : app(JobPostService::class)->getJobAddressId($job, $setting);
            $data['is_remote']           = isRemoteJob($job);
            $data['company_name']        = $job->company->name;
            $data['company_easyjob_url'] = companyPageRoute($job->company);
            $data['hideCoverPhoto'] = castToInt(data_get($job, 'meta.hideCoverPhoto', 0));
            $data['is_pinned'] = castToInt($job->is_pinned);
            return $data;
    }

    public function getJobLocations(Request $request)
    {
        $orderColumns = ['title', 'expire_at', 'published_at'];
        $orders = ['asc', 'desc'];
        $searchString = $request->get('search');
        $orderByRequest = $request->get('orderby', 'title');
        $orderRequest = $request->get('order', 'asc');
        $rows = $request->get('rows', 10);
        $orderBy = in_array($orderByRequest, $orderColumns) ? $orderByRequest : 'title';
        $order = in_array($orderRequest, $orders)? $orderRequest: 'asc';
        $status = $request->get('status');
        $location = $request->get('location');

        $company = auth('app')->user();
        $companySettings = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::SHOW_LOCATION_FILTER_ON_COMPANY_PAGE);

        $showLocationFilter = $companySettings['show_location'] ?? null;
        $showCity = $companySettings['show_city'] ?? null;
        $showState = $companySettings['show_state'] ?? null;
        $showCountry = $companySettings['show_country'] ?? null;

        $query   = JobPost::with('applicants.user.media', 'category', 'company', 'media')
            ->where('company_id', $company->id)
            ->where('status', JobStatus::PUBLISHED)
            ->actives()
            ->nonExpired()
            ->select([
                'id', 'title', 'slug', 'created_at', 'published_at', 'vacancies', 'meta',
                'updated_at', 'expire_at', 'category_id', 'status', 'company_id', 'is_pinned', 'job_type'
            ]);

        if (!blank($searchString)) {
            $query->where('title', 'like', "%{$searchString}%");
        }

        if ($status == 'active') {
            $query->whereDate('expire_at', '>=', now());
        }


        if ($status == 'expired') {
            $query->whereDate('expire_at', '<', now());
        }



        $query->orderByDesc('is_pinned')->orderBy($orderBy, $order);
        $jobs = $query->clone()->get();
        $locations = [];

        if (!blank($jobs)) {
            $locations = app(CareerService::class)->locationFilterOptions($jobs, $companySettings, $location);
        }

        return $this->sendSuccessResponse($locations);
    }


    public function activeJobs(Request $request)
    {
        $company = auth('app')->user();
        $user = auth('app')->user()->creator;

        $jobs = $this->jobPostService->getActiveJobs($company, $user);

        return $this->sendSuccessResponse($jobs);
    }

    public function draftJobs(Request $request)
    {
        $searchString = $request->get('search');
//        $limit = $request->get('limit', 10);
        $company = auth('app')->user();

//        $orderBy = $request->get('orderby');
//        $OrderColName = 'title';
//        $order = 'asc';
//
//        if (!blank($orderBy)) {
//            $splitted = explode(":", $orderBy);
//            $OrderColName = data_get($splitted, 0, $OrderColName);
//            $order = data_get($splitted, 1, $order);
//        }
        $rows = $request->get('rows', 10);

        $query = JobPost::with('applicants', 'category')->where('company_id', $company->id)
            ->where('status', JobStatus::DRAFT)->latest();


        if (!blank($searchString)) {
            $query->where('title', 'like', "%{$searchString}%");
        }

        $jobs = $query->paginate($rows);
        $jobs->transform(function ($job) {
            $data = [
                'id'              => data_get($job, 'id'),
                'title'           => data_get($job, 'title'),
                'banner_image'    => $job->image,
                'category'        => data_get($job, 'category'),
                'applicant_count' => $job->applicants->whereIn('status', [JobApplyStatus::COMPLETE, JobApplyStatus::SELECTED, JobApplyStatus::REJECT])->count(),
                'created_at'      => $job->created_at->format(config('easyjob.user.date_format')),
                'expire_at'       => $job->expire_at->format(config('easyjob.user.date_format')),
                'days_left'       => $job->expire_at->diffForHumans(),
                'is_expired'      => $job->is_expired,
                'job_link'        => jobDetailsRoute($job),
                'status'          => $job->status,
                'posted_at'       => $job->posted_at->format(config('easyjob.user.date_format')),
            ];

            $applicants = $job->applicants->whereIn('status', [JobApplyStatus::COMPLETE, JobApplyStatus::SELECTED, JobApplyStatus::REJECT])->take(4);
            foreach ($applicants as $applicant) {
                $data['applicants'][] = [
                    'name'  => data_get($applicant, 'user.name'),
                    'image' => data_get($applicant, 'user.profile_image'),
                ];
            }

            return $data;
        });


        return response()->json([
            'status' => 'success',
            'data'   => $jobs,
        ], 200);
    }

    public function archiveJobs(Request $request)
    {
        $searchString = $request->get('search');
//        $limit = $request->get('limit', 10);
        $company = auth('app')->user();

//        $orderBy = $request->get('orderby');
//        $OrderColName = 'title';
//        $order = 'asc';
//
//        if (!blank($orderBy)) {
//            $splitted = explode(":", $orderBy);
//            $OrderColName = data_get($splitted, 0, $OrderColName);
//            $order = data_get($splitted, 1, $order);
//        }
        $rows = $request->get('rows', 10);

        $query = JobPost::with('applicants', 'category')->where('status', JobStatus::ARCHIVED)
            ->where('company_id', $company->id)->latest();

        if (!blank($searchString)) {
            $query->where('title', 'like', "%{$searchString}%");
        }
        $jobs = $query->paginate($rows);
        $jobs->transform(function ($job) {
            $data = [
                'id'              => data_get($job, 'id'),
                'title'           => data_get($job, 'title'),
                'banner_image'    => $job->image,
                'category'        => data_get($job, 'category'),
                'applicant_count' => $job->applicants->whereIn('status', [JobApplyStatus::COMPLETE, JobApplyStatus::SELECTED, JobApplyStatus::REJECT])->count(),
                'created_at'      => $job->created_at->format(config('easyjob.user.date_format')),
                'expire_at'       => $job->expire_at->format(config('easyjob.user.date_format')),
                'posted_at'       => $job->posted_at->format(config('easyjob.user.date_format')),
                'days_left'       => $job->expire_at->diffForHumans(),
                'is_expired'      => $job->is_expired,
                'job_link'        => jobDetailsRoute($job),
                'status'          => $job->status,
            ];

            $applicants = $job->applicants->whereIn('status', [JobApplyStatus::COMPLETE, JobApplyStatus::SELECTED, JobApplyStatus::REJECT])->take(4);
            foreach ($applicants as $applicant) {
                $data['applicants'][] = [
                    'name'  => data_get($applicant, 'user.name'),
                    'image' => data_get($applicant, 'user.profile_image'),
                ];
            }

            return $data;
        });

        return response()->json([
            'status' => 'success',
            'data'   => $jobs,
        ], Response::HTTP_OK);
    }

    public function jobDetails(JobPost $job)
    {
        $company = auth('app')->user();

        if ($this->isNotMyJob($company, $job) instanceof JsonResponse) {
            return $this->isNotMyJob($company, $job);
        }

        $job->load('category', 'address');
        $address         = $job->address;
        $employment_type = $experience_level = $salary_type = [];
        if ($job->employment_type) {
            $employment_type = [
                'id'   => $job->employment_type,
                'name' => __("constants.employment_type.{$job->employment_type}"),
            ];
        }

        if ($job->experience_level) {
            $experience_level = [
                'id'   => $job->experience_level,
                'name' => __("constants.experience_level.{$job->experience_level}"),
            ];
        }

        if ($job->salary_type) {
            $salary_type = [
                'id'   => $job->salary_type,
                'name' => __("constants.salary_type.{$job->salary_type}"),
            ];
        }
        $jobLink              = jobDetailsRoute($job);
        $jobData = [
            'id'               => $job->id,
            'title'            => $job->title,
            'slug'             => $job->slug,
            'preview'          => jobPreviewRoute($job),
            'banner'           => data_get($company, 'type') == Type::AGENCY ? $this->jobPostService->changeMediaUrl($job, $job->image) : $job->image,
            'banner_image'     => data_get($company, 'type') == Type::AGENCY ? $this->jobPostService->changeMediaUrl($job, $job->image) : $job->image,
            'company_logo'     => $this->jobPostService->changeMediaUrl($job, data_get($job, 'company.logo')),
            'requirements'     => $job->requirements,
            'responsibilies'   => $job->responsibilies,
            'other_benefits'   => $this->jobPostService->showJobBenefit($job),
            'category'         => $job->category()->first(['id', 'name']),
            'vacancies'        => $job->vacancies,
            'country'          => $address ? $address->country()->first(['id', 'name']) : null,
            'state'            => $address ? $address->state()->first(['id', 'name']) : null,
            'city'             => $address ? $address->city()->first(['id', 'name']) : null,
            'is_remote'        => isRemoteJob($job),
            'job_type'         => data_get($job, 'job_type', JobType::ON_SITE),
            'expire_at'        => $job->expire_at,
            'skills'           => $job->skills ? $job->skills()->get(['id', 'name'])->toArray() : [],
            'office_time'      => $job->getOfficeTime(),
            'employment_type'  => $employment_type,
            'experience_level' => $experience_level,
            'salary_type'      => $salary_type,
            'salary'           => $job->getSalary(),
            'meta'             => $job->meta,
            'status'           => $job->status,
            'company_data'     => $this->serializeCompanyData($company),
            'apply_url'        => data_get($company, 'type') == Type::AGENCY ? $this->jobPostService->changeAgencyUrl(jobApplyRoute($job)) : jobApplyRoute($job),
            'social_links'      => [
                'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=' . $jobLink . '/#',
                'linkedIn' => 'https://www.linkedin.com/sharing/share-offsite/?url=' . $jobLink,
                'twitter'  => 'https://twitter.com/intent/tweet?text=' . $jobLink . '/#',
            ],
            'job_link'          => $jobLink,
            'hideCoverPhoto'    => (int)data_get($job, 'meta.hideCoverPhoto', 0),
            'show_on_job_board' => (int)data_get($job, 'show_on_job_board', 0),
            'hiring_teams' => app(JobPermissionService::class)->getHiringTeamDetails($job->company),
        ];

        return response()->json([
            'status' => 'success',
            'data'   => $jobData,
        ]);
    }

    public function pipelineDetails(JobPost $job)
    {
        $data = $this->getPipelineWithCandidate($job);

        return response()->json([
            'status' => 'success',
            'data'   => $data,
        ], Response::HTTP_OK);
    }

    public function jobCandidates(JobPost $job, Request $request)
    {
        $company = auth('app')->user();

        if ($this->isNotMyJob($company, $job) instanceof JsonResponse) {
            return $this->isNotMyJob($company, $job);
        }

        if ($this->isNotMyJob($company, $job) instanceof JsonResponse) {
            return $this->isNotMyJob($company, $job);
        }
        $candidates           = $this->getFilteredCandidate($job, $request);
        $applicantTransformer = app(ApplicantTransformer::class);

        return response()->json([
            'status' => 'success',
            'data'   => [
                'candidates' => $applicantTransformer->serializeDataForJobCandidateListWp($candidates),
                'job'        => $applicantTransformer->serializeJobForCandidateList($job),
            ],
        ], Response::HTTP_OK);
    }

    public function pipelineUpdate(JobPost $job, Request $request)
    {
        $company = auth('app')->user();
        if ($this->isNotMyJob($company, $job) instanceof JsonResponse) {
            return $this->isNotMyJob($company, $job);
        }

        $steps = $request->get('steps', []);
        $newSteps = collect($steps);

        $dropPipelineIds = [];

        $jobPipelines = $job->pipelines;
        if(!blank($jobPipelines)) {
            foreach ($jobPipelines as $jobPipeline) {

                $hasStep = $newSteps->where('id', $jobPipeline->id)->first();
                if(blank($hasStep)) {
                    $dropPipelineIds[] = $jobPipeline->id;
                }
            }
        }

        if(!blank($dropPipelineIds)) {
            JobPipeline::whereIn('id', $dropPipelineIds)->delete();
        }

        if (!blank($steps)) {
            foreach ($steps as $key => $step) {
                JobPipeline::updateOrCreate(
                    ['id' => data_get($step, 'id'), 'job_post_id' => $job->id],
                    ['label' => data_get($step, 'label'), 'type' => data_get($step, 'type'), 'order' => $key]
                );
            }
        }


        if (!blank($dropPipelineIds)) {
            $query = $job->applicants()->whereIn('job_pipeline_id', $dropPipelineIds);

            if ($query->count() > 0) {
                $jobPipeline = JobPipeline::where(['job_post_id' => $job->id, 'type' => PipelineType::APPLIED])->first();
                $query->update(['job_pipeline_id' => data_get($jobPipeline, 'id', 0), 'status' => JobApplyStatus::COMPLETE]);
            }
        }

        $job->refresh();

        return response()->json([
            'status' => 'success',
            'data'   => $job->pipelines,
        ], Response::HTTP_OK);
    }

    public function changePipelineForCandidate(JobPost $job, Request $request)
    {
        $company = auth('app')->user();

        if ($this->isNotMyJob($company, $job) instanceof JsonResponse) {
            return $this->isNotMyJob($company, $job);
        }

        $pipelineId = $request->get('pipeline_id');
        $applicantIds = $request->get('applicants');

        $pipeline = $job->pipelines()->where('id', $pipelineId)->first();

        if(blank($pipeline) || blank($applicantIds)) {
            return response()->json([
                'status' => 'failed',
                'data' => [
                    'request' => $request->all(),
                    'message' => 'Pipeline Not Found',
                ],
            ], Response::HTTP_BAD_REQUEST);
        }

        try {
            $applicants = JobApplicant::whereIn('id', $applicantIds)->get();

            $updateData['job_pipeline_id'] = $pipeline->id;
            $updateData['pipeline_updated_at'] = now();
            $updateData['status'] = JobApplyStatus::COMPLETE;

            $notificationType = NotificationType::CANDIDATE_PIPELINE_CHANGED;
            if ($pipeline->type === PipelineType::REJECTED) {
                $updateData['status'] = JobApplyStatus::REJECT;
                $notificationType = NotificationType::CANDIDATE_REJECTED;
            } elseif ($pipeline->type === PipelineType::SELECTED) {
                $updateData['status'] = JobApplyStatus::SELECTED;
                $notificationType = NotificationType::CANDIDATE_SELECTED;
            }

            $applicants->each->update($updateData);

            foreach ($applicants as $applicant) {
                event(new JobPipelineChanged($applicant, $notificationType, $company->creator));
            }

            return response()->json([
                'status' => 'success',
                'data' => [],
            ], Response::HTTP_OK);
        } catch (\Exception $exception) {
            \Log::alert($exception->getMessage());
            return response()->json([
                'status' => 'failed',
                'data' => $exception->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }
    }

    public function serializeJobPostData(JobPost $jobPost, $filter = [])
    {
        $data                    = Arr::except($jobPost->toArray(), ['category_id', 'sub_category_id', 'company_id', 'deleted_at', 'steps', 'apply_rules']);
        $data['category']        = $this->serializeCategory($jobPost->category);
        $data['sub_category']    = $this->serializeCategory($jobPost->subCategory);
        $data['salary_type']     = data_get(trans('constants.salary_type'), $jobPost->salary_type);
        $data['employment_type'] = data_get(trans('constants.employment_type'), $jobPost->employment_type);
        $data['candidate_count'] = $jobPost->applicants->where('status', JobApplyStatus::COMPLETE)->count();
        $data['address']         = $jobPost->address;
        $data['department']      = $jobPost->department;
        $data['created_by']      = $this->serializeUserData($jobPost->creator);
        $data['slug']            = $jobPost->slug;
        $data['apply_url']       = jobApplyRoute($jobPost);
        $data['company']         = $jobPost->company->getAttributes();
        $data['company_url']     = companyPageRoute($jobPost->company);
        $data['banner']          = $jobPost->getImageAttribute();

        if (in_array('skills', $filter)) {
            $data['skills'] = $jobPost->skills->map(function ($skills) {
                return $skills->only('id', 'name', 'slug');
            });
        }
        return $data;
    }

    public function serializeCategory($category)
    {
        if ($category instanceof Category) {
            return Arr::except($category->toArray(), ['created_at', 'updated_at']);
        }
        return [];
    }

    public function serializeUserData(User $user)
    {
        $data                  = $user->only('first_name', 'last_name', 'name', 'email');
        $data['profile_image'] = $user->profile_image;
        return $data;
    }



    public function isNotMyJob($company, $job)
    {
        if (!$company->is($job->company)) {
            return response()->json([
                'status' => 'failed',
                'data'   => [
                    'message' => 'This job is not for your company',
                ],
            ], Response::HTTP_UNAUTHORIZED);
        }

        return false;
    }

    private function serializeCompanyData($company)
    {
        $data                   = $company->only('name', 'mobile_number', 'description', 'website');
        $data['creator']        = $company->creator;
        $data['address']        = $company->address;
        $data['showcase_photo'] = $company->getMedia('showcase_photo')->map(function ($media) {
            return $media->getFullUrl();
        });
        $data['logo']           = $company->logo;
        $data['cover_photo']    = $company->getMedia('cover_photo')->map(function ($media) {
            return $media->getFullUrl();
        });
        return $data;
    }

    /**
     * Get Pipeline list with candidate
     * @param JobPost $job
     * @return mixed
     */
    private function getPipelineWithCandidate(JobPost $job)
    {
        $applicants = JobApplicant::where('job_post_id', $job->id)
            ->where('status', '!=', JobApplyStatus::PENDING)
            ->without(['apply_step_tab', 'quiz_started_at', 'quiz_ends_at', 'quiz_last_question_id'])
            ->with(['assessments', 'pipeline','employments','user', 'user.media'])
            ->orderBy('pipeline_order', 'ASC');

        return $job->pipelines->map(function ($pipeline) use ($applicants) {
            $pipelineApplicants = (clone $applicants)->where('job_pipeline_id', $pipeline->id)->get();

            $pipeline['applicants'] = app(ApplicantTransformer::class)->serializeDataForJobCandidateListWp($pipelineApplicants);
            $pipeline['count'] = $pipelineApplicants->count();

            return $pipeline;
        })->toArray();
    }

    public function jobCreate(Request $request)
    {
        $validator = \Validator::make($request->all(), $this->jobCreateRules());

        $validator->after(function ($validator) use ($request) {
            if ($this->employmentTypeOtherValidationRules($request->only('employment_type', 'employment_type_other'))) {
                $validator->errors()->add(
                    'employment_type_other', 'The employee type field is required.'
                );
            }
        });

        if ($validator->fails()) {
            return $this->sendFailedResponse($validator->errors()->toArray());
        }

        $user = auth('app')->user()->creator;

        if (!$user->email_verified_at) {
            return $this->sendFailedResponse("Your email is not verified, please verify email to publish a job.");
        }

        $jobData = $request->all();
        $jobData['has_benefits'] = $request->boolean('has_benefits');
        $data    = $this->serializeJobInformationData($jobData, $user);

        $address = $this->serializeAddressData($jobData);
        [$status, $message] = $this->jobPostPackagePermissionAndPackageExpireCheck($user, data_get($data, 'status', JobStatus::DRAFT));
        if ($status) {
            return $this->sendFailedResponse($message);
        }

        \DB::beginTransaction();
        try {
            $jobPost = JobPost::create($data);

            $jobPost->address()->create($address);

            $this->updateSkillsData($request->get('skills'), $jobPost);

            if ($request->get('coverPhoto')) {
                $extension = explode('/', mime_content_type($request->get('coverPhoto')))[1];
                $fileName  = JobPost::POST_COVER_COLLECTION . "." . $extension;

                $jobPost->addMediaFromBase64($request->get('coverPhoto'))
                    ->usingFileName($fileName)
                    ->toMediaCollection(JobPost::POST_COVER_COLLECTION);
            }

            $this->jobPostService->saveJobDefaultPipelineTemplate(auth('app')->user(), $jobPost->id);

            \DB::commit();

            $response = $jobPost->toArray();
            $response['show_on_job_board'] = (int) $jobPost['show_on_job_board'];
            $response['employment_type_other'] = $jobPost->meta['employment_type_other'] ?? null;
            $response['hideCoverPhoto'] = (int)$jobPost->meta['hideCoverPhoto'] ?? 0;
            $response['salary_type'] = data_get($jobData, 'salary_type');
            $response['employment_type'] = data_get($jobData, 'employment_type');
            $response['experience_level'] = data_get($jobData, 'experience_level');
            $response['job_type'] = data_get($jobData, 'job_type');
            if (!blank(data_get($jobPost, 'salary'))) {
               $response['salary_field_type'] = 1;
            } else {
                $response['salary_field_type'] = 2;
            }
            $response['job_pipelines'] = $jobPost->pipelines;

            return $this->sendSuccessResponse($response, 'Job created successfully.');
        } catch (\Exception $e) {
            \DB::rollBack();
            return $this->sendFailedResponse($e->getMessage());
        }
    }

    private function employmentTypeOtherValidationRules($requestData) {
        $employmentTypeId = data_get($requestData, 'employment_type.id', 0);
        $employmentTypeOther = data_get($requestData, 'employment_type_other');

        if($employmentTypeId == EmploymentType::OTHER && strlen($employmentTypeOther) == 0) {
            return true;
        }
        return false;
    }

    public function jobUpdate(Request $request, $id)
    {
        $company = auth('app')->user();

        $jobPost = JobPost::find($id);
        if (blank($jobPost)) {
            return $this->sendFailedResponse('Invalid job.');
        }

        if ($company->id !== $jobPost->company_id) {
            return $this->sendUnauthorisedResponse();
        }

        $validator = \Validator::make($request->all(), $this->jobCreateRules());

        $validator->after(function ($validator) use ($request) {
            if ($this->employmentTypeOtherValidationRules($request->only('employment_type', 'employment_type_other'))) {
                $validator->errors()->add(
                    'employment_type_other', 'The employee type field is required.'
                );
            }
        });

        if ($validator->fails()) {
            return $this->sendFailedResponse($validator->errors()->toArray());
        }

        $user = $company->creator;
        if (!$user->email_verified_at) {
            return $this->sendFailedResponse("Your email is not verified, please verify email to publish a job.");
        }

        $jobData = $request->all();
        $jobData['has_benefits'] = $request->boolean('has_benefits');
        $data = $this->serializeJobInformationData($jobData, $user, $jobPost);

        $address = $this->serializeAddressData($jobData);

        [$status, $message] = $this->jobPostPackagePermissionAndPackageExpireCheck($user, data_get($data, 'status', JobStatus::DRAFT), $jobPost);
        if ($status) {
            return $this->sendFailedResponse($message);
        }


        \DB::beginTransaction();
        try {
            $jobPost->fill($data)->save();

            if ($jobPost->address) {
                $jobPost->address()->updateOrCreate(['id' => $jobPost->address->id], $address);
            } else {
                $jobPost->address()->create($address);
            }

            $this->updateSkillsData($request->get('skills'), $jobPost);

            if ($request->get('coverPhoto')) {
                $jobPost->addMediaFromBase64($request->get('coverPhoto'))
                    ->toMediaCollection(JobPost::POST_COVER_COLLECTION);
            }

            \DB::commit();

            $this->sendJobUpdateNotification($jobPost);

            $response = $jobPost->toArray();
            $response['show_on_job_board'] = (int) $jobPost['show_on_job_board'];
            $response['employment_type_other'] = $jobPost->meta['employment_type_other'] ?? null;
            $response['hideCoverPhoto'] = (int)$jobPost->meta['hideCoverPhoto'] ?? 0;
            $response['salary_type'] = data_get($jobData, 'salary_type');
            $response['employment_type'] = data_get($jobData, 'employment_type');
            $response['experience_level'] = data_get($jobData, 'experience_level');
            $response['job_type'] = data_get($jobData, 'job_type');

            if (!blank(data_get($jobPost, 'salary'))) {
                $response['salary_field_type'] = 1;
            } else {
                $response['salary_field_type'] = 2;
            }

            event(new JobPostUpdate($jobPost, ['status' => $request->integer('status')], $company));

            return $this->sendSuccessResponse($response, 'Job updated successfully.');
        } catch (\Exception $e) {
            \DB::rollBack();
            return $this->sendFailedResponse('Job failed to update.');
        }
    }

    /**
     * Update to Job Pinned/Unpinned
     * @param Request $request
     * @param $jobId
     * @return Application|ResponseFactory|JsonResponse|Response
     */
    public function updatePinnedJob(Request $request, $jobId)
    {
        try {
            $company = auth('app')->user();

            $jobPost = JobPost::query()->find($jobId);
            if (blank($jobPost)) {
                return $this->sendFailedResponse('Invalid job.');
            }

            if ($company->id !== $jobPost->company_id) {
                return $this->sendUnauthorisedResponse();
            }

            $jobPost = $this->jobPostService->updatePinnedJob($jobPost, $request->boolean('is_pinned'));

            return $this->sendSuccessResponse($jobPost, __('responses.context_pinned', ['context' => 'Job', 'pin' => $jobPost->is_pinned ? 'Pinned' : 'Unpinned']));
        } catch (Exception $e) {
            Log::error($e);
            return $this->sendFailedResponse([], __('responses.context_cannot_be_pinned', ['context' => 'Job']));
        }
    }

    public function destroyJob($jobId)
    {
        $company = auth('app')->user();

        $job = JobPost::find($jobId);
        if (blank($job)) {
            return $this->sendFailedResponse('Invalid job.');
        }

        if ($company->id !== $job->company_id) {
            return $this->sendUnauthorisedResponse();
        }

        try {
            event(new JobPostDelete($job, $company->creator, $company));

            $job->delete();
            return $this->sendSuccessResponse([], 'Job Deleted');
        } catch (\Exception $e) {
            return $this->sendFailedResponse('Cannot delete job. Something went wrong.');
        }
    }

    public function changeJobStatus(Request $request, $id)
    {
        $company = auth('app')->user();

        $jobPost = JobPost::find($id);
        if (blank($jobPost)) {
            return $this->sendFailedResponse('Invalid job.');
        }

        if ($company->id !== $jobPost->company_id) {
            return $this->sendUnauthorisedResponse();
        }

        if ($request->has(['status', 'change_expire_date', 'expire_date_status'])){
            return $this->markAsExpireOrExtend($request, $jobPost);
        }

        $status = $request->get('status');

        $user = $company->creator;

        try {
            event(new JobPostUpdate($jobPost, ['status' => $request->integer('status')], $company));

            $pkgService = app(PackageService::class);
            switch ($status) {
                case JobStatus::PUBLISHED:
                    if (!$pkgService->hasPackagePermission(PackageRule::ACTIVE_JOBS, $user)) {
                        $error = data_get(__("subscription.package.errors"), PackageRule::ACTIVE_JOBS, '');
                        return $this->sendFailedResponse($error);
                    }

                    if (Carbon::now()->gte($jobPost->expire_at)) {
                        return $this->sendFailedResponse("The expiry date must be a future date.");
                    }

                    $jobPost->update([
                        'status'       => JobStatus::PUBLISHED,
                        'published_at' => Carbon::now()->toDateTimeString(),
                    ]);

                    return $this->sendSuccessResponse($jobPost, "Job Published successfully");
                case JobStatus::ARCHIVED:
                    $jobPost->update([
                        'status' => JobStatus::ARCHIVED,
                    ]);
                    return $this->sendSuccessResponse($jobPost, "Job Archived successfully");
                case JobStatus::REPUBLISHED:
                    if (!$pkgService->hasPackagePermission(PackageRule::ACTIVE_JOBS, $user)) {
                        $error = data_get(__("subscription.package.errors"), PackageRule::ACTIVE_JOBS, '');
                        return $this->sendFailedResponse($error);
                    }

                    if (Carbon::now()->gte($jobPost->expire_at)) {
                        return $this->sendFailedResponse("The expiry date must be a future date.");
                    }

                    $jobPost->update([
                        'status'       => JobStatus::PUBLISHED,
                        'published_at' => Carbon::now()->toDateTimeString(),

                    ]);

                    return $this->sendSuccessResponse($jobPost, "Job Republished successfully");
            }
        } catch (\Exception $e) {
            return $this->sendFailedResponse("Job failed to update.");
        }
    }

    public function markAsExpireOrExtend($request, $jobPost)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|numeric',
        ]);

        if (blank($jobPost)) {
            return $this->sendFailedResponse('Invalid job id');
        }

        if ($validator->fails()) {
            return $this->sendFailedResponse($validator->errors()->toArray());
        }

        try {
            $jobPostService = new JobPostService();
            $jobPost = $jobPostService->changeJobStatus($jobPost, $request->only(['status', 'change_expire_date', 'expire_date_status']));
            $message = 'Job ' . trans('constants.change_status.' . request('status')) . ' successfully.';
            return $this->sendSuccessResponse($jobPost, $message);
        } catch (Exception $e) {
            \Log::error($e);
            return $this->sendFailedResponse('Cannot extend job. Something went wrong.');
        }
    }

    public function jobRequiredFieldData($id, Request $request)
    {
        $company = auth('app')->user();
        $jobPost = JobPost::where(['id' => $id, 'company_id' => $company->id])->first();

        if ($this->isNotMyJob($company, $jobPost) instanceof JsonResponse) {
            return $this->isNotMyJob($company, $jobPost);
        }

        try {
            $wpVersion = $request->header('x-plugin-version', null);

            if (version_compare($wpVersion, "2.4.0") === -1 || blank($wpVersion)) {
                return $this->jobRequiredFieldDataForOldVersion($id);
            }

            $response = app(JobService::class)->getJobRequiredField(data_get($jobPost, 'slug'), $company);
            return $this->sendSuccessResponse($response);
        } catch (Exception $e) {
            \Log::error($e);
            return $this->sendFailedResponse(__('responses.something_went_wrong'), []);
        }
    }

    public function jobRequiredFieldDataForOldVersion($id){
        try {
            $company       = auth('app')->user();
            $post          = JobPost::find($id);
            $applyAllRules = config('job.apply.fields');
            array_push($applyAllRules, config('job.post.apply.steps')[JobApplyStep::RESUME]['fields']);
            $applyRules = data_get($post, "meta.apply_rules", []) ?? [];
            $data       = [];

            $applyTabs  = config('job.post.apply.steps')[JobApplyStep::PERSONAL_INFORMATIONS]['tabs'];
            foreach ($applyAllRules as $key => $allRule) {
                $tabData       = [
                    'index'    => $key,
                    'name'     => data_get($applyTabs, "{$key}.title", 'Documents'),
                    'id'       => data_get($applyTabs, "{$key}.tab", 'documents'),
                    'selected' => [],
                ];
                $selectedField = 0;
                foreach ($allRule as $field => $name) {

                    //$selected = isset($applyRules[$key]) && array_key_exists($field, $applyRules[$key]) ? $applyRules[$key][$field] == 'required' : false;
                    $selected = $applyRules[$key][$field]['visibility'] ?? false;

                    if ($selected) {
                        array_push($tabData['selected'], $field);
                        $selectedField++;
                    }

                    $tabData['rules'][] = [
                        'field' => $field,
                        'name'  => $name,
                    ];
                }
                $tabData['selectAll'] = count($allRule) === $selectedField;
                if (count($applyRules) == 0) {
                    $tabData['selected']  = array_keys($allRule);
                    $tabData['selectAll'] = true;
                }

                $data[] = $tabData;
            }

            $customFields = data_get($post, 'custom_fields', []);

            $jobCustomFields = array_filter($customFields, function ($item) {
                return $item['meta']['visibility'];
            });

            $jobCustomFields = array_map(function ($item) {
                return $item['field_name'];
            }, $jobCustomFields);

            $jobCustomFields = array_values($jobCustomFields);

            // $jobCustomFields            = array_column(data_get($post, 'custom_fields', []), 'field_name');

            $customApplyRules ['rules'] = $company->applyFields->where('active', Status::ACTIVE)->map(function ($field) {
                return [
                    'id'         => data_get($field, 'id'),
                    'title'      => data_get($field, 'title'),
                    'type'       => data_get($field, 'type'),
                    'field_name' => data_get($field, 'field_name'),
                    'field'      => data_get($field, 'field_name'),
                    'name'       => data_get($field, 'title'),
                ];
            })->toArray();

            $customApplyRules['rules'] = array_values($customApplyRules['rules']);




            $customApplyRules['name']      = 'Custom Fields';
            $customApplyRules['id']        = 'custom_fields';
            $customApplyRules['selected']  = $jobCustomFields;
            $customApplyRules['selectAll'] = !blank($customApplyRules ['rules']) && count($customApplyRules ['rules']) === count($jobCustomFields);
            $response['apply_rules']       = $data;
            $response['custom_rules']      = [$customApplyRules];
            return $this->sendSuccessResponse($response);
        } catch (\Exception $e) {
            return $this->sendFailedResponse($e->getMessage());
        }
    }

    public function saveRequiredFields(Request $request, $job)
    {
        $wpVersion = $request->header('x-plugin-version', null);

        if (version_compare($wpVersion, "2.4.0") === -1 || blank($wpVersion)) {
            return $this->saveRequiredFieldsForOldVersion($request, $job);
        }

        $company = auth('app')->user();
        $user    = $company->creator;

        $jobPost = JobPost::where(['id'=> $job, 'company_id' => $company->id])->first();
        if (blank($jobPost)) {
            return $this->sendUnauthorisedResponse();
        }

        $request->validate([
            'apply_rules' => ['required', 'array'],
            'custom_fields' => ['nullable', 'array'],
        ]);

        try {
            app(JobService::class)->saveJobRequiredFields($jobPost, $user, $request->only('apply_rules', 'custom_fields'), $company);
            return $this->sendSuccessResponse([], __('responses.updated'));
        } catch (Exception $e) {
            \Log::error($e);
            return $this->sendFailedResponse(__('responses.something_went_wrong'), []);
        }
    }

    public function saveRequiredFieldsForOldVersion($request, $job){
        try {
            $company = auth('app')->user();

            $jobPost = JobPost::where(['id'=> $job, 'company_id' => $company->id])->first();
            if (blank($jobPost)) {
                return $this->sendUnauthorisedResponse();
            }

            $validator = Validator::make($request->all(), [
                'apply_rules'  => ['required', 'array'],
                'custom_rules' => ['required', 'array'],
            ]);

            if ($validator->fails()) {
                return $this->sendFailedResponse($validator->errors()->toArray());
            }

            $metaData = $jobPost->meta;
            $metaData['apply_rules'] = $this->serializeRequiredData($request->get('apply_rules'));

            $jobPost->fill([
                'meta' => $metaData,
                'custom_fields' => $this->saveJobCustomField($request->get('custom_rules', [])),
            ])->save();

            return $this->sendSuccessResponse([], 'Job apply rules updated successfully.');
        } catch (Exception $e) {
            return $this->sendFailedResponse($e->getMessage());
        }
    }

    /**
     * Formatting job apply rules
     * @param array $applyRules
     * @return array
     */
    private function serializeRequiredData(array $applyRules)
    {
        $defaultFields = ['first_name' => 'first_name', 'last_name'=> 'last_name'];

        $data = [];
        foreach ($applyRules as $key => $tab) {
            foreach ($tab['rules'] as $rule) {
                $field = $rule['field'];

                // Selected field is a visible field which one show when candidate apply
                $selectedFieldKeys = array_flip(data_get($tab, 'selected', []));
                $requiredFieldKeys = array_flip(data_get($tab, 'required', []));

                //dd($field, $selectedFieldKeys[$field]);
                // Check field visibility & required
                $isVisible = isset($selectedFieldKeys[$field]);
                $isRequired = isset($requiredFieldKeys[$field]);

                // Set Default Value
                if(isset($defaultFields[$field]) || isset($selectedFieldKeys[$field])) {
                    $isVisible = true;
                    $isRequired = true;
                }

                $data[$key][$field] = [
                    "visibility" => $isVisible,
                    "rules" => [$isRequired ? 'required' : 'nullable'],
                ];
            }
        }

        return $data;
    }

    /**
     * Formatting Job custom field
     * @param $customRules
     * @return array
     */
    private function saveJobCustomField($customRules) {
        $jobCustomRules = null;
        $customApplyRules = $customRules[0] ?? [];

        if (!blank($customApplyRules)) {
            $companyRules = data_get($customApplyRules, 'rules') ?? [];
            $selectedFieldKeys = array_flip(data_get($customApplyRules, 'selected', []));
            $requiredFieldKeys = array_flip(data_get($customApplyRules, 'required', []));

            foreach ($companyRules as $rule) {
                $isRequired = isset($requiredFieldKeys[$rule['field_name']]);
                $customApplyField = CustomApplyField::find($rule['id']);

                if (isset($selectedFieldKeys[$rule['field_name']])){
                    $isRequired = true;
                }

                $rule['meta'] = [
                    'rules' => [$isRequired ? 'required' : 'nullable'],
                    'visibility' => true,
                ];

                if ($customApplyField->type == "file"){
                    $rule['meta']['allowed_types'] = $customApplyField->meta?->allowed_types;
                }

                if (isset($selectedFieldKeys[$rule['field_name']])) {
                    unset($rule['field'], $rule['name']);

                    $jobCustomRules [] = $rule;
                }
            }
        }

        return $jobCustomRules;
    }

    public function onboardCandidate(JobPost $job, Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'applicants'       => 'required|array',
            'joining_date'     => 'required|date',
            'assigned_salary'  => 'required',
            'provision_period' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendFailedResponse($validator->errors()->toArray());
        }

        $company = auth('app')->user();
        $applicants = $job->applicants()->whereIn('id', $request->get('applicants'))
            ->where('company_id', $company->id)
            ->get();

        if (blank($applicants)) {
            return $this->sendFailedResponse('Applicant not found for this job.');
        }

        \DB::beginTransaction();
        try {
            $applicants->map(function ($applicant) use ($request) {
                $applicant->selected_meta = $request->except('applicants');
                $applicant->updated_at      = Carbon::now();
                $applicant->save();
            });
            \DB::commit();;
            return $this->sendSuccessResponse([], 'Candidate Onboard Successful.');
        } catch (\Exception $e) {
            \DB::rollBack();
            return $this->sendFailedResponse('Something went wrong');
        }
    }

    private function jobCreateRules()
    {
        return [
            'category'        => 'required',
            'title'           => 'string|required|max:150',
            'country'         => 'nullable',
            'state'           => 'nullable',
            'city'            => 'nullable',
            'expire_at'       => 'date|required',
            'details'         => 'required|string',
            'skills'          => 'required|array',
            'salary_type'     => 'required',
            'vacancies'       => 'nullable|digits_between:1,4',
            'salary'          => 'nullable|max:50',
            'office_time'     => 'nullable|max:200',
            'employment_type' => 'required',
            'employment_type_other' => 'nullable|string|max:50',
            'hideCoverPhoto' => 'nullable',
            'salary_range' => 'nullable|array'
        ];
    }

    private function serializeJobInformationData($jobData, $user, $jobPost = null)
    {
        $employmentTypeId  = data_get($jobData, 'employment_type.id');

        $meta              = !blank($jobPost) ? data_get($jobPost, 'meta', []) : [];
        $status            = data_get($jobPost, 'status', JobStatus::DRAFT);
        $meta['is_remote'] = isRemoteJob($jobData);
        $meta['hideCoverPhoto'] = boolval(data_get($jobData, 'hideCoverPhoto', 0));
        $meta['employment_type_other'] = $employmentTypeId == EmploymentType::OTHER ? data_get($jobData, 'employment_type_other', 'Other') : null;

        $data = [
            'company_id'            => auth('app')->user()->id,
            'title'                 => data_get($jobData, 'title'),
            'requirements'          => data_get($jobData, 'details'),
            'responsibilies'        => data_get($jobData, 'responsibilities'),
            'category_id'           => data_get($jobData, 'category.id'),
            'vacancies'             => data_get($jobData, 'vacancies'),
            'expire_at'             => Carbon::parse(data_get($jobData, 'expire_at'))->format('Y-m-d'),
            'salary_type'           => data_get($jobData, 'salary_type.id'),
            'employment_type'       => data_get($jobData, 'employment_type.id'),
            'experience_level'      => data_get($jobData, 'experience_level.id'),
            'office_time'           => data_get($jobData, 'office_time'),
            'show_on_job_board'     => (bool) data_get($jobData, 'show_on_job_board', 0),
            'show_on_career_page'   => (bool) data_get($jobData, 'show_on_career_page', 1),
            'show_company_benefits' => $jobData['has_benefits'] ?? false, // TODO: please fix this line
            'created_by'            => $user->id,
            'updated_by'            => $user->id,
            'meta'                  => $meta,
            'status'                => $status,
            'job_type' => data_get($jobData, 'job_type.id', JobType::ON_SITE)
        ];

        if (data_get($jobData, 'salary_field_type') == 1) {
            $data['salary_range'] = null;
            $data['salary']       = data_get($jobData, 'salary');
        } else {
            $data['salary']       = null;
            $data['salary_range'] = data_get($jobData, 'salary_range');
        }

        return $data;
    }

    private function serializeAddressData($addressData)
    {
        return [
            'country_id' => data_get($addressData, 'country.id'),
            'state_id'   => data_get($addressData, 'state.id'),
            'city_id'    => data_get($addressData, 'city.id'),
        ];
    }

    private function updateSkillsData($skills = [], $post)
    {
        $skillsIds = [];
        foreach ($skills as $key => $skill) {
            $newSkill    = Skill::updateOrCreate(['name' => $skill['name']], ['slug' => \Str::slug($skill['name'])]);
            $skillsIds[] = $newSkill->id;
        }

        return $post->skills()->sync($skillsIds);
    }

    public function searchCategory(Request $request, $categoryId = null)
    {
        $categoryService = app(CategoryService::class);
        $keyword     = $request->get('keyword');
        $company     = auth('app')->user();
        return $this->sendSuccessResponse($categoryService->searchCompanyCategory($company, $keyword));
    }

    public function searchSkill(Request $request)
    {
        $company = auth('app')->user();

        $options       = [];
        $keyword       = $request->get('keyword');
        $skills        = Skill::where(function ($query) use ($keyword) {
            if ($keyword) {
                $query->where('name', 'like', "%{$keyword}%");
            }
        })->where(function ($q) use ($company) {
            $q->whereNull('company_id')->orWhere('company_id', $company->company_id);
        })->orderBy('name', 'ASC')->get();
        $defaultSkills = $skills->where('company_id', null)->toArray();
        $customSkills  = $skills->where('company_id', $company->company_id)->toArray();

        if (!blank($customSkills)) {
            $options[] = [
                'group_name' => 'Custom skills',
                'opts'       => array_values($customSkills),
            ];
        }

        if (!blank($defaultSkills)) {
            $options[] = [
                'group_name' => 'Default skills',
                'opts'       => array_values($defaultSkills),
            ];
        }

        return $this->sendSuccessResponse($options);
    }

    public function jobInfoMetaData(Request $request)
    {
        $data      = [];
        $constants = trans('constants');

        foreach (data_get($constants, 'employment_type', []) as $key => $item) {
            $data['employment_type'][] = [
                'id'   => $key,
                'name' => $item,
            ];
        }

        foreach (data_get($constants, 'salary_type', []) as $key => $item) {
            $data['salary_type'][] = [
                'id'   => $key,
                'name' => $item,
            ];
        }

        foreach (data_get($constants, 'experience_level', []) as $key => $item) {
            $data['experience_level'][] = [
                'id'   => $key,
                'name' => $item,
            ];
        }

        return $this->sendSuccessResponse($data);
    }

    public function jobBasicInfo($job)
    {
        $company = auth('app')->user();
        $jobPost = JobPost::with('category', 'address')
            ->whereCompanyId($company->id)
            ->find($job);

        if (blank($jobPost)) {
            return $this->sendFailedResponse('Invalid job id');
        }

        $address = $jobPost->address;

        $employment_type = $experience_level = $salary_type = [];

        if ($jobPost->employment_type) {
            $employment_type = [
                'id'   => $jobPost->employment_type,
                'name' => __("constants.employment_type.{$jobPost->employment_type}"),
            ];
        }

        if ($jobPost->experience_level) {
            $experience_level = [
                'id'   => $jobPost->experience_level,
                'name' => __("constants.experience_level.{$jobPost->experience_level}"),
            ];
        }

        if ($jobPost->salary_type) {
            $salary_type = [
                'id'   => $jobPost->salary_type,
                'name' => __("constants.salary_type.{$jobPost->salary_type}"),
            ];
        }

        if ($jobPost->job_type) {
            $job_type = [
                'id'   => $jobPost->job_type,
                'name' => __("constants.job_type.{$jobPost->job_type}"),
            ];
        }

        $jobLink   = jobDetailsRoute($jobPost);
        $benefits  = data_get($jobPost, 'show_company_benefits', false);
        $basicData = [
            'id'               => $jobPost->id,
            'title'            => $jobPost->title,
            'slug'             => $jobPost->slug,
            'preview'          => jobPreviewRoute($jobPost),
            'banner_image'     => $jobPost->image,
            'details'          => $jobPost->requirements,
            'responsibilities' => $jobPost->responsibilies,
            'category'         => $jobPost->category()->first(['id', 'name']),
            'vacancies'        => $jobPost->vacancies,
            'country'          => $address ? $address->country()->first(['id', 'name']) : null,
            'state'            => $address ? $address->state()->first(['id', 'name']) : null,
            'city'             => $address ? $address->city()->first(['id', 'name']) : null,
            'is_remote'        => isRemoteJob($jobPost),
            'expire_at'        => $jobPost->expire_at,
            'skills'           => $jobPost->skills ? $jobPost->skills()->get(['id', 'name'])->toArray() : [],
            'office_time'      => $jobPost->getOfficeTime(),
            'benefits'         => $benefits,
            'has_benefits'     => $benefits,
            'employment_type'  => $employment_type,
            'experience_level' => $experience_level,
            'salary_type'      => $salary_type,
            'job_type'         => $job_type,
            'meta'             => $jobPost->meta,
            'status'           => $jobPost->status,
            'social_links'     => [
                'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=' . $jobLink . '/#',
                'linkedIn' => 'https://www.linkedin.com/sharing/share-offsite/?url=' . $jobLink,
                'twitter'  => 'https://twitter.com/intent/tweet?text=' . $jobLink . '/#',
            ],
            'job_link'         => $jobLink,
            'employment_type_other' => data_get($jobPost, 'meta.employment_type_other', null),
            'hideCoverPhoto' => (int)data_get($jobPost, 'meta.hideCoverPhoto', 0),
            'show_on_job_board' => (int)data_get($jobPost, 'show_on_job_board', 0),
            'show_on_career_page' => (int)data_get($jobPost, 'show_on_career_page', 1)
        ];

        if (!blank(data_get($jobPost, 'salary'))) {
            $basicData['salary'] = data_get($jobPost, 'salary');
            $basicData['salary_range'] = null;
        } else {
            $basicData['salary'] = null;
            $basicData['salary_range'] = data_get($jobPost, 'salary_range');
        }
        $basicData['salary_field_type'] = !blank($basicData['salary_range']) ? SalaryRangeType::RANGE->value : SalaryRangeType::SALARY->value;

        return $this->sendSuccessResponse($basicData);
    }

    private function sendJobUpdateNotification(JobPost $job)
    {
        $company = auth('app')->user();
        $ns      = new NotificationService($company);
        $ns->jobUpdateNotificationToManager($job, $company->creator);
        $es = new CompanyEmailService($company);
        $es->sendJobUpdateEmailToManagers($company->creator, $job);

    }

    public function getInvitations(JobPost $job, Request $request)
    {

        $company = auth('app')->user();

        if ($company->id !== $job->company_id) {
            return $this->sendFailedResponse(
                [],
                'Unauthorized Access',
                CustomResponseCode::UNAUTHORIZED_ACCESS
            );
        }
        $invitations = $job->invitations->map(function ($candidate) {
            return [
                'name'  => data_get($candidate, 'applicant_name'),
                'email' => data_get($candidate, 'applicant_email'),
            ];
        })->toArray();
        return $this->sendSuccessResponse($invitations);
    }


    public function addCandidate(JobPost $job, Request $request)
    {
        $company = auth('app')->user();
        if ($company->id !== $job->company_id) {
            return $this->sendFailedResponse(
                [],
                'Unauthorized Access',
                CustomResponseCode::UNAUTHORIZED_ACCESS
            );
        }
        $validator = \Validator::make($request->all(), [
            'email' => 'required | email',
        ]);
        if ($validator->fails()) {
            return $this->sendFailedResponse($validator->errors()->toArray());
        }
        $candidate     = User::where('email', data_get($request, 'email'))->first();
        $emailService  = new CompanyEmailService($job->company);
        $candidateName = '';
        if (!blank($candidate)) {
            $candidateName = data_get($candidate, 'name', '');
            $applicant     = $job->applicants()->where('user_id', $candidate->id)->whereNotIn('status', [JobApplyStatus::PENDING])->first();
            if (!blank($applicant)) {
                return $this->sendFailedResponse([], 'Applicant Already Applied For This Job');
            }
        }

        if (!$job->invitations()->where('applicant_email', data_get($request, 'email'))->first()) {
            $job->invitations()->create(['applicant_email' => $request->get('email'), 'applicant_name' => $candidateName]);
        }

        try {
            $emailService->sendJobApplyInviteEmail($request->get('email'), $job, $job->company->creator);
            return $this->sendSuccessResponse([], 'Invitation link is sent to candidate');
        } catch (\Exception $e) {
//            return $this->errorResponse([], $e->getMessage());
            return $this->sendFailedResponse('Something went wrong');
        }
    }

    public function deleteInvitation(JobPost $job, Request $request)
    {
        $company = auth('app')->user();
        if ($company->id !== $job->company_id) {
            return $this->sendFailedResponse(
                [],
                'Unauthorized Access',
                CustomResponseCode::UNAUTHORIZED_ACCESS
            );
        }
        $job->invitations()->where('applicant_email', $request->get('email'))->delete();
        return $this->sendSuccessResponse([], 'Invitation removed');
    }

    private function getFilteredCandidate($job, $request)
    {
        $search            = $request->get('search');
        $selectedPipelines = $request->get('pipeline', []);
        $selectedFilter    = $request->get('basic', []);
        $sortBy            = $request->get('sortBy');
        $query             = $job->applicants()->where('status', '!=', JobApplyStatus::PENDING);

        if (!blank($search)) {
            $query->whereHas('user', function ($user) use ($search) {
                $user->where('name', 'like', "%{$search}%");
            });
        }
        if (!blank($selectedPipelines)) {
            $selectedPipelines = explode(',', $selectedPipelines);
            $query->whereIn('job_pipeline_id', $selectedPipelines);
        }
        if (!blank($selectedFilter)) {
            if (in_array(self::FILTER_NEW_CANDIDATE, $selectedFilter)) {
                $to   = Carbon::now(getTimeZone())->endOfDay()->toDateTimeString();
                $form = Carbon::now(getTimeZone())->startOfDay()->subDays(7)->toDateTimeString();
                $query->whereBetween('updated_at', [$form, $to]);
            }

            $searchRated    = in_array(self::FILTER_RATED_CANDIDATE, $selectedFilter);
            $searchNotRated = in_array(self::FILTER_NON_RATED_CANDIDATE, $selectedFilter);
            if (!$searchRated || !$searchNotRated) {
                if ($searchRated) {
                    $query->where('rating', '>', 0);
                }
                if ($searchNotRated) {
                    $query->where('rating', 0);
                }
            }
//            $searchWithAssessment = in_array(self::FILTER_WITH_ASSESSMENT, $selectedFilter);
//            $searchWithOutAssessment = in_array(self::FILTER_WITHOUT_ASSESSMENT, $selectedFilter);
//            if (!$searchWithAssessment || !$searchWithOutAssessment) {
//                if ($searchWithAssessment) {
//                    $query->whereHas('assessments');
//                }
//                if ($searchWithOutAssessment) {
//                    $query->has('assessments', '==', 0);
//                }
//            }
        }

        switch ($sortBy) {
            case self::SORT_BY_EXPERIENCE:
                $query->orderBy('total_experience', 'desc');
                break;
            case self::SORT_BY_EXPERIENCE_MATCH:
                $query->orderBy('experience_score', 'desc');
                break;
            case self::SORT_BY_EDUCATION_MATCH:
                $query->orderBy('education_score', 'desc');
                break;
            case self::SORT_BY_SKILL_MATCH:
                $query->orderBy('skills_score', 'desc');
                break;
            case self::SORT_BY_TOTAL_AI_SCORE:
                $query->orderBy('final_ai_score', 'desc');
                break;
            case self::SORT_BY_QUIZ_SCORE:
                $query->orderBy('quiz_marks_obtain', 'desc');
                break;
            default;
                $query->orderBy('submitted_at', 'desc');
        }
        return $query->get()->load(['user', 'job']);
    }

    public function exportCandidateLink(JobPost $job, Request $request)
    {
        $company = auth('app')->user();
        if ($company->id !== $job->company_id) {
            return $this->sendFailedResponse(
                [],
                'Unauthorized Access',
                CustomResponseCode::UNAUTHORIZED_ACCESS
            );
        }
        $data          = $request->all();
        $data ['job']  = $job->id;
        $data['token'] = $request->bearerToken();
        return $this->sendSuccessResponse(['url' => route('wp.job.export', $data)], 'Success');

    }

    public function exportCandidate(JobPost $job, Request $request)
    {
        $company = auth('app')->user();
        if (blank($company) || $company->company_id !== $job->company_id) {
            return $this->sendFailedResponse(
                [],
                'Unauthorized Access',
                CustomResponseCode::UNAUTHORIZED_ACCESS
            );
        }
        $candidates = $this->getFilteredCandidate($job, $request);
        $company    = $job->company;
        //$ai_setup   = data_get($company, 'meta.ai_setup.enabled', false);
        $ai_setup   = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::AI_SCORE_ENABLE);
        try {
            $customFields = data_get($job, 'custom_fields', []);
            $export       = new JobApplicantExport($candidates, $ai_setup, $customFields);
            $localPath    = "public/exports/$job->id.xls";
            Excel::store($export, $localPath, 'local', \Maatwebsite\Excel\Excel::XLS);
            $path = storage_path("app/" . $localPath);
            return response()->download($path, "$job->title.xls")->deleteFileAfterSend();
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            return $this->sendFailedResponse([], $e->getMessage());
        }
    }

    public function getJobTemplates(Request $request)
    {
        $title    = $request->get('title');
        $industry = $request->get('industry_id');
        $type     = $request->get('type');
        $query    = JobPostTemplate::where('status', 1);
        if (!blank($title)) {
            $query->where(function ($q) use ($title) {
                $q->where('title', 'like', '%' . $title . '%')
                    ->orWhere('tags', 'like', '%' . $title . '%');
            });
        }

        if (!blank($industry)) {
            $name = CompanyType::find((int)$industry)->name;
            $query->where(function ($q) use ($name) {
                $q->where('tags', 'like', '%' . $name . '%')
                    ->orWhere('title', 'like', '%' . $name . '%');
            });
        } elseif (!blank($type)) {
            $name     = CompanyType::find((int)$type)->name;
            $newQuery = JobPostTemplate::where('status', 1)
                ->where(function ($q) use ($name) {
                    $q->where('tags', 'like', '%' . $name . '%')
                        ->orWhere('title', 'like', '%' . $name . '%');
                });

            if ($newQuery->exists()) {
                $templates = $newQuery->paginate(5);
                $templates->transform(function ($template) {
                    return $this->serializeTemplate($template);
                });

                if ($templates->getCollection()->count() == 0) {
                    $templates = JobPostTemplate::where('id', 6)->paginate(5);
                    $templates->transform(function ($template) {
                        return $this->serializeTemplate($template);
                    });
                }

                $data = [
                    'templates' => $templates,
                    'industry'  => CompanyType::find($type)->only('id', 'name'),
                ];

                return $this->sendSuccessResponse($data);
            }
        }

        $templates = $query->paginate(5);
        $templates->transform(function ($template) {
            return $this->serializeTemplate($template);
        });

        if ($templates->getCollection()->count() == 0) {
            $templates = JobPostTemplate::where('id', 6)->paginate(5);
            $templates->transform(function ($template) {
                return $this->serializeTemplate($template);
            });
        }

        $data = [
            'templates' => $templates,
            'industry'  => null,
        ];

        return $this->sendSuccessResponse($data);
    }

    private function serializeTemplate($template)
    {
        $date = data_get($template, 'details.expire_at');
        if (blank($date)) {
            $expireDate = '';
        } else {
            $expireDate = Carbon::createFromFormat('d/m/Y', $date) > Carbon::now()->addDay() ? Carbon::createFromFormat('d/m/Y', $date)->format('m/d/Y') : '';
        }
        return [
            'title'            => data_get($template, 'title'),
            'details'          => data_get($template, 'details.details'),
            'responsibilities' => data_get($template, 'details.responsibilities'),
            'vacancies'        => data_get($template, 'details.vacancies'),
            'expire_at'        => $expireDate,
            'salary_type'      => data_get($template, 'details.salary_type'),
            'employment_type'  => data_get($template, 'details.employment_type'),
            'experience_level' => data_get($template, 'details.experience_level'),
            'salary'           => data_get($template, 'details.salary'),
            'office_time'      => data_get($template, 'details.office_time'),
            'category'         => data_get($template, 'details.category'),
            'skills'           => data_get($template, 'details.skills'),
            'country'          => data_get($template, 'details.country'),
            'state'            => data_get($template, 'details.state'),
            'city'             => data_get($template, 'details.city'),
        ];

    }

    public function deleteCandidate(JobPost $job, Request $request)
    {
        $company = auth('app')->user();

        if ($company->id !== $job->company_id) {
            return $this->sendUnauthorisedResponse();
        }

        $candidates = $request->get('candidates');

        try {
            if (!blank($candidates)) {
                $query = $job->applicants()->whereIn('id', $candidates);
                QuestionAnswer::whereIn('job_applicant_id', $candidates)->delete();
                ApplicantAssessment::whereIn('job_applicant_id', $candidates)->delete();
                $query->delete();
            }

            return $this->sendSuccessResponse([], 'Job applicant(s) removed.');

        } catch (\Exception $e) {
            \Log::error($e);
            return $this->sendFailedResponse('Job applicant(s) removed.');
        }


    }

    private function jobPostPackagePermissionAndPackageExpireCheck($user, $jobPostStatus, $jobPost = null): array
    {
        $message = null;

        if(!blank($jobPost) && in_array($jobPost->status, [JobStatus::PUBLISHED, JobStatus::REPUBLISHED])) {
            return [false, $message];
        }

        if ($jobPostStatus != JobStatus::PUBLISHED) {
            return [false, $message];
        }

        if ($user->package_validity < Carbon::now()->startOfDay()) {
            $freePackage         = Package::whereRaw("LOWER(name) = 'free'")->first();
            $user->package_rules = $freePackage->rule;
        }

        if (!app(PackageService::class)->hasPackagePermission(PackageRule::ACTIVE_JOBS, $user)) {
            $error = data_get(__("subscription.package.errors"), PackageRule::ACTIVE_JOBS, '');
            return [true, $error];
        }

        return [false, $message];

    }
}
