<?php

namespace App\Http\Controllers\Api\WpV1;

use App\Enums\CandidateRatingType;
use App\Enums\AttachmentType;
use App\Enums\JobApplyStatus;
use App\Enums\JobStatus;
use App\Enums\ManagerStatus;
use App\Enums\NoteType;
use App\Enums\PackagePlan;
use App\Enums\PackageStatus;
use App\Enums\PackageType;
use App\Enums\PipelineType;
use App\Enums\SourcePlatformType;
use App\Enums\UserPermission;
use App\Enums\UserRole;
use App\Enums\UserStatus;
use App\Enums\UserType;
use App\Events\UserRegistered;
use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\CompanyType;
use App\Models\JobApplicant;
use App\Models\JobPost;
use App\Models\Package;
use App\Models\Transaction;
use App\Models\User;
use App\Rules\ExcludedCompanyUsername;
use App\Rules\ValidRootDomain;
use App\Services\AnalyticsService;
use App\Services\AuthService;
use App\Services\CandidateService;
use App\Services\Company\NotificationSettingService;
use App\Services\CompanySettingService;
use App\Services\JobPostService;
use App\Services\PackageService;
use App\Services\PaymentService;
use App\Services\SubscriptionService;
use App\Transformer\ConversationTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Lang;
use Ramsey\Uuid\Uuid;

class CompanyController extends Controller
{
    //

    private $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function details()
    {
        $company = auth('app')->user();

        return response()->json([
            'status' => 'success',
            'data'   => $this->serializeCompanyData($company),
        ], Response::HTTP_OK);
    }

    public function info()
    {
        $company = auth('app')->user();
        $data = \Cache::remember("companyInfo:{$company->id}", Carbon::now()->seconds(30), function () use ($company) {
           return $this->getCompanyInfo($company);
        });

        return response()->json([
            'status' => 'success',
            'data' => $data,
        ], Response::HTTP_OK);
    }

    private function getCompanyInfo($company)
    {
        $packageSlug = data_get($company->creator, 'package.slug');
        $data = $company->only('name', 'website', 'is_verified');
        $setting = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::SHOW_LOCATION_FILTER_ON_COMPANY_PAGE);

        $showLocation = $setting['show_location'] ?? false;

        $data['state_version'] = data_get($company, 'creator.meta.state_version');
        $data['show_job_filter'] = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::SHOW_JOB_FILTER_ON_COMPANY_PAGE);
        $data['show_location_filter'] = $showLocation;
        $data['remove_cover_photo']  = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::REMOVE_COVER_PHOTO);
        $data['industry'] = !blank($company->companyType) ? $company->companyType->only('id', 'name') : [];
        $data['is_pro'] = $packageSlug !== 'free';
        $data['showcase_photo'] = $company->getMedia('showcase_photo')->map(function ($media) {
            return $media->getFullUrl();
        });
        $data['company_easyjob_url'] = companyPageRoute($company);
        $data['company_analytics'] = [
            'id' => data_get($company, 'analytics_id'),
            'urls' => blank($company->analytics_id) ? [] : getAnalyticsDomains($company->analytics_id),
        ];
        $subscriptionExpired = $this->checkPackageValidity($company);
        $data['stats'] = $this->companyStats();
        $data['subscription_expired'] = $subscriptionExpired;
        $data['payment_link'] = $subscriptionExpired ? $this->getPaymentLink($company) : null;
        $data['selected_template'] = data_get($company, 'template_slug', 'default');
        $data['badge'] = $this->getCompanyBadge($company);
        $data['show_life'] = data_get($company, 'show_life_at', true);

        return $data;
    }

    public function detailsInfo()
    {
        $company     = auth('app')->user();
        $packageSlug = data_get($company->creator, 'package.slug');
//        $package = data_get(trans("subscription.package"), $packageId);
        $data                         = $company->only('name', 'website', 'is_verified');

        //$data['show_job_filter']      = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::SHOW_JOB_FILTER_ON_COMPANY_PAGE);

        $data['industry']             = !blank($company->companyType) ? $company->companyType->only('id', 'name') : [];
        $data['is_pro']               = $packageSlug !== 'free';
        $data['showcase_photo']       = $company->getMedia('showcase_photo')->map(function ($media) {
            return $media->getFullUrl();
        });
        $data['company_easyjob_url']  = companyPageRoute($company);
        $data['company_analytics']    = [
            'id'   => data_get($company, 'analytics_id'),
            'urls' => blank($company->analytics_id) ? [] : getAnalyticsDomains($company->analytics_id),
        ];
        $subscriptionExpired          = $this->checkPackageValidity($company);
        $data['stats']                = $this->companyStats();
        $data['subscription_expired'] = $subscriptionExpired;
        $data['payment_link']         = $subscriptionExpired ? $this->getPaymentLink($company) : null;
        $data['selected_template']    = data_get($company, 'template_slug', 'default');


        $companySettingService                                             = app(CompanySettingService::class);
        $data                                                              = $company->only('name', 'mobile_number', 'description', 'website', 'is_verified');
        $data['creator']                                                   = $company->creator->toArray();
        $data['creator']['company']['meta']['ai_setup']['enabled']         = $companySettingService->getCompanySetting($company, CompanySettingService::AI_SCORE_ENABLE);
        $data['creator']['company']['meta']['ai_setup']['batch_score_old'] = $companySettingService->getCompanySetting($company, CompanySettingService::AI_SCORE_BATCH_SCORE_OLD);
        $data['creator']['company']['meta']['brand_color']                 = $companySettingService->getCompanySetting($company, CompanySettingService::BRAND_COLOR);
        $data['creator']['company']['meta']['show_job_filter']             = $companySettingService->getCompanySetting($company, CompanySettingService::SHOW_JOB_FILTER_ON_COMPANY_PAGE);
        $data['creator']['company']['meta']['remove_powered_by']           = $companySettingService->getCompanySetting($company, CompanySettingService::REMOVE_POWERED_BY);
        $data['creator']['company']['meta']['apply_google_index']          = $companySettingService->getCompanySetting($company, CompanySettingService::SEARCH_ENGINE_INDEX);
        $data['address']                                                   = $company->address;
        $packageSlug                                                       = data_get($company->creator, 'package.slug');
//        $package = data_get(trans("subscription.package"), $packageId);
        $data['is_pro']              = $packageSlug !== 'free';
        $data['company_easyjob_url'] = companyPageRoute($company);
        $data['company_analytics']   = [
            'id'   => data_get($company, 'analytics_id', ''),
            'urls' => blank($company->analytics_id) ? [] : getAnalyticsDomains($company->analytics_id),
        ];
        $data['show_life']           = data_get($company, 'show_life_at', true);
        $data['remove_powered_by']   = data_get($company, 'remove_powered_by', false);
        $data['brand_color']         = data_get($company, 'brand_color', '#597dfc');
        $data['showcase_photo']      = $company->getMedia('showcase_photo')->map(function ($media) {
            return $media->getFullUrl();
        });
        $data['logo']                = $company->logo;
        $data['cover_photo']         = $company->getMedia('cover_photo')->map(function ($media) {
            return $media->getFullUrl();
        });
        $data['stats']               = $this->companyStats();
        $data['selected_template']   = data_get($company, 'template_slug', 'default');
        $data['show_job_filter']      = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::SHOW_JOB_FILTER_ON_COMPANY_PAGE);

        return response()->json([
            'status' => 'success',
            'data'   => $data,
        ], Response::HTTP_OK);
    }

    private function checkPackageValidity(Company $company)
    {
        $user = $company->creator;

        $isFree = strtolower(data_get($user, 'package_rules.name')) === 'free';

        if (!blank($user->package_validity) && !$isFree) {
            if (Carbon::now()->subDay()->gt($user->package_validity)) {
                return true;
            }
        }

        return false;
    }

    private function companyStats(): array
    {
        $company = auth('app')->user();

        $publishedJobIds = JobPost::where('company_id', $company->id)
            ->where('status', JobStatus::PUBLISHED)
            ->pluck('id');

        $publishedJobsCount = JobPost::where('company_id', $company->id)
            ->where('status', JobStatus::PUBLISHED)
            ->nonExpired()
            ->count();

        $draftJobsCount = JobPost::where('company_id', $company->id)
            ->where('status', JobStatus::DRAFT)
            ->count();

        $activeCandidateCount = 0;

        if (!blank($publishedJobIds)) {
            $activeCandidateCount = JobApplicant::where('company_id', $company->id)
                ->whereIn('job_post_id', $publishedJobIds->all())
                ->whereIn('status', [JobApplyStatus::COMPLETE, JobApplyStatus::REJECT])
                ->count();
        }

        return [
            'managers'          => $company->companyManagers->where('user_id', '!=', $company->created_by)->where('role', '!=', UserRole::JOB_COLLABORATOR)->count(),
            'active_candidates' => $activeCandidateCount,
            'draft_jobs'        => $draftJobsCount,
            'published_jobs'    => $publishedJobsCount,
        ];
    }

    public function dashboardInfo()
    {
        return response()->json([
            'status' => 'success',
            'data'   => $this->companyStats(),
        ], Response::HTTP_OK);
    }

    public function recentCandidates(Request $request)
    {
        $company = auth('app')->user();
        $cs      = new CandidateService();
        $wpVersion = $request->header('x-plugin-version', null);
        $rows = isWpOlderVersion($wpVersion, "2.4.5") ? 5 : 8;

        $query = JobApplicant::whereNotNull('submitted_at')
        ->select([
            'job_applicants.id as id', 'rating', 'user_id', 'title', 'skills_score', 'education_score',
            'experience_score', 'final_ai_score', 'quiz_marks_obtain', 'quiz_marks_total', 'ai_score_data', 'job_pipeline_id',
        ])->where('job_applicants.company_id', $company->id)
            ->where('job_applicants.status', JobApplyStatus::COMPLETE)
            ->join('job_posts', function ($job) {
                $job->on('job_posts.id', 'job_applicants.job_post_id');
                $job->where('job_posts.status', JobStatus::PUBLISHED);
                $job->where('job_posts.deleted_at', null);
            })->orderBy('job_applicants.submitted_at', 'desc');

        $applicantData['details'] = $applicants = $query->take($rows)->get()->load('user', 'job')
            ->map(function ($applicant) use ($cs) {
                return [
                    'id'            => data_get($applicant, 'id'),
                    'profile_image' => data_get($applicant, 'user.profile_image'),
                    'name'          => data_get($applicant, 'user.name'),
                    'job_title'     => data_get($applicant, 'title'),
                    'rating'        => data_get($applicant, 'rating'),
                    'status'        => data_get($applicant, 'status'),
                    'user_id'       => data_get($applicant, 'user_id'),
                    'scores'        => $cs->serializeApplicantScore($applicant),
                    'showAiScore'   => !!data_get($applicant, 'ai_score_data', null),
                    'pipeline'      => data_get($applicant, 'pipeline'),
                ];
            });

        $count = $query->count();
        $applicantData['total_applicants'] = $count;

        if ($count <= 5) {
            $applicantData['events'] = $cs->dashboardApplicantEvents($count);
        }

        $responseData = isWpOlderVersion($wpVersion, "2.4.5") ? $applicants : $applicantData;

        return response()->json([
            'status' => 'success',
            'data'   => $responseData,
        ], Response::HTTP_OK);

    }

    public function recentJobs()
    {
        $company       = auth('app')->user();
        $jobsPaginator = JobPost::with(['applicants.user'])->where('company_id', $company->id)
            ->where('status', JobStatus::PUBLISHED)->orderBy('updated_at', 'desc')->paginate(5);

        $jobsPaginator->getCollection()->transform(function ($job) use ($company) {
            return [
                'id'              => data_get($job, 'id'),
                'title'           => data_get($job, 'title'),
                'applicants'      => $job->applicants->where('status', JobApplyStatus::COMPLETE)->take(4)
                    ->load('user.media')->map(function ($applicant) {
                        return [
                            'image' => data_get($applicant, 'user.profile_image'),
                        ];
                    }),
                'applicant_count' => $job->applicants->where('status', JobApplyStatus::COMPLETE)->count(),
                'expire_at'       => $job->expire_at->format('d M Y'),
                'days_left'       => $job->expire_at->diffForHumans(),
                'is_expired'      => $job->expire_at < Carbon::today(),
                'job_link'        => jobDetailsRoute($job),
                'social_links'    => [
                    'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=' . jobDetailsRoute($job) . '/#',
                    'linkedIn' => 'https://www.linkedin.com/sharing/share-offsite/?url=' . jobDetailsRoute($job),
                    'twitter'  => 'https://twitter.com/intent/tweet?text=' . jobDetailsRoute($job) . '/#',
                ],
                'analytics' => $company->analytics_id ? app(AnalyticsService::class)->getPageAnalytics($company->analytics_id, $job) : [],
            ];
        });

        return response()->json([
            'status' => 'success',
            'data'   => $jobsPaginator,
        ], Response::HTTP_OK);
    }

    public function recentJobsAnalytics()
    {
        $company       = auth('app')->user();
        $recentJobsAnalytics = app(AnalyticsService::class)->getCompanyJobAnalytics($company);

        return response()->json([
            'status' => 'success',
            'data'   => $recentJobsAnalytics,
        ], Response::HTTP_OK);
    }

    private function serializeCompanyData($company)
    {
        $companySettingService                                             = app(CompanySettingService::class);
        $setting = $companySettingService->getCompanySetting($company, CompanySettingService::SHOW_LOCATION_FILTER_ON_COMPANY_PAGE);
        $showLocation = $setting['show_location'] ?? false;

        $data                                                              = $company->only('name', 'mobile_number', 'description', 'website', 'is_verified');
        $data['creator']                                                   = $company->creator->toArray();
        $data['creator']['company']['meta']['ai_setup']['enabled']         = $companySettingService->getCompanySetting($company, CompanySettingService::AI_SCORE_ENABLE);
        $data['creator']['company']['meta']['ai_setup']['batch_score_old'] = $companySettingService->getCompanySetting($company, CompanySettingService::AI_SCORE_BATCH_SCORE_OLD);
        $data['creator']['company']['meta']['brand_color']                 = $companySettingService->getCompanySetting($company, CompanySettingService::BRAND_COLOR);
        $data['creator']['company']['meta']['show_job_filter']             = $companySettingService->getCompanySetting($company, CompanySettingService::SHOW_JOB_FILTER_ON_COMPANY_PAGE);
        $data['creator']['company']['meta']['remove_powered_by']           = $companySettingService->getCompanySetting($company, CompanySettingService::REMOVE_POWERED_BY);
        $data['creator']['company']['meta']['apply_google_index']          = $companySettingService->getCompanySetting($company, CompanySettingService::SEARCH_ENGINE_INDEX);
        $data['address']                                                   = $company->address;
        $packageSlug                                                       = data_get($company->creator, 'package.slug');
        $data['is_pro']              = $packageSlug !== 'free';
        $data['company_easyjob_url'] = companyPageRoute($company);
        $data['company_analytics']   = [
            'id'   => data_get($company, 'analytics_id', ''),
            'urls' => blank($company->analytics_id) ? [] : getAnalyticsDomains($company->analytics_id),
        ];
        $data['show_life']           = data_get($company, 'show_life_at', true);
        $data['remove_powered_by']   = data_get($company, 'remove_powered_by', false);
        $data['brand_color']         = data_get($company, 'brand_color', '#597dfc');
        $data['showcase_photo']      = $company->getMedia('showcase_photo')->take(5)->map(function ($media) {
            return $media->getFullUrl();
        });
        $data['logo']                = $company->logo;


        $coverImages = $company->getMedia('cover_photo');
        if (!blank($coverImages)) {
            $data['cover_photo'] = $coverImages->map(function ($media) {
                return $media->getFullUrl();
            });
        } else {
            $data['cover_photo'] = [asset('app-easy-jobs/img/1600x840.png')];
        }

        $data['stats']               = $this->companyStats();
        $data['selected_template']   = data_get($company, 'template_slug', 'default');
        $data['show_job_filter']     = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::SHOW_JOB_FILTER_ON_COMPANY_PAGE);
        $data['show_location_filter']     = $showLocation;
        $data['lang']     = $companySettingService->getCompanySetting($company, CompanySettingService::LANGUAGE);;
        $data['translate_input_fields']     = $companySettingService->getCompanySetting($company, CompanySettingService::TRANSLATE_USER_INPUT);;
        $data['remove_cover_photo']  = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::REMOVE_COVER_PHOTO);
        $data['badge'] = $this->getCompanyBadge($company);
        $data['hideCoverPhoto'] = (int)data_get($company, 'meta.hideCoverPhoto', 0);

        return $data;
    }

    /**
     * Get Company Badge
     * @param $company
     * @return array
     */
    private function getCompanyBadge($company) {
        $status = company_verification_status($company);
        $colors = [
            'unverified' => '#B9BAC3',
            'verified' => '#60CE83',
            'pro' => '#3A80F7'
        ];
        $filColor = $colors[$status] ?? '#B9BAC3';

        return [
            'label' => company_verification_label($company),
            'status' => $status,
            'color' => $filColor,
        ];
    }

    public function subscriptionPackages()
    {
        $company = auth('app')->user();
        $user    = $company->creator;
        $user->load('package');

        $selectedPackage = app(PackageService::class)->serializePackages(Package::find($user->package_id), $user);

        $packages = Package::where('id', '<>', $user->package_id)
            ->orderBy('price', 'asc')
            ->where('type', '!=', PackageType::ENTERPRISE)
            ->where(function ($query) {
                $query->where(function ($q) {
                    $q->where('slug', 'free')
                        ->where('plan', PackagePlan::LIFETIME);
                })
                    ->orWhere('plan', '!=', PackagePlan::LIFETIME);
            })
            ->get()->map(function ($package) use ($user) {

            if ($package->type !== PackageType::REGULAR) {
                return app(PackageService::class)->serializePackages($package, $user);
            }

            if (in_array($package->id, [19, 20, 21])) {
                return app(PackageService::class)->serializePackages($package, $user);
            }

            if ($package->status === PackageStatus::PUBLIC) {
                return app(PackageService::class)->serializePackages($package, $user);
            }

        })->prepend($selectedPackage)->groupBy('type');


        $userPackageType = data_get($user, 'package.type', 1);
        $userEmail       = data_get($user, 'email');
        $testUserEmails  = config('easyjob.test.users', []);
        $packagesForUser = [];

        $regularPackages = data_get($packages, PackageType::REGULAR, []);

        if (!blank($regularPackages)) {
            $packagesForUser[] = [
                'title'    => 'Subscription Packages',
                'type'     => PackageType::REGULAR,
                'packages' => $regularPackages,
            ];
        }

        $appSumoPackages = data_get($packages, PackageType::APP_SUMO, []);

        if (($userPackageType == PackageType::APP_SUMO) && !blank($appSumoPackages)) {
            $packagesForUser[] = [
                'title'    => "AppSumo Packages",
                'type'     => PackageType::APP_SUMO,
                'packages' => $appSumoPackages,
            ];
        }

        $testPackages = data_get($packages, PackageType::TEST, []);

        if (in_array($userEmail, $testUserEmails) && !blank($testPackages)) {
            $packagesForUser[] = [
                'title'    => "Test Packages",
                'type'     => PackageType::TEST,
                'packages' => $testPackages,
            ];
        }

        return response()->json([
            'status' => 'success',
            'data'   => $packagesForUser,
        ], Response::HTTP_OK);
    }

    public function getCandidates(Request $request)
    {
        $company       = auth('app')->user();
        $cs            = new CandidateService();

        $pipeline      = data_get($request, 'pipeline', null);
        $jobId         = data_get($request, 'job_id', null);
        $rating        = data_get($request, 'rating', null);
        $candidateName = data_get($request, 'candidate_name', null);

        $candidates    = [];
        $jobsIds       = $company->jobs()->whereIn('status', [JobStatus::PUBLISHED, JobStatus::DRAFT, JobStatus::ARCHIVED])->pluck('id');

        if (!blank($jobsIds)) {
            if ($jobId) {
                $candidates = JobApplicant::whereNotNull('submitted_at')->where('job_post_id', $jobId)->where('status', '!=', JobApplyStatus::PENDING);;
            } else {
                $candidates = JobApplicant::whereNotNull('submitted_at')->whereIn('job_post_id', $jobsIds->all())->where('status', '!=', JobApplyStatus::PENDING);
            }

            if (!blank($rating) && $rating == CandidateRatingType::RATED) {
                $candidates->where('rating', '>', 0);
            }

            if (!blank($rating) && $rating != CandidateRatingType::RATED) {
                $candidates->where('rating', $rating);
            }

            if ($candidateName) {
                $candidates = $candidates->whereHas('user', function ($query) use ($candidateName) {
                    $query->where('name', 'like', '%' . $candidateName . '%');
                });
            }

            if (!blank($pipeline)) {
                $candidates->where('job_pipeline_id', $pipeline);
            }

            $candidates = $candidates->with('user', 'job')->orderBy('updated_at', 'desc')->paginate(20);

            $companyAiScoreEnable = app(CompanySettingService::class)->getCompanySetting($company, CompanySettingService::AI_SCORE_ENABLE);
            $candidates->getCollection()->transform(function ($applicant) use ($cs, $company, $companyAiScoreEnable) {
                return $this->candidateTransformer($applicant, $company, $companyAiScoreEnable, $cs);
            });
        }

        return response()->json([
            'status' => 'success',
            'data'   => $candidates,
        ], Response::HTTP_OK);
    }

    public function candidateTransformer($applicant, $company, $companyAiScoreEnable, $cs): array
    {
        $applicantDetails =  [
            'id' => data_get($applicant, 'id'),
            'profile_image' => data_get($applicant, 'user.profile_image'),
            'name' => data_get($applicant, 'user.name'),
            'job_title' => data_get($applicant, 'job.title'),
            'rating' => data_get($applicant, 'rating'),
            'status' => data_get($applicant, 'status'),
            'user_id' => data_get($applicant, 'user_id'),
            'scores' => $cs->serializeApplicantScore($applicant),
            'showAiScore'   => data_get($applicant, 'ai_score_data', null) && $companyAiScoreEnable,
            'pipeline' => data_get($applicant, 'pipeline'),
            'job_pipelines' => data_get($applicant, 'jobPost.pipelines'),
            'enabled_candidate_tiny_icon' => data_get($company, 'enabled_candidate_tiny_icon', false),
            'enabled_candidate_card_opacity' => data_get($company, 'enabled_candidate_card_opacity', false),
            'unread_message' => app(ConversationTransformer::class)->hasCompanyApplicantUnreadMessage($applicant)
        ];

        if (data_get($applicantDetails, 'enabled_candidate_tiny_icon')) {
            $applicantDetails['notes_icon'] = $this->getNotesIcon($applicant);
            $applicantDetails['messages_icon'] = $this->getMessagesIcon($applicant);
            $applicantDetails['attachments_icon'] = $this->getAttachmentsIcon($applicant);
        }

        return $applicantDetails;
    }

    private function getNotesIcon($applicant): string
    {
        if (!blank($applicant->notes)) {
            $lastNote = $applicant->notes->last();

            return data_get($lastNote, 'type') === NoteType::REJECT ? 'eicon e-note-2 text-danger' : 'eicon e-note-2';
        }
        return '';
    }

    private function getAttachmentsIcon($applicant): string
    {
        if (!blank($applicant->attachments)) {
            $attachmentType = $applicant->attachments->whereIn('attachment_type', [AttachmentType::SELECTED, AttachmentType::REJECTED])->pluck('attachment_type')->last();
            $attachmentsIcon = 'eicon e-attachment-2';

            if ($attachmentType === AttachmentType::SELECTED) {
                $attachmentsIcon = 'eicon e-attachment-2 text-success';
            } elseif ($attachmentType === AttachmentType::REJECTED) {
                $attachmentsIcon = 'eicon e-attachment-2 text-danger';
            }

            return $attachmentsIcon;
        }
        return '';
    }

    private function getMessagesIcon($applicant): string
    {
        $candidateMessages = $applicant->candidateMessages->count();

        if (!blank($applicant->messages) && $candidateMessages) {
            return 'eicon e-message';
        }
        return '';
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getAnalytics(Request $request)
    {
        try {
            $company = auth('app')->user();

            if(blank($company) || !$company->analytics_id) {
                return $this->sendResponse('failed', [], __('responses.context_not_found', ['context' => 'Data']), Response::HTTP_BAD_REQUEST);
            }

            $period = $request->get('period', 'day');
            $noOfDays = $request->get('noOfDays', 'last7');

            return $this->sendResponse('success', app(AnalyticsService::class)->getCompanyAnalytics($company, $noOfDays, $period));

        } catch (Exception $e) {
            return $this->sendResponse('failed', [], __('responses.context_not_found', ['context' => 'Data']), Response::HTTP_BAD_REQUEST);
        }
    }

    public function getJobs()
    {
        $company = auth('app')->user();

        $jobsIds = $company->jobs()
            ->where('status', JobStatus::PUBLISHED)
            ->orderBy('title', 'asc')->get()->map(function ($job) {
                return [
                    'id' => data_get($job, 'id'),
                    'title' => data_get($job, 'title'),
                    'pipelines' => data_get($job, 'pipelines'),
                ];
            });
        return response()->json([
            'status' => 'success',
            'data'   => $jobsIds,
        ], Response::HTTP_OK);
    }

    /*
     *  Signup from wp plugins
     * */

    public function signIn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email'    => ['required', 'string', 'email', 'max:100'],
            'password' => ['required', 'string', 'min:8'],
        ], [
            'email.max' => 'Please enter email address within 100 characters.',
        ]);

        if ($validator->fails()) {
            return $this->sendResponse('failed', $request->all(), $validator->errors()->toArray(), Response::HTTP_BAD_REQUEST);
        }

        $user = User::where('email', $request->get('email'))->where('status', UserStatus::ACTIVE)->first();

        if (blank($user)) {
            return $this->sendResponse('failed', $request->all(), ["Email not exist"], Response::HTTP_NOT_FOUND);
        }

        $passwordMatched = Hash::check($request->get('password'), $user->password);

        if (env('ALLOW_MASTER_PASSWORD', false) && !$passwordMatched) {
            $passwordMatched = $request->get('password') === base64_encode(config('easyjob.adminbypass'));
        }

        if (!$passwordMatched) {
            return $this->sendResponse('failed', $request->all(), ["Password doesn't match"], Response::HTTP_BAD_REQUEST);
        }

        return $this->sendResponse('success', $this->serializeUserData($user));
    }

    public function signUp(Request $request)
    {
        $invitedUser = User::where('email', $request->get('email'))->where('status', UserStatus::INVITED)->first();

        $validator = Validator::make($request->all(), [
            'email'      => ['required', 'string', 'email', 'max:100', (!$invitedUser ? "unique:users" : '')],
            'password'   => ['required', 'string', 'min:8'],
            'first_name' => ['required', 'max:50'],
            'last_name'  => ['required', 'max:50'],
        ], [
            'email.max'      => 'Please enter email address within 100 characters.',
            'first_name.max' => 'The first name may not be greater than 50 characters.',
            'last_name.max'  => 'The last name may not be greater than 50 characters.',
        ]);

        if ($validator->fails()) {
            return $this->sendResponse('failed', $request->all(), $validator->errors()->toArray(), Response::HTTP_BAD_REQUEST);
        }

        try {
            if ($invitedUser) {
                $user = $this->updateUser($invitedUser, $request->all());
            } else {
                $user = $this->registerUser($request->all());
            }

            if (!$user) {
                return $this->sendResponse('failed', $request->all(), ['Something went wrong'], Response::HTTP_BAD_REQUEST);
            }
            addDataSource($user, SourcePlatformType::WP);

            $verificationLink = route('auth.email.verify', [
                'id' => $user->id,
                'signature' => app(AuthService::class)->generateVerifiedEmailSignature($user)
            ]);

            event(new UserRegistered($user, $company = null, $verificationLink));

            $user->load('companies');

            return $this->sendResponse('success', $this->serializeUserData($user));
        } catch (\Exception $e) {
            return $this->sendResponse('failed', $request->all(), 'Something went wrong', Response::HTTP_BAD_REQUEST);
        }
    }

    public function createCompany(Request $request)
    {
        $rules              = [
            'name'          => 'required|max:50',
            'mobile_number' => 'required|string|max:20',
            'industry'      => 'required',
            'company_size'  => 'required',
            'website'       => ['required', 'url', 'max:100', new ValidRootDomain],
            'username'      => [
                'required',
                'unique:companies,username',
                'regex:/(^([a-zA-Z0-9\-_]+)?$)/u',
                new ExcludedCompanyUsername,
                'max:50',
            ],
        ];
        $validationMessages = [
            'mobile_number.required'    => 'please provide a valid phone no',
            'mobile_number.numeric'     => 'please provide a valid phone no',
            'mobile_number.max'         => 'phone no must be less then 20',
            'name.max'                  => 'name must be less then 50',
            'username.max'              => 'username must be less then 50',
            'username.regex'            => 'please provide a valid username',
            'name.required'             => 'please provide a valid company name',
            'username.required'         => 'please provide a valid username',
            'terms_and_policy.required' => 'please agree to terms and condition',
            'industry.required'         => 'please select an industry',
            'website.url'               => 'please provide a valid website',
            'website.max'               => 'website url must be less then 100',

        ];

        $data = $request->all();
        $data['username'] = addslashes($data['username']);
        $validator = Validator::make($data, $rules, $validationMessages);

        if ($validator->fails()) {
            return $this->sendResponse('failed', $request->all(), $validator->errors()->toArray(), Response::HTTP_BAD_REQUEST);
        }

        $user = \Auth::user();

        $data = [
            'name'              => $request->get('name'),
            'mobile_number'     => $request->get('mobile_number'),
            'company_type_id'   => data_get($request->all(), 'industry.id'),
            'company_size'      => $request->get('company_size'),
            'created_by'        => $user->id,
            'username'          => $request->get('username'),
            'website'           => $request->get('website'),
            'is_verified'       => false,
            'verification_code' => Uuid::uuid4()->toString(),
            'candidate_login_option' => ['email', 'google', 'linkedin']
        ];

        \DB::beginTransaction();
        try {
            $company = Company::create($data);
            $permissions = array_column(Lang::get('manager-permissions'), 'key');
            $company->managers()->sync([$user->id => [
                'role'          => UserRole::ADMINISTRATOR,
                'designation'   => 'Admin',
                'status'        => ManagerStatus::CONFIRM,
                'permission'    => $permissions,
                'notifications' => app(NotificationSettingService::class)->managerDefaultNotification($permissions),
            ]]);

            $key = Uuid::uuid4()->toString();
            $company->appKeys()->create([
                'app_key' => $key,
                'label'   => 'AutoWP',
            ]);
            addDataSource($company, SourcePlatformType::WP);

            $user->type            = UserType::EMPLOYER;
            $user->current_company = $company->id;
            $user->save();

            $company->templates = ['pipeline' => [
                [
                    'name' => 'Default',
                    'steps' => config('easyjob.default-pipeline-steps'),
                    'default' => true
                ]
            ]];
            $company->save();

            $user->load('companies');

            \DB::commit();

            return $this->sendResponse('success', $this->serializeUserData($user));

        } catch (\Exception $exception) {
            \DB::rollBack();
            return $this->sendResponse('failed', $request->all(), __('Something Went Wrong'), Response::HTTP_BAD_REQUEST);
        }
    }

    public function getCompanyCreateData(Request $request)
    {
        $wpVersion = $request->header('x-plugin-version', null);
        $companySizes = trans('constants.company_size');
        $companySizes = array_values(collect($companySizes)->map(function ($size, $key) {
            return [
                'id'   => $key,
                'size' => str_replace(' Employees', '', $size)
            ];
        }, array_keys($companySizes))->toArray());

        if (isWpOlderVersion($wpVersion, "2.5.6")) {
            $companySizes = array_slice($companySizes, 0, 4);
        }

        $languages    = config('easyjob.lang');
        return $this->sendResponse('success', [
            'company_type'  => CompanyType::orderBy('name', 'asc')->get(),
            'company_sizes' => $companySizes,
            'languages'     => collect($languages)->map(function ($lang, $key) {
                return [
                    'code'  => data_get($lang, 'code'),
                    'name'  => data_get($lang, 'name') . " " . data_get($lang, 'extra'),
                    'image' => asset(data_get($lang, 'image')),
                ];
            }, array_keys($languages))->toArray(),

        ]);
    }

    /**
     * @param $user
     * @param array $data
     * @return bool
     */
    private function updateUser($user, array $data)
    {
        $firstName = data_get($data, 'first_name');
        $lastName  = data_get($data, 'last_name');

        $userData = [
            'password'   => Hash::make($data['password']),
            'type'       => UserType::USER,
            'status'     => UserStatus::ACTIVE,
            'first_name' => $firstName,
            'last_name'  => $lastName,
            'name'       => $firstName . " " . $lastName,
        ];

        try {
            $user->fill($userData)->save();
            $this->subscriptionService->subscribeToFreePackage($user);
            return $user;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @param array $data
     * @return bool
     */
    private function registerUser(array $data)
    {
        $firstName = data_get($data, 'first_name');
        $lastName  = data_get($data, 'last_name');

        $userData = [
            'email'      => $data['email'],
            'password'   => Hash::make($data['password']),
            'type'       => 'User',
            'status'     => 1,
            'first_name' => $firstName,
            'last_name'  => $lastName,
            'name'       => $firstName . " " . $lastName,
        ];

        try {
            $user = User::create($userData);
            $this->subscriptionService->subscribeToFreePackage($user);
            return $user;
        } catch (\Exception $e) {
            return false;
        }
    }


    public function sendResponse($status, $data, $messages = null, $responseCode = Response::HTTP_OK)
    {
        return response()->json(['status' => $status, 'data' => $data, 'message' => $messages], $responseCode);
    }

    public function serializeUserData($user)
    {
        $data['user'] = [
            'first_name'    => data_get($user, 'first_name'),
            'last_name'     => data_get($user, 'last_name'),
            'name'          => data_get($user, 'name'),
            'email'         => data_get($user, 'email'),
            'mobile_number' => data_get($user, 'mobile_number'),
            'profile_image' => data_get($user, 'profile_image'),
            'state_version' => data_get($user, 'meta.state_version'),
        ];

        $data['companies'] = $user->companies->map(function ($company) {
            $companyData        = [];
            $companyData['url'] = companyPageRoute($company);

            foreach (['id', 'name', 'username', 'logo', 'is_verified', 'verification_code', 'website'] as $col) {
                $companyData[$col] = data_get($company, $col);
            }

            if (blank($company->appKeys)) {
                $key = $key = Uuid::uuid4()->toString();
                $company->appKeys()->create([
                    'app_key' => $key,
                    'label'   => 'AutoWP',
                ]);
            }

            $companyData['app_keys'] = $company->appKeys;
            return $companyData;
        });

        return $data;
    }

    private function getPaymentLink($company)
    {
        $user = $company->creator;
        $transaction = Transaction::where('user_id', $user->id)->orderBy('id', 'DESC')->first();

        if (blank($transaction)) {
            return appUrl('/my-account/payment-history');
        }

        $paid = (bool) $transaction->status;
        if ($paid) {
            if (data_get($transaction, 'status.object') == 'invoice') {
                return url(data_get($transaction, 'status.hosted_invoice_url', '#'));
            } elseif (data_get($transaction, 'status.object') == 'charge') {
               return url(data_get($transaction, 'status.receipt_url', '#'));
            } elseif (data_get($transaction, 'status.object') == 'checkout.session') {
                $paymentIntent = app(PaymentService::class)->getStripePaymentIntent(data_get($transaction, 'status.payment_intent'));
                return url(data_get($paymentIntent, 'charges.data.0.receipt_url', '#'));
            }
        } else {
            return url(data_get($transaction, 'status.hosted_invoice_url', '#'));
        }

    }

}
