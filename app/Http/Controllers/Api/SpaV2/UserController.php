<?php

namespace App\Http\Controllers\Api\SpaV2;

use App\Enums\AddressType;
use App\Enums\CheckoutProcessStatus;
use App\Enums\CouponScope;
use App\Enums\DiscountType;
use App\Enums\ManagerStatus;
use App\Enums\PackagePlan;
use App\Enums\PackageRule;
use App\Enums\PackageStatus;
use App\Enums\PackageType;
use App\Enums\PaymentMethod;
use App\Enums\PaymentType;
use App\Enums\StripeCouponDuration;
use App\Enums\UserType;
use App\Http\Requests\User\ApplyCouponRequest;
use App\Http\Requests\User\ChangeMyPasswordRequest;
use App\Http\Requests\User\PasswordChangedVerificationRequest;
use App\Http\Requests\User\UpdateBasicInformationRequest;
use App\Http\Requests\User\UpdateBillingDetailRequest;
use App\Http\Requests\User\UpdateLastSeenVersionRequest;
use App\Http\Requests\User\UpdatePaymentInfoRequest;
use App\Jobs\CompanyDelete;
use App\Jobs\ZohoSyncDeal;
use App\Jobs\ZohoUpdateContactStatus;
use App\Jobs\ZohoUpdateDealStage;
use App\Mail\PasswordChanged;
use App\Mail\SubscriptionCancelRequestMail;
use App\Models\Company;
use App\Models\CompanyManager;
use App\Models\Coupon;
use App\Models\DeletedUsers;
use App\Models\Package;
use App\Models\Transaction;
use App\Models\User;
use App\Services\AddressService;
use App\Services\AffiliationService;
use App\Services\CouponService;
use App\Services\JobPermissionService;
use App\Services\JobPostService;
use App\Services\PackageService;
use App\Services\PaymentService;
use App\Services\Paypal\PaypalService;
use App\Services\SubscriptionService;
use App\Services\TransactionService;
use App\Services\UserAccountService;
use App\Services\ZohoCRMService;
use App\Transformer\UserTransformer;
use App\Utils\EjLogger;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Log;
use Mail;
use Sentry;
use Srmklive\PayPal\Services\PayPal as PayPalClient;

class UserController extends ApiController
{
    private UserTransformer $userTransformer;
    private PayPalClient $paypalClient;

    /**
     * @param UserTransformer $userTransformer
     * @param PayPalClient $paypalClient
     */

    public function __construct(UserTransformer $userTransformer, PayPalClient $paypalClient)
    {
        parent::__construct();

        $this->userTransformer = $userTransformer;
        $this->paypalClient =  $paypalClient;
    }

    public function getUserId(Request $request)
    {
        return $this->successResponse(['user_id' => $this->user()->id]);
    }

    public function getUserInfo(Request $request)
    {
        $user = $this->user();
        if (blank($user)) {
            return $this->errorResponse([], __('responses.context_not_found', ['context' => 'User']), 404);
        }

        $user = $user->load('companies.creator');
        $user->accessToken = $request->bearerToken() ?? $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

        return $this->successResponse($this->userTransformer->serializeLoginData($user));
    }


    public function getAllSubscriptionPackages()
    {
        $user = $this->user();

        if ($user->id != $this->company->created_by) {
            return $this->errorResponse([], 'Company owner access required.');
        }

        $userType = data_get($user, 'package.type');

        $userPakcageRules = Arr::except(data_get($user, 'package_rules', []), ['id']);
        $user->load('myCompanies.managers', 'myCompanies.jobs', 'package');
        $userPackage = $user->package;

        foreach ($userPakcageRules as $key => $rule) {
            if (array_key_exists($key, $userPackage->getAttributes())) {
                $userPackage->{$key} = $rule;
            };
        }

        $selectedPackage = app(PackageService::class)->serializePackages($userPackage, $user);

        $packages = Package::where('id', '<>', $user->package_id)
            ->orderBy('plan', 'asc')
            ->orderBy('price', 'asc')
            ->where('type', '!=', PackageType::ENTERPRISE)
            ->where(function ($query) {
                $query->where(function ($q) {
                    $q->where('slug', 'free')
                        ->where('plan', PackagePlan::LIFETIME);
                })
                ->orWhere('plan', '!=', PackagePlan::LIFETIME);
            })
            ->get()->map(function ($package) use ($user) {

                if ($package->type !== PackageType::REGULAR) {
                    return app(PackageService::class)->serializePackages($package, $user);
                }

                if ($package->status === PackageStatus::PUBLIC) {
                    return app(PackageService::class)->serializePackages($package, $user);
                }

            })->prepend($selectedPackage)->groupBy('type');

//        $packages = Package::orderBy('price', 'asc')->get()->map(function ($package) use ($user) {
//
//            if ($package->type == PackageType::REGULAR) {
//                if ($package->status == PackageStatus::PUBLIC) {
//                    return $this->serializePackages($package);
//                } elseif ($package->status == PackageStatus::HIDDEN && $package->id == $user->package_id) {
//                    return $this->serializePackages($package);
//                }
//            } else {
//                return $this->serializePackages($package);
//            }
//        })->groupBy('type');


        $userPackageType = data_get($user, 'package.type', 1);
        $userEmail = data_get($user, 'email');
        $testUserEmails = config('easyjob.test.users', []);
        $packagesForUser = [];


        $appSumoPackages = data_get($packages, PackageType::APP_SUMO, []);

        if (($userPackageType == PackageType::APP_SUMO) && !blank($appSumoPackages)) {
            $packagesForUser[] = [
                'title' => "AppSumo Packages",
                'type' => PackageType::APP_SUMO,
               // 'packages' => $appSumoPackages
                'packages' => [$selectedPackage]
            ];
        }

        $testPackages = data_get($packages, PackageType::TEST, []);

        if (in_array($userEmail, $testUserEmails) && !blank($testPackages)) {
            $packagesForUser[] = [
                'title' => "Test Packages",
                'type' => PackageType::TEST,
                'packages' => $testPackages
            ];
        }

        $regularPackages = data_get($packages, PackageType::REGULAR, []);

        if (!blank($regularPackages)) {
            $packagesForUser[] = [
                'title' => 'Subscription Packages',
                'type' => PackageType::REGULAR,
                'packages' => $regularPackages
            ];
        }

        return $this->successResponse($packagesForUser);
    }

    public function cancelSubscriptionPackages(Request $request)
    {
        try {
            $user = $this->user();
            $package = $user->package;

            $subscriptionDetails = (object)[
                'userName' => $user->name,
                'packageName' => $package->name,
                'packageValidity' => $user->package_validity,
            ];

            $cancelNow =  $request->get('type', 'now');
            if ($package->type !== PackageType::APP_SUMO && $package->slug !== 'free') {
                $paymentService = app(PaymentService::class);
                if($cancelNow) {
                    if ($user->stripe_id) {
                        $paymentService->userCancelSubscription($user->stripe_id, 'now');
                    }
                    app(SubscriptionService::class)->subscribeToFreePackage($user);

                    $transService = app(TransactionService::class);
                    $transService->createFreeTransaction($user);

                    dispatch(new ZohoSyncDeal($user->id));
                } else {

                    $paymentService->updateSubscriptionToEndOfPeriod($this->guard);
                    $user->subscription_cancel_request = true;
                    $user->save();
                }

                updateUserStateVersion($user);

                $user->refresh();

                Mail::to($user->email)->send(new SubscriptionCancelRequestMail($subscriptionDetails));

                return $this->successResponse((new UserTransformer())->serializeStateUserData($user), __('responses.context_subscription', ['context' => 'canceled']));
            } else {
                return $this->errorResponse([], __('responses.context_subscription_package', ['context' => 'canceled']));
            }
        } catch (Exception $e) {
            Log::error($e);
        }
    }

    public function pauseSubscriptionPackages()
    {
        try {
            $user = $this->user();
            $package = $user->package;

            if ($package->type !== PackageType::APP_SUMO && $package->slug !== 'free') {

                $paymentService = app(PaymentService::class);
                $paymentService->updateSubscriptionToPausePaymentCollection($this->guard);

                return $this->successResponse([], __('responses.context_subscription', ['context' => 'paused']));
            } else {
                return $this->errorResponse([], __('responses.context_subscription_package', ['context' => 'paused']));
            }
        } catch (\Exception $exception) {
            \Log::error($exception->getMessage());
        }
    }

    public function resumeSubscriptionPackages()
    {
        try {
            $user = $this->user();
            $package = $user->package;

            if ($package->type !== PackageType::APP_SUMO && $package->slug !== 'free') {

                $paymentService = app(PaymentService::class);
                $paymentService->updateSubscriptionToResumePaymentCollection($this->guard);

                return $this->successResponse([], __('responses.context_subscription', ['context' => 'resumed']));
            } else {
                return $this->errorResponse([], __('responses.context_subscription_package', ['context' => 'resumed']));
            }
        } catch (\Exception $exception) {

            \Log::error($exception->getMessage());
        }
    }

    /**
     * Get public package list
     * @param Request $request
     * @return JsonResponse
     */
    public function getPublicSubscriptionPackages(Request $request)
    {
        $plan = $request->get('plan');
        $packages = [];

        $selectedPackage = null;
        if ($request->has('plan')) {
            $selectedPackage = Package::query()->where('slug', $plan)->first();
        }

        if (!blank($selectedPackage) && data_get($selectedPackage, 'status') !== PackageStatus::PUBLIC) {
            $packages[] = $this->serializePackageData($selectedPackage);
        } else {
            $packageQuery = Package::query()
                ->where('type', '!=', PackageType::ENTERPRISE)
                ->where(function ($query) {
                    $query->where(function ($q) {
                        $q->where('slug', 'free')
                            ->where('plan', PackagePlan::LIFETIME);
                    })
                        ->orWhere('plan', '!=', PackagePlan::LIFETIME);
                })
                ->where('status', PackageStatus::PUBLIC)->orderBy('price', 'asc');

            if (!blank($selectedPackage)) {
                $packageQuery->where('id', '<>', $selectedPackage->id);
            }

            $packages = $packageQuery->get();

            if (!blank($selectedPackage)) {
                $packages->prepend($selectedPackage);
            }

            $packages = $packages->map(function ($package) {
                $package->name = str_replace(['Yearly', 'yearly', 'Monthly', 'monthly', 'Lifetime'], '', $package->name);
                return $this->serializePackageData($package);
            });
        }

        return $this->successResponse($packages);
    }

    private function serializePackageData($package)
    {
        $data = $package->only(['id', 'type', 'name', 'slug', 'plan', 'price', 'discount', 'discounted_price', 'stripe']);
        $data['plan'] = data_get(trans("subscription.package.plan"), $package->plan, '');
        $data['plan_id'] = $package->plan;
        $data['has_discount'] = data_get($package, 'discount', 0) > 0;
        $data['attrs'] = [];
        $numberGen = [PackageRule::COMPANIES, PackageRule::ACTIVE_JOBS, PackageRule::ACTIVE_CANDIDATES, PackageRule::TEAM_ACCOUNTS];
        foreach ([PackageRule::ACTIVE_JOBS, PackageRule::TEAM_ACCOUNTS, PackageRule::ACTIVE_CANDIDATES] as $key) {
            $data['attrs'][] = subscriptionAttrLevel($package, $key, in_array($key, $numberGen));
        }
        return $data;
    }

    /**
     * Get Package By Slug
     * @param $slug
     * @return JsonResponse
     */
    public function getSubscriptionPackage($slug)
    {
        $user = $this->user();

        $package = Package::query()->where('type', '!=', PackageType::APP_SUMO)->where('slug', $slug)->first();
        if (blank($package) || blank($user)) {
            return $this->errorResponse([], __('responses.context_not_found', ['context' => 'Package']), 404);
        }

        $currentPackage = $user->package;
        if ($currentPackage->price > 0) {
            $desc = $currentPackage->name . " " . __('to') . " " . $package->name;
        } else {
            $desc = __('New subscription to') . " " . $package->name;
        }

        $packageData = app(PackageService::class)->serializePackages($package, $user);

        $packageData['desc'] = $desc;
        $packageData['coupon_code'] = data_get($package, 'coupon.code', '');

        $coupon = $package->coupon;
        if ($coupon) {
            $isPercent = $coupon->discount_type === DiscountType::PERCENT;
            $packageData['discount_text'] = number_format($coupon->amount) . ($isPercent ? "%" : "$") . " Discount";
        } else {
            $packageData['discount_text'] = '';
        }

        return $this->successResponse($packageData);
    }

    public function changeMyPassword(ChangeMyPasswordRequest $request)
    {
        $user = $this->user();

        $passwordMatched = Hash::check($request->get('old_password'), $user->password);
        if (!$passwordMatched) {
            return $this->validationErrorResponse(['old_password' => [__('responses.user.wrong_current_password')]]);
        }

        try {
            $user->password = Hash::make($request->get('new_password'));
            $user->save();


            Mail::to($user->email)->send(new PasswordChanged($this->user(), $request->get('domain_company')));

            return $this->successResponse($user->toArray(), __('responses.user.password_change'));
        } catch (\Exception $e) {
            return $this->errorResponse([], __('responses.user.password_not_changed'));
        }
    }

    public function newPasswordVerification(Request $request)
    {
        try {
            app(UserAccountService::class)->newPasswordVerification($this->user(), $request->get('domain_company'));

            return $this->successResponse([], __('responses.user.check_email_otp'));
        } catch (\Exception $e) {
            return $this->errorResponse([], __('responses.user.otp_code_not_send'));
        }
    }

    public function passwordChangedVerification(PasswordChangedVerificationRequest $request)
    {
        try {
            $user = (new UserAccountService())->passwordChangedVerification($request->only('otp_code', 'new_password'), $this->user(), $request->get('domain_company'));
            if (!blank($user)) {
                return $this->successResponse($user, __('responses.user.password_change'));
            }
            return $this->errorResponse([], __('responses.user.otp_invalid_or_expire'));
        } catch (\Exception $e) {
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    public function paymentHistory()
    {
        $user = $this->user();

        if ($user->id != $this->company->created_by) {
            return $this->errorResponse([], 'Company owner access required.');
        }

        $transactions = $user->transactions()->orderBy('updated_at', 'DESC')->paginate(10);

        $transactions->getCollection()->transform(function ($transaction) {
            $action = [];
            $paid = (bool) data_get($transaction, 'status');
            if ($paid) {
                if (data_get($transaction, 'stripe.object') == 'invoice') {
                    $action['title'] = __('Invoice');
                    $action['link'] = url(data_get($transaction, 'stripe.hosted_invoice_url', '#'));
                } elseif (data_get($transaction, 'stripe.object') == 'charge') {
                    $action['title'] = __('Receipt');
                    $action['link'] = url(data_get($transaction, 'stripe.receipt_url', '#'));
                } elseif (data_get($transaction, 'stripe.object') == 'checkout.session') {
                    $paymentIntent = app(PaymentService::class)->getStripePaymentIntent(data_get($transaction, 'stripe.payment_intent'));
                    $action['title'] = __('Receipt');
                    $action['link'] = url(data_get($paymentIntent, 'charges.data.0.receipt_url', '#'));
                }
            } else {
                $action['title'] = __('Pay Now');
                $action['link'] = url(data_get($transaction, 'stripe.hosted_invoice_url', '#'));
            }

            return [
                'id' => data_get($transaction, 'id'),
                'package_id' => data_get($transaction, 'package_id'),
                'trans_id' => $transaction->trans_id,
                'details' => $transaction->desc,
                'type' => str_replace('_', ' ', getEnumValue(PaymentType::class, $transaction->type, true)),
                'price' => data_get($transaction, 'price', 0.00),
                'discount' => data_get($transaction, 'discount', 0.00),
                'total' => data_get($transaction, 'total', 0.00),
                'stripe' => $transaction->stripe,
                'paypal' => $transaction->paypal,
                'payment_method' => $transaction->payment_method,
                'coupon' => $transaction->coupon,
                'paid' => $paid,
                'isPayable' => ($transaction->total > 0),
                'date' => $transaction->created_at->format(config('easyjob.user.date_format')),
                'isLifetime' => $transaction?->package?->plan == PackagePlan::LIFETIME,
                'action' => $action,
            ];
        });

        return $this->successResponse($transactions->toArray());
    }

    public function getActionUrl($transactionId)
    {
        $user = $this->user();
        $transaction = $user->transactions()->where('id', $transactionId)->first();
        $paid = (bool) data_get($transaction, 'status');

        if (blank(data_get($transaction, 'stripe'))) {
            return $this->successResponse(['url' => '#']);
        }

        $stripe = new \Stripe\StripeClient(config('easyjob.stripe_secret'));

        if (data_get($transaction, 'stripe.object') == 'invoice') {
            $invoice = $stripe->invoices->retrieve(data_get($transaction, 'stripe.id'), []);
            return $this->successResponse(['url' => url(data_get($invoice, 'hosted_invoice_url', '#'))]);
        } elseif (data_get($transaction, 'stripe.object') == 'charge') {
            $invoice = $stripe->charges->retrieve(data_get($transaction, 'stripe.id'), []);
            return $this->successResponse(['url' =>  url(data_get($invoice, 'receipt_url', '#'))]);
        } elseif (data_get($transaction, 'stripe.object') == 'checkout.session') {
            $paymentIntent = app(PaymentService::class)->getStripePaymentIntent(data_get($transaction, 'stripe.payment_intent'));
            return $this->successResponse(['url' => url(data_get($paymentIntent, 'charges.data.0.receipt_url', '#'))]);
        }
    }

    public function getPaypalInvoice($transactionId)
    {
        try {
            $user = $this->user();
            $transaction = $user->transactions()->where('id', $transactionId)->first();
            $transaction->date_paid = $transaction->created_at->format(config('easyjob.user.date_format'));
            $transaction->discount_percentage = $this->calPercentage($transaction->discount, $transaction->price);

            return $this->successResponse($transaction);
        }catch (\Exception $e){
            return $this->errorResponse($e);
        }
    }

    public function calPercentage($amount, $total)
    {
        $count1 = $amount / $total;
        $count2 = $count1 * 100;
        $count = number_format($count2, 0);
        return $count;
    }

    public function billingDetails()
    {
        $user = $this->user();

        if ($user->id != $this->company->created_by) {
            return $this->errorResponse([], 'Company owner access required.');
        }

        $billingAddress = $user->billingAddress();
        $data = [
            'payment_info' => $user->only('stripe_id', 'card_holder_name', 'card_brand', 'card_last_four'),
        ];
        if ($billingAddress) {

            $billingAddressArr = [];
            if ($billingAddress->city) array_push($billingAddressArr, $billingAddress->city->name);
            if ($billingAddress->state) array_push($billingAddressArr, $billingAddress->state->name);
            if ($billingAddress->country) array_push($billingAddressArr, $billingAddress->country->name);

            $data['billing'] = [
                'id' => $billingAddress->id,
                'address_line_1' => $billingAddress->address_line_1,
                'address_line_2' => $billingAddress->address_line_2,
                'postal_code' => $billingAddress->postal_code,
                'country' => $billingAddress->country,
                'state' => $billingAddress->state,
                'city' => $billingAddress->city,
                'location' => implode(', ', $billingAddressArr)
            ];
        }
        $data['billing']['name'] = $user->name;
        $data['billing']['email'] = $user->email;

        return $this->successResponse($data);
    }

    public function updateBillingDetails(UpdateBillingDetailRequest $request)
    {
        try {
            $user = $this->user();
            $addrData = $request->all();

            $data = [
                'type' => AddressType::BILLING,
                'address_line_1' => $request->get('street'),
                'country_id' => data_get($addrData, 'country.id'),
                'state_id' => data_get($addrData, 'state.id'),
                'city_id' => data_get($addrData, 'city.id'),
                'postal_code' => $request->get('postal_code'),
                'model_id' => $user->id,
                'model_type' => User::class
            ];

            $addressId = $request->get('id');
            $addressService = app(AddressService::class);
            $addressService->createOrUpdate($data, $addressId);

            return $this->successResponse([], __('responses.context_updated', ['context' => 'Billing information']));
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorResponse([], __('responses.context_cannot_be_updated', ['context' => 'Billing information']));
        }
    }

    public function updateBasicInformation(UpdateBasicInformationRequest $request)
    {
        try {
            $data = [
                'first_name' => $request->get('first_name'),
                'last_name' => $request->get('last_name'),
                'name' => '',
                'mobile_number' => $request->get('mobile_number'),
                'zoho_sync' => false
            ];

            $user = $this->user();
            $user->fill($data)->save();

            $serializeCandidate = (new UserTransformer())->serializeStateUserData($user);
            return $this->successResponse($serializeCandidate, __('responses.context_updated', ['context' => 'Account information']));
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            return $this->errorResponse([], __('responses.failed_to_update'));
        }
    }


    public function updateLastSeenVersion(UpdateLastSeenVersionRequest $request)
    {
        try {
            $user = $this->user();
            $user->last_seen_version = $request->get('last_seen_version');
            $user->save();

            return $this->successResponse([], __('responses.context_updated', ['context' => 'User last seen version']));
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorResponse([], __('responses.failed_to_update'));
        }
    }

    /**
     * Delete My Account With Company & Manager & Applied Job Information
     * @return JsonResponse
     * @throws \Exception
     */
    public function deleteMyAccount()
    {
        \DB::beginTransaction();
        try {
            $user = $this->user();
            dispatch(new ZohoUpdateContactStatus(app(ZohoCRMService::class)->getUserData($user, true)));


            app(PaymentService::class)->cancelSubscription($this->guard);

            DeletedUsers::create(['email' => $user->email, 'user_id' => $user->id]);

            $user->email = "deleteduser" . $user->id . "@easy.jobs";
            $user->save();

            Transaction::query()->where('user_id', $user->id)->whereNotNull('deal_id')->each(function ($transaction) {
                dispatch(new ZohoUpdateDealStage($transaction->deal_id));
            });

            (new JobPostService())->deleteUserJobApplicantData($user);

            CompanyManager::where('user_id', $user->id)->get()->each(function ($manager) {
                (new JobPermissionService())->deleteCompanyCollaborator($manager->company, $manager->user->id);
                $manager->notifications()->delete();
            });

            Company::where('created_by', $user->id)->get()->each(function ($company) use ($user) {
                dispatch(new CompanyDelete($company->id, $user));

                $company->zoho_sync = false;
                $company->save();

                CompanyManager::query()->where('company_id', $company->id)->whereNot('user_id', $user->id)->get()->each(function ($manager) {
                    dispatch(new ZohoUpdateContactStatus(app(ZohoCRMService::class)->getUserData($manager->user, false)));
                });
            });

            $user->media?->each(function ($media) {
                $media->delete();
            });

            $user->delete();

            \DB::commit();

            return $this->successResponse([], __('responses.context_deleted', ['context' => 'Account']));
        } catch (\Exception $e) {
            \DB::rollBack();
            return $this->errorResponse([], __('responses.context_cannot_be_deleted', ['context' => 'Account']));
        }
    }

    public function submitCheckout(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'package_id' => 'required|integer',
            'street' => 'nullable',
            'country' => 'nullable',
            'state' => 'nullable',
            'city' => 'nullable',
            'postal_code' => 'nullable',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        $provider = $request->get('provider', PaymentMethod::STRIPE);
        $couponScope = match ($provider) {
            PaymentMethod::PAYPAL => CouponScope::GATEWAY,
            PaymentMethod::STRIPE => CouponScope::STRIPE
        };
        $user = $this->user();
        $package = Package::find($request->get('package_id'));
        $packageService = app(PackageService::class);
        $isUpgradable = $packageService->isPlanUpgradeable($user, $package);

        if ($isUpgradable['status'] === false) {
            return $this->errorResponse($isUpgradable, $isUpgradable['message']);
        }

        if ($user->id != $this->company->created_by) {
            return $this->errorResponse([], 'Company owner access required.');
        }

        try {
            $paymentService = app(PaymentService::class);

            $addrData = $request->all();

            $addressData = [
                'type' => AddressType::BILLING,
                'address_line_1' => data_get($addrData, 'companyAddress.street'),
                'country_id' => data_get($addrData, 'companyAddress.country.id'),
                'state_id' => data_get($addrData, 'companyAddress.state.id'),
                'city_id' => data_get($addrData, 'companyAddress.city.id'),
                'postal_code' => data_get($addrData, 'companyAddress.postal_code'),
                'model_id' => $user->id,
                'model_type' => User::class
            ];

            $addressId = $request->get('id');
            $addressService = app(AddressService::class);
            $addressService->createOrUpdate($addressData, $addressId);

            $code = $request->get('coupon');

            if (blank($code) && $package?->coupon) {
                $code = $package?->coupon?->code;
            }

            [$coupon, $msg] = $this->checkCouponValidation($code, $couponScope);

            if ($code && blank($coupon)) {
                if (!$coupon->hasPackage($package->id)) {
                    return $this->errorResponse([], __('responses.subscription.invalid_coupon_code'));
                } else {
                    return $this->errorResponse([], $msg);
                }
            }

            if ($this->isDirectSubscribable($coupon, $package->plan)) {
                $subS = new SubscriptionService();
                $user = $subS->subscribe($user, $package);

                $user = $user->load('companies');
                $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

                // Save transaction data
                $transService = app(TransactionService::class);
                $transService->createLifetimeFreeTransaction($user, $package, $coupon);

                return $this->successResponse([
                    'isSubscribed' => true,
                    'user' => $this->userTransformer->serializeLoginData($user)
                ]);
            }



            $response = [];

            if ($provider === PaymentMethod::STRIPE) {
                $paymentService->createOrUpdateStripeCustomer($request, null, 'api');

                if ($coupon) {
                    $paymentService->updateStripeCustomerCoupon($user, $coupon);
                } elseif ($package->coupon_id) {
                    $paymentService->updateStripeCustomerCoupon($user, $package->coupon);
                } else {
                    $paymentService->updateStripeCustomerCoupon($user);
                }

                $urls = [
                    'success' => config('app.url') . "/my-account/checkout/{$package->slug}?success={$package->slug}&provider=1",
                    'cancel' => config('app.url') ."/my-account/checkout/{$package->slug}",
                ];

                $session = $paymentService->createStripeSessionWithSubscription($package, $user, $urls);
                $response = ['session_id' => $session->id];
            }

            if ($provider === PaymentMethod::PAYPAL) {
                $urlsForPaypal = [
                    'success' => config('app.url') ."/my-account/checkout/{$package->slug}?success={$package->slug}&provider=2",
                    'cancel' => config('app.url') ."/my-account/checkout/{$package->slug}?failure={$user->id}&provider=2",
                ];

                $originalPrice = $package->discounted_price;
                $discountedPrice = $package->discounted_price;

                if ($coupon) {
                    $originalPrice = $package->price;
                    $discountedPrice = app(CouponService::class)->calculatePrice($coupon, $originalPrice);
                }

                if ($discountedPrice == 0){
                    $subS = new SubscriptionService();
                    $user = $subS->subscribe($user, $package);

                    $user = $user->load('companies');
                    $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

                    // Save transaction data
                    $transService = app(TransactionService::class);
                    $transService->createLifetimeFreeTransaction($user, $package, $coupon);

                    return $this->successResponse([
                        'isSubscribed' => true,
                        'user' => $this->userTransformer->serializeLoginData($user)
                    ]);
                }

                if ($package->plan === PackagePlan::LIFETIME) {
                    $session = $paymentService->createPaypalOrderForLifetime($package, $discountedPrice, $user, $urlsForPaypal);
                    $link = collect($session['links'])->where('rel', 'approve')->first();
                } else {
                    $plan = null;

                    if ($coupon) {
                        $plan = app(PaypalService::class)->getPaypalPlan(
                            $package->plan,
                            $coupon->duration,
                            $originalPrice,
                            $discountedPrice,
                            $coupon->duration_in_months ?? 0
                        );
                    }

                    $session = $paymentService->createPaypalSessionWithSubscription($package, $discountedPrice, $user, $urlsForPaypal, $plan);
                    $link = collect($session['links'])->where('rel', 'approve')->first();
                }


                $response = [
                    'redirectUrl' => $link['href'],
                    'session_id' => $session['id'],
                ];
            }

            $promoter = $request->get('promoter');

            $affiliationService = app(AffiliationService::class);
            $affiliationService->saveAffiliatePromoter($user, $promoter);
            updateUserStateVersion($user);

            $user->update([
                'checkout_process_status' => CheckoutProcessStatus::UPGRADING
            ]);

            return $this->successResponse($response, __('responses.context_saved', ['context' => 'Billing information']));
        } catch (\Exception $e) {
            $logData = [
                'package' => $package->only(['id', 'name', 'plan_name', 'price', 'discount', 'discounted_price']),
                'user_name' => $user->name,
                'user_email' => $user->email,
                'domain' => data_get($user, 'company.admin_custom_domain', config('easyjob.app.domain')),
                'coupon' => $coupon,
                'failed reason' => $e->getMessage()
            ];

            EjLogger::payment()->error(json_encode($logData));

            Sentry::captureMessage(json_encode($logData));

            return $this->errorResponse([$e->getMessage()], __('responses.something_went_wrong'));
        }
    }

    private function isDirectSubscribable($coupon, $packagePlan): bool
    {
        return (
            $coupon &&
            $coupon->discount_type === DiscountType::PERCENT &&
            $coupon->amount == 100 &&
            $packagePlan === PackagePlan::LIFETIME
        );
    }

    public function subscriptionSessionCreate(Request $request)
    {
        $forceFreePackage = $request->get('forceFreePackage');
        $provider = $request->get('provider', PaymentMethod::STRIPE);

        if (!in_array($provider, [PaymentMethod::STRIPE, PaymentMethod::PAYPAL, PaymentMethod::NONE])) {
            return $this->errorResponse([], 'Invalid payment provider.');
        }

        $couponScope = match ($provider) {
            PaymentMethod::PAYPAL => CouponScope::GATEWAY,
            PaymentMethod::STRIPE => CouponScope::STRIPE
        };

        $plan = $request->get('plan');
        $user = $this->user();
        $package = Package::where('slug', $plan)->first();

        $packageService = app(PackageService::class);
        $isUpgradable = $packageService->isPlanUpgradeable($user, $package);
        if ($isUpgradable['status'] === false) {
            return $this->errorResponse($isUpgradable, $isUpgradable['message']);
        }

        if ($user->myCompanies()->count() > 0 && $user->id != $this->company->created_by) {
            return $this->errorResponse([], 'Company owner access required.');
        }

        try {
            $code = $request->get('coupon');
            $promoter = $request->get('promoter');

            if (blank($code) && $package?->coupon) {
                $code = $package?->coupon?->code;
            }

            [$coupon, $msg] = $this->checkCouponValidation($code, $couponScope);

            //dd($coupon);

            if ($code && blank($coupon)) {
                if (!$coupon->hasPackage($package->id)) {
                    return $this->errorResponse([], __('responses.subscription.invalid_coupon_code'));
                } else {
                    return $this->errorResponse([], $msg);
                }
            }

            // For lifetime 100% coupon
            if ($this->isDirectSubscribable($coupon, $package->plan)) {
                $subS = new SubscriptionService();
                $user = $subS->subscribe($user, $package);

                $user = $user->load('companies');
                $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

                app(TransactionService::class)->createLifetimeFreeTransaction($user, $package, $coupon);

                return $this->successResponse([
                    'isSubscribed' => true,
                    'user' => $this->userTransformer->serializeLoginData($user)
                ]);
            }

            $paymentService = app(PaymentService::class);
            $response = [];

            if ($provider === PaymentMethod::STRIPE) {
                $paymentService->createOrUpdateStripeCustomer($request, null, 'api');

                if ($coupon) {
                    $paymentService->updateStripeCustomerCoupon($user, $coupon);
                } elseif ($package->coupon_id) {
                    $paymentService->updateStripeCustomerCoupon($user, $package->coupon);
                } else {
                    $paymentService->updateStripeCustomerCoupon($user);
                }

                $urls = [
                    'success' => appUrl("/subscribe?success={$plan}&provider=1&plan={$plan}"),
                    'cancel' => appUrl("/subscribe?plan={$plan}&provider=1"),
                ];

                $session = $paymentService->createStripeSessionWithSubscription($package, $user, $urls);
                $response = [
                    'session_id' => $session->id,
                    'mode' => data_get($session, 'mode'),
                    'plan' => data_get($session, 'metadata.plan')
                ];
            }

            if ($provider === PaymentMethod::PAYPAL) {
                $urlsForPaypal = [
                    'success' => appUrl("/subscribe?success={$plan}&provider=2&plan={$plan}"),
                    'cancel' => appUrl("/subscribe?plan={$plan}&provider=2&failure={$user->id}"),
                ];

                $originalPrice = $package->discounted_price;
                $discountedPrice = $package->discounted_price;

                if ($coupon) {
                    $originalPrice = $package->price;
                    $discountedPrice = app(CouponService::class)->calculatePrice($coupon, $originalPrice);
                }

                if ($discountedPrice == 0){
                    $subS = new SubscriptionService();
                    $user = $subS->subscribe($user, $package);

                    $user = $user->load('companies');
                    $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

                    // Save transaction data
                    $transService = app(TransactionService::class);
                    $transService->createLifetimeFreeTransaction($user, $package, $coupon);

                    return $this->successResponse([
                        'isSubscribed' => true,
                        'user' => $this->userTransformer->serializeLoginData($user)
                    ]);
                }

                if ($package->plan === PackagePlan::LIFETIME) {
                    $session = $paymentService->createPaypalOrderForLifetime($package, $discountedPrice, $user, $urlsForPaypal, $forceFreePackage);
                    $link = collect($session['links'])->where('rel', 'approve')->first();
                } else {
                    $plan = null;

                    if ($coupon) {
                        //$originalPrice = $package->price;
                        //$discountedPrice = app(CouponService::class)->calculatePrice($coupon, $originalPrice);
                        $plan = app(PaypalService::class)->getPaypalPlan(
                            $package->plan,
                            $coupon->duration,
                            $originalPrice,
                            $discountedPrice,
                            $coupon->duration_in_months ?? 0
                        );
                    }

                    $session = $paymentService->createPaypalSessionWithSubscription($package, $discountedPrice, $user, $urlsForPaypal, $plan, $forceFreePackage);
                    $link = collect($session['links'])->where('rel', 'approve')->first();
                }


                $response = [
                    'redirectUrl' => $link['href'],
                    'sessionId' => $session['id'],
                ];
            }


            $affiliationService = app(AffiliationService::class);
            $affiliationService->saveAffiliatePromoter($user, $promoter);


            return $this->successResponse($response);
        } catch (\Exception $e) {
            EjLogger::payment()->error($e->getMessage());
            return $this->errorResponse([], $e->getMessage());
        }
    }

    private function checkCouponValidation($code = '', $couponScope = CouponScope::STRIPE)
    {
        if (blank($code)) {
            return [false, "Coupon is not valid."];
        }

        $coupon = Coupon::where('code', $code)
            ->where('is_active', 1)
            ->first();

        if (blank($coupon)) {
            return [false, "Coupon is not valid."];
        }

        if (data_get($coupon, 'duration', '') === StripeCouponDuration::ONCE) {
            $useCount = $this->user()->transactions()->where('coupon->code', $code)->count();
            if ($useCount > 0) {
                return [false, 'Coupon has been already used.'];
            }
        }

        $now = Carbon::now()->timestamp;

        if ($coupon->starts_at && ($coupon->starts_at->timestamp >= $now)) {
            return [false, 'Coupon expired.'];
        }

        if ($coupon->ends_at && ($coupon->ends_at->endOfDay()->timestamp <= $now)) {
            return [false, 'Coupon expired.'];
        }

        if (!empty($coupon->max_usage) && $coupon->applied_count >= $coupon->max_usage) {
            return [false, 'Coupon uses limit exceeded.'];
        }

        if ($couponScope === CouponScope::STRIPE) {
            $paymentService = app(PaymentService::class);

            $stripeCoupon = $paymentService->getStripeCoupon($code);

            if (blank($stripeCoupon) || $stripeCoupon->valid === false) {
                return [false, "Coupon is not valid."];
            }
        }

        return [$coupon, ''];
    }


    public function couponDiscount(Request $request, $plan, $code)
    {
        try {
            $plan = Package::where('slug', $plan)->first();
            $provider = (int) $request->get('provider', PaymentMethod::STRIPE);
            $couponScope = match ($provider) {
                PaymentMethod::PAYPAL => CouponScope::GATEWAY,
                PaymentMethod::STRIPE => CouponScope::STRIPE
            };

            [$coupon, $msg] = $this->checkCouponValidation($code, $couponScope);

            if ($coupon) {
                if (!$coupon->hasPackage($plan->id)) {
                    return $this->errorResponse([], __('responses.subscription.invalid_coupon_code'));
                }

                $isPercent = $coupon->discount_type === DiscountType::PERCENT;

                $amount = number_format(($isPercent ? $plan->price * $coupon->amount / 100 : $coupon->amount), 2, '.', '');

                $couponData = [
                    'code' => $code,
                    'discount' => $amount,
                    'discounted_price' => app(CouponService::class)->calculatePrice($coupon, $plan->price),
                    'discount_text' => number_format($coupon->amount) . ($isPercent ? "%" : "$") . " Discount"
                ];

                return $this->successResponse($couponData);
            } else {
                return $this->errorResponse([], $msg);
            }
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorResponse([], $e->getMessage());
        }

    }

    public function applyCoupon(ApplyCouponRequest $request)
    {
        /*$validator = \Validator::make($request->all(), [
            'coupon' => 'required|alpha_num',
            'plan' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }*/

        $user = $this->user();

        try {
            $today = Carbon::now()->toDateString();
            $coupon = Coupon::where('code', $request->get('coupon'))
                ->where('scope', CouponScope::STRIPE)
                ->where('starts_at', '<=', $today)
                ->where('ends_at', '>=', $today)
                ->first();

            if (blank($coupon)) {
                return $this->errorResponse([], __('responses.subscription.invalid_coupon_code'));
            }

//            $paymentService = app(PaymentService::class);
//            $paymentService->saveCardToStripe($request, $user);

            $user->type = UserType::EMPLOYER;
            $user->save();

            return $this->successResponse([], __('responses.context_updated', ['context' => 'Billing information']));

        } catch (\Exception $e) {
            return $this->errorResponse([], $e->getMessage());
        }
    }

    public function updatePaymentInfo(UpdatePaymentInfoRequest $request)
    {
        /*$validator = \Validator::make($request->all(), [
            'stripeToken' => 'required',
            'cardType' => 'required',
            'card_name' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }*/

        $user = $this->user();

        try {

            $paymentService = app(PaymentService::class);
            $paymentService->saveCardToStripe($request, $user);

            return $this->successResponse([], __('responses.context_updated', ['type' => 'Payment information']));

        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            return $this->errorResponse([], __('responses.context_cannot_be_updated', ['type' => 'Payment information']));
        }
    }

    /**
     * Switch To Another Company Or Candidate
     * If Switch To Candidate User Current Company Will Be Null
     * @param Request $request
     * @return JsonResponse
     */
    public function changeCurrentCompany(Request $request)
    {
        try {
            $user = $this->user();
            $companyId = $request->get('company_id');

            $companyManager = CompanyManager::where(['company_id' => $companyId, 'user_id' => $user->id, 'status' => ManagerStatus::CONFIRM])->first();
            if ($companyId && blank($companyManager)) {
                return $this->errorResponse([], __('responses.something_went_wrong'));
            }

            $user->fill(['current_company' => $companyId])->save();

            return $this->successResponse($user->toArray(), __('responses.user.company_changed'));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    public function subscribeToFreePackage(Request $request)
    {
        \DB::beginTransaction();

        $plan = $request->get('plan');
        $user = $this->user();
        $package = Package::where('slug', $plan)->first();

        $packageService = app(PackageService::class);
        $isUpgradable = $packageService->isPlanUpgradeable($user, $package);
        if ($isUpgradable['status'] === false) {
            return $this->errorResponse($isUpgradable, $isUpgradable['message']);
        }

        if ($user->myCompanies()->count() > 0 && $user->id != $this->company->created_by) {
            return $this->errorResponse([], 'Company owner access required.');
        }

        try {
            if ($request->get('is_free')) {

                $subscriptionService = app(SubscriptionService::class);
                $subscriptionService->subscribeToFreePackage($user);

                $user->type = UserType::EMPLOYER;
                $user->save();

                $transService = app(TransactionService::class);
                $transService->createFreeTransaction($user);

                // prepare data
                $user = $user->load('companies');
                $userTransformer = app(UserTransformer::class);
                $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

                \DB::commit();
                return $this->successResponse($userTransformer->serializeLoginData($user), __('responses.subscription.subscribed_free_packaged'));
            } else {
                return $this->errorResponse([], __('responses.subscription.invalid_plan'));
            }
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error($e);
            return $this->errorResponse([], __('responses.subscription.invalid_plan'));
        }
    }

    public function paypalSubscribe(Request $request, $plan = null)
    {
        $plan = $request->get('plan') ?? $plan;
        $user = $this->user();
        $package = Package::where('slug', $plan)->first();

        $data = [
            'plan_id' => data_get(json_decode($package->paypal), 'plan'),
            'custom_id' => $user->id,
            'start_time' => now()->addMinutes(5)->format('Y-m-d\TH:i:sp'),
            'shipping_amount' => [
                'currency_code' => config('easyjob.payment.paypal.currency_code'),
                'value' => $package->price,
            ],
            'subscriber' => [
                'name' => [
                    'given_name' => $user->first_name,
                    'surname' => $user->last_name,
                ],
                'email_address' => $user->email,
                'shipping_address' => [
                    'name' => [
                        'full_name' => $user->name
                    ],
                    'address' => config('easyjob.payment.paypal.address')
                ],
            ],
            'application_context' => config('easyjob.payment.paypal.application_context_create'),
        ];

        try {
            $tokenResponse = $this->paypalClient->getAccessToken();
            $this->paypalClient->setAccessToken($tokenResponse);
            $response = $this->paypalClient->createSubscription($data);

            $subS = new SubscriptionService();
            $user = $subS->subscribe($user, $package);

            $user = $user->load('companies');
            $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

            return $this->successResponse([
                'user' => $this->userTransformer->serializeLoginData($user),
                'redirectUrl' => data_get($response, 'links.0.href'),
            ]);

        }catch (\Exception $e){
            \Log::error($e);
            return $this->errorResponse([], $e->getMessage());
        }


    }
    public function updatePaypalSubscription(Request $request)
    {
        $user = $this->user();
        $package = Package::where('id', $request->get('package_id'))->first();
        $transaction = Transaction::where('user_id', $user->id)->first();

        $data = [
            'plan_id' => data_get(json_decode($package->paypal), 'plan'),
            'custom_id' => $user->id,
            'start_time' => now()->addMinutes(5)->format('Y-m-d\TH:i:sp'),
            'shipping_amount' => [
                'currency_code' => config('easyjob.payment.paypal.currency_code'),
                'value' => $package->price,
            ],
            'shipping_address' => [
                'name' => [
                    'full_name' => $user->name
                ],
                'address' => config('easyjob.payment.paypal.address')
            ],
            'application_context' => config('easyjob.payment.paypal.application_context_update'),
        ];

        try {
            if ($transaction->paypal !== null){
                $tokenResponse = $this->paypalClient->getAccessToken();
                $this->paypalClient->setAccessToken($tokenResponse);
                $response = $this->paypalClient->reviseSubscription($transaction->paypal, $data);

                $subS = new SubscriptionService();
                $user = $subS->subscribe($user, $package);
                $user = $user->load('companies');
                $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

                return $this->successResponse([
                    'user' => $this->userTransformer->serializeLoginData($user),
                    'redirectUrl' => data_get($response, 'links.0.href'),
                ]);
            }else{
                return $this->paypalSubscribe($request, $package->slug);
            }

        }catch (\Exception $e){
            \Log::error($e);
            return $this->errorResponse([], 'Something went wrong. Please try again');
        }
    }

    public function continueAsCandidate()
    {
        \DB::beginTransaction();
        try {
            $user = $this->user();

            $subscriptionService = app(SubscriptionService::class);
            $subscriptionService->subscribeToFreePackage($user);

            $user->type = UserType::CANDIDATE;
            $user->save();

            $transService = app(TransactionService::class);
            $transService->createFreeTransaction($user);

            $user = $user->load('companies');
            $userTransformer = app(UserTransformer::class);
            $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;
            \DB::commit();
            return $this->successResponse($userTransformer->serializeLoginData($user));
        } catch (\Exception $e) {
            \DB::rollBack();
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    public function subscriptionVerify(Request $request)
    {
        $plan = $request->get('plan');
        $orderId = $request->get('sessionId');
        $couponId = $request->get('couponId', null);
        $provider = $request->get('provider');

        if($couponId) {
            $couponId = Coupon::query()->where('code', $couponId)->first()?->id;
        }

        if ($plan && $orderId) {
            try {
                $user = $this->user();
                $paymentService = app(PaymentService::class);
                $paymentStatus = false;
                $paymentStatusForPaypal = false;

                if ($provider == PaymentMethod::STRIPE) {
                    $session = $paymentService->getStripeSession($orderId);
                    $paymentStatus = $session->payment_status === 'paid';
                }

                if ($provider == PaymentMethod::PAYPAL) {
                    $package = Package::where('slug', $plan)->first();

                    if ($package->plan === PackagePlan::LIFETIME) {
                        $session = $paymentService->getCaptureDetails($orderId);
                        $paymentStatusForPaypal = $session['status'] == 'COMPLETED';
                    } else {
                        $session = $paymentService->getSubscriptionDetails($orderId);
                        $paymentStatusForPaypal = $session['status'] === 'ACTIVE';
                    }

                    if ($paymentStatusForPaypal) {
                        subscribeToMailChimp($user);
                        $user = $user->load('companies');
                        $userTransformer = app(UserTransformer::class);
                        $user->payment_provider = $provider;
                        $user->coupon_id = $couponId;
                        $user->save();

                        $subS = new SubscriptionService();
                        $subS->subscribe($user, $package);
                        $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

                        return $this->successResponse($userTransformer->serializeLoginData($user));
                    } else {
                        return $this->errorResponse($session->toArray(), __('responses.context_subscription', ['context' => 'failed']));
                    }
                }

                if ($paymentStatus) {
                    subscribeToMailChimp($user);
                    $user = $user->load('companies');
                    $userTransformer = app(UserTransformer::class);
                    $user->payment_provider = $provider;
                    $user->coupon_id = $couponId;
                    $user->save();

                    $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;
                    //dd($user);

                    return $this->successResponse($userTransformer->serializeLoginData($user));
                } else {
                    return $this->errorResponse($session->toArray(), __('responses.context_subscription', ['context' => 'failed']));
                }
            } catch (\Exception $e) {
                return $this->errorResponse([], $e->getMessage());
            }
        } else {
            return $this->errorResponse([], __('responses.context_subscription', ['context' => 'failed']));
        }
    }

    public function getPackageDetails()
    {
        $package = $this->user()->package;
        return $this->successResponse($package->only(['id', 'name', 'slug']));
    }

    public function moveToFreePackage(Request $request)
    {
        $user = $this->user();
        $packageUser = $request->get('user');

        if ($user->id != $packageUser) return $this->errorResponse([], __('responses.something_went_wrong'));

        $subscription = new SubscriptionService();
        $subscription->subscribeToFreePackage($user);

        return $this->successResponse([]);
    }
}
