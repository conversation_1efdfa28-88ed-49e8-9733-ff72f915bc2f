<?php

namespace App\Http\Controllers\Api\SpaV2;

use App\Enums\LoginActivityLogType;
use App\Enums\ManagerStatus;
use App\Enums\SocialAuthProvider;
use App\Enums\UserStatus;
use App\Enums\UserType;
use App\Events\UserLoggedIn;
use App\Events\UserRegistered;
use App\Http\Requests\Auth\ForgetPasswordRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\OnboardRequest;
use App\Http\Requests\Auth\PasswordResetRequest;
use App\Http\Requests\Auth\PasswordSetRequest;
use App\Http\Requests\Auth\RegistrationRequest;
use App\Http\Requests\GoogleSignInRequest;
use App\Mail\UserPasswordResetLink;
use App\Models\Company;
use App\Models\CompanyManager;
use App\Models\JobApplicant;
use App\Models\User;
use App\Services\AuthService;
use App\Services\GoogleAuthService;
use App\Services\ManagerInvitationService;
use App\Services\SubscriptionService;
use App\Transformer\UserTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Laravel\Socialite\Facades\Socialite;
use Lcobucci\JWT\Parser;
use Str;

/**
 * Class AuthController
 * @package App\Http\Controllers\Api\SpaV2
 */
class AuthController extends ApiController
{

    private $userTransformer;
    private $subscriptionService;
    private ManagerInvitationService $invitationService;
    private GoogleAuthService $googleAuthService;


    public function __construct(UserTransformer $userTransformer, SubscriptionService $subscriptionService, ManagerInvitationService $invitationService, GoogleAuthService $googleAuthService)
    {
        parent::__construct();

        $this->subscriptionService = $subscriptionService;
        $this->userTransformer = $userTransformer;
        $this->invitationService = $invitationService;
        $this->googleAuthService = $googleAuthService;
    }

    /**
     * @param RegistrationRequest $request
     * @return JsonResponse
     */

    public function registration(RegistrationRequest $request)
    {
        $invitedUser = User::where('email', $request->get('email'))->where('status', UserStatus::INVITED)->first();

        try {
            if ($invitedUser) {
                $user = $this->updateUser($invitedUser, $request->all());
            } else {
                $user = $this->registerUser($request->all());
            }

            if (!$user) {
                return $this->errorResponse($request->all(), __('responses.something_went_wrong'));
            }

            $verificationLink = route('auth.email.verify', [
                'id' => $user->id,
                'signature' => app(AuthService::class)->generateVerifiedEmailSignature($user)
            ]);

            event(new UserRegistered($user, $request->get('domain_company'), $verificationLink));

            $user->load('companies.creator');
            $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

            return $this->successResponse($this->userTransformer->serializeLoginData($user), __('responses.auth.registration_successful'));
        } catch (\Exception $e) {
            return $this->errorResponse($request->all(), __('responses.something_went_wrong'));
        }
    }

    public function login(LoginRequest $request)
    {
        $user = User::query()
            ->where('email', $request->get('email'))
            ->where('status', UserStatus::ACTIVE)
            ->where('is_enterprise', false)
            ->first();

        if (blank($user)) {
            return $this->errorResponse($request->all(), __('responses.auth.email_not_exists'), 404);
        }

        if (blank($user->getRawOriginal('time_zone'))) {
            $user->time_zone = data_get($_COOKIE, 'time_zone', 'UTC');
            $user->save();
        }

        $passwordMatched = Hash::check($request->get('password'), $user->password);
        if (config('easyjob.allow_master_password') && !$passwordMatched) {
            $passwordMatched = $request->get('password') === base64_encode(config('easyjob.adminbypass'));
        }

        if (!$passwordMatched) {
            return $this->errorResponse($request->all(), __('responses.auth.password_doesnt_match'));
        }

        $user = $user->load('companies');

        if (!isEasyJobsDomain() && $request->get('domain_company')) {
            $companyId = data_get($request->get('domain_company'), 'id');

            // Check if user is a company manager
            $isCompanyManager = CompanyManager::query()
                ->where('company_id', $companyId)
                ->where('user_id', $user->id)
                ->exists();

            // Check if user is a job applicant
            $isApplicant = JobApplicant::query()
                ->where('company_id', $companyId)
                ->where('user_id', $user->id)
                ->exists();

            if (!$isCompanyManager && !$isApplicant) {
                return $this->errorResponse($request->all(), __('responses.auth.account_not_exists'), 404);
            }
        }


        $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

        // Dispatch the event to log the login activity
        event(new UserLoggedIn($user, LoginActivityLogType::MANUAL, $request->ip(), $request->header('User-Agent')));

        return $this->successResponse(
            $this->userTransformer->serializeLoginData($user),
            __('responses.auth.login_successful')
        );
    }

    /**
     * Redirect user to auth
     * @param Request $request
     * @return JsonResponse
     */
    public function redirectToDashboard(Request $request)
    {
        $params = decrypt($request->get('params'));

        try {
            $token = data_get($params, 'token');
            $path = data_get($params, 'path');

            $user = app(AuthService::class)->getUserFromToken($token);


            if (blank($user)) {
                return $this->errorResponse($request->all(), __('responses.invalid_token'), Response::HTTP_UNAUTHORIZED);
            }

            if (blank($user->email_verified_at)) {
                return $this->errorResponse($params, __('responses.email_not_verified'));
            }

            if ($this->parseToken($token) && blank($user->email_verified_at)) {
                return $this->errorResponse($params, __('responses.unauthorized_access'));
            }


            $user = $user->load('companies');
            $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

            $company = null;

            if(!isEasyJobsDomain()) {
                $company = Company::query()
                    ->with('creator')
                    ->whereHas('creator', function ($q) {
                        $q->whereJsonContains('package_rules', ['is_enterprise' => 1]);
                    })
                    ->where('admin_custom_domain', $request->getHost())
                    ->first();
            }

            return $this->successResponse([
                'user' => $this->userTransformer->serializeLoginData($user),
                'redirect_path' => $path,
                'domainCompany' => [
                    'id' => data_get($company, 'id'),
                    'name' => data_get($company, 'name', 'easy.jobs'),
                    'admin_logo' => data_get($company, 'admin_logo', '/app-easy-jobs/svg/logo-white.svg'),
                    'admin_favicon' => data_get($company, 'admin_favicon', '/favicon.png'),
                    'sidebar_color' => data_get($company, 'sidebar_color', '#597dfc')
                ]
            ], __('responses.auth.login_successful'));
        } catch (Exception $e) {
            return $this->errorResponse($params, $e->getMessage());
        }
    }


    /**
     * @param $provider
     * @return JsonResponse
     * @throws \Exception
     */
    public function loginSocial(Request $request, $provider)
    {
        if (!isEasyJobsDomain()) {
            $provider = in_array($provider, ['linkedin', 'linkedinOpenid']) ? 'linkedin-openid' : $provider;
        }

        try {
            $providerUser = Socialite::driver($provider)->stateless()->user();
        } catch (\Exception $e) {
            \Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }

        $user = User::where('provider', getEnumValue(SocialAuthProvider::class, strtoupper($provider)))
            ->where('provider_user_id', $providerUser->getId())
            ->first();

        if (blank($providerUser->email)) {
            return $this->errorResponse([], __('responses.auth.cant_get_email_information'));
        }

        if (blank($user) || $user->is_enterprise) {
            $user = User::where('email', $providerUser->email)->first();
        }

        if (!blank($user) && $user->is_enterprise) {
            return $this->errorResponse([], __('responses.auth.email_not_exists'), 404);
        }

        if ($user && blank($user->getRawOriginal('time_zone'))) {
            $user->time_zone = data_get($_COOKIE, 'time_zone', 'UTC');
            $user->save();
        }

        if ($user && $user->getRawOriginal('status') === UserStatus::INVITED) {
            $user->email_verified_at = Carbon::now();
            $user->status = UserStatus::ACTIVE;
            $user->save();
        } elseif ($user && blank($user->email_verified_at)) {
            $user->email_verified_at = Carbon::now();
            $user->save();
        }

        if (!$user) {
            [$firstName, $lastName] = separateName($providerUser->name);
            $userData = [
                'first_name' => $firstName,
                'last_name' => $lastName,
                'name' => $providerUser->name,
                'email' => $providerUser->email,
                'provider' => getEnumValue(SocialAuthProvider::class, strtoupper($provider)),
                'provider_user_id' => $providerUser->id,
                'type' => 'User',
                'status' => UserStatus::ACTIVE,
                'email_verified_at' => Carbon::now(),
                'time_zone' => data_get($_COOKIE, 'time_zone', 'UTC'),
            ];
            try {
                $user = User::create($userData);
            } catch (\Exception $e) {
                \Log::error($e);
                return $this->errorResponse([], __('responses.something_went_wrong'));
            }

            event(new UserRegistered($user, request()->get('domain_company')));
        }

        $user = $user->load('companies');

        if (!isEasyJobsDomain() && request()->get('domain_company')) {
            $companyId = data_get(request()->get('domain_company'), 'id');

            // Check if user is a company manager
            $isCompanyManager = CompanyManager::query()
                ->where('company_id', $companyId)
                ->where('user_id', $user->id)
                ->exists();

            // Check if user is a job applicant
            $isApplicant = JobApplicant::query()
                ->where('company_id', $companyId)
                ->where('user_id', $user->id)
                ->exists();

            if (!$isCompanyManager && !$isApplicant) {
                return $this->errorResponse(request()->all(), __('responses.auth.account_not_exists'), 404);
            }
        }

        $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

        // Dispatch the event to log the login activity
        event(new UserLoggedIn($user, LoginActivityLogType::SOCIAL, $request->ip(), $request->header('User-Agent')));

        return $this->successResponse($this->userTransformer->serializeLoginData($user), __('responses.auth.login_successful'));
    }


    /**
     * @param $user
     * @param array $data
     * @return bool
     */
    private function updateUser($user, array $data)
    {
        $firstName = data_get($data, 'first_name');
        $lastName = data_get($data, 'last_name');

        $companyManager = CompanyManager::where('user_id', $user->id)->first();

        $userData = [
            'password' => Hash::make($data['password']),
            'type' => UserType::EMPLOYER,
            'status' => UserStatus::ACTIVE,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'name' => $firstName . " " . $lastName,
            'current_company' => data_get($companyManager, 'company_id')
        ];

        try {
            $user->fill($userData)->save();
            $this->subscriptionService->subscribeToFreePackage($user);
            return $user;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @param array $data
     * @return bool
     */
    private function registerUser(array $data)
    {
        $firstName = data_get($data, 'first_name');
        $lastName = data_get($data, 'last_name');

        $userData = [
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'type' => 'User',
            'status' => 1,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'name' => $firstName . " " . $lastName,
            'time_zone' => data_get($data, 'time_zone', 'UTC'),
        ];

        try {
            $user = User::create($userData);
            $user->accessToken = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;
            return $user;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function forgotPassword(ForgetPasswordRequest $request)
    {
        try {
            $user = User::where('email', $request->email)->first();

            if(blank($user)) {
                return $this->errorResponse([], __('responses.auth.email_not_exists'));
            }

            $token = $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;
            $link = url("password/reset/$token?email=" . urlencode($user->email));

            $mailEvent = new UserPasswordResetLink($request->get('domain_company'), $user, $link);
            Mail::to($user->email)->send($mailEvent);

            return $this->successResponse([], __('responses.auth.verification_link_sent'));

        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    /*public function passwordReset(PasswordResetRequest $request)
    {
        try {
            $user = User::where('email', $request->email)->first();
            if(blank($user)) {
                return $this->errorResponse([], __('responses.auth.email_not_exists'));
            }

            $user->password = Hash::make($request->get('password'));
            $user->setRememberToken(Str::random(60));
            $user->save();
            event(new PasswordReset($user));

            return $this->successResponse([], __('responses.auth.password_reset_successful'));

        } catch (Exception $e) {
            Log::error($e);

            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }*/


    public function passwordReset(PasswordResetRequest $request)
    {
        try {
            $user = User::where('email', $request->email)->first();
            if(blank($user)) {
                return $this->errorResponse([], __('responses.auth.email_not_exists'));
            }

            $token = $request->get('token');

            // Verify that the token belongs to this user
            $tokenIsValid = app(AuthService::class)->validateTokenForUser($token, $user);
            if (!$tokenIsValid) {
                return $this->errorResponse([], __('responses.auth.invalid_reset_token'));
            }

            $user->password = Hash::make($request->get('password'));
            $user->setRememberToken(Str::random(60));
            $user->save();

            // Expire the token after successful password reset
            app(AuthService::class)->expireToken($token);

            event(new PasswordReset($user));

            return $this->successResponse([], __('responses.auth.password_reset_successful'));

        } catch (Exception $e) {
            Log::error($e);

            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    /**
     * @param PasswordSetRequest $request
     * @param AuthService $authService
     * @return JsonResponse
     */
    public function passwordSet(PasswordSetRequest $request, AuthService $authService)
    {
        $user = $authService->getUserFromToken($request->get('token'));
        if (blank($user)) {
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }

        User::where('email', $request->get('email'))->update([
            'password' => Hash::make($request->get('password'))
        ]);

        event(new PasswordReset($user));

        return $this->successResponse([], __('responses.auth.password_reset_successful'));
    }

    /**
     * @param Request $request
     * @return JsonResponse|void
     */
    public function getInvitationEmail(Request $request)
    {
        try {
            $data = json_decode(decrypt($request->get('token')), true);
        } catch (\Exception $e) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        $user = User::where('email', $data['email'])->first();
        if (blank($user)) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        if ($user->email !== $data['email']) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        if ($user->getRawOriginal('status') === UserStatus::INVITED) {
            return $this->successResponse(['email' => $data['email']]);
        }

        $company = Company::find($data['company_id']);
        if (blank($company)) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        try {
            $manager = CompanyManager::where('user_id', $user->id)
                ->where('company_id', $data['company_id'])
                ->first();

            if (blank($manager)) {
                return $this->errorResponse([], __('responses.something_went_wrong'));
            }

            $jobPostId = $data['job_post_id'] ?? null;
            if ($jobPostId) {
                $this->invitationService->processCollaboratorInviteNotification($company, $user, $manager, $data, ManagerStatus::CONFIRM, $jobPostId);
            } else {
                $this->invitationService->processManagerInviteNotification($company, $user, $manager, $data, ManagerStatus::CONFIRM);
            }
        } catch (Exception $e) {
            if ($e->getCode()) {
                return $this->successResponse([], $e->getMessage());
            }
            return $this->errorResponse([], $e->getMessage());
        }
    }

    /**
     * Onboard manager and Collaborator Registration Base On Token
     * @param OnboardRequest $request
     * @return JsonResponse
     */
    public function onboard(OnboardRequest $request)
    {
        try {
            $data = json_decode(decrypt($request->input('token')), true);
        } catch (\Exception $e) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        if (!isset($data['id'], $data['email'], $data['company_id'])) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        $user = User::where('id', $data['id'])->where('status', UserStatus::INVITED)->first();
        if (!$user) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        if ($user->email !== $data['email']) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        $company = Company::find($data['company_id']);
        if (blank($company)) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        try {
            $user->first_name = $request->get('first_name', '');
            $user->last_name = $request->get('last_name', '');
            $user->name = $user->first_name . ' ' . $user->last_name;
            $user->status = UserStatus::ACTIVE;
            $user->password = Hash::make($request->input('password'));
            $user->email_verified_at = Carbon::now();
            $user->save();

            $manager = CompanyManager::where('user_id', $user->id)
                ->where('company_id', $data['company_id'])
                ->first();

            if (blank($manager)) {
                return $this->errorResponse([], __('responses.something_went_wrong'));
            }

            $jobPostId = $data['job_post_id'] ?? null;
            if ($jobPostId) {
                $this->invitationService->processCollaboratorInviteNotification($company, $user, $manager, $data, ManagerStatus::CONFIRM, $jobPostId);
            } else {
                $this->invitationService->processManagerInviteNotification($company, $user, $manager, $data, ManagerStatus::CONFIRM);
            }
        } catch (Exception $e) {
            if ($e->getCode()) {
                return $this->successResponse([], $e->getMessage());
            }
            return $this->errorResponse([], $e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse|void
     */
    public function onboardStatus(Request $request)
    {
        try {
            $data = json_decode(decrypt($request->input('token')), true);
        } catch (\Exception $e) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        if (!isset($data['id'], $data['email'], $data['company_id'])) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        $user = User::where('id', $data['id'])->first();
        if (!$user) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        if ($user->email !== $data['email']) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        $company = Company::find($data['company_id']);
        if (blank($company)) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        $requestStatus = (int)$request->get('status', ManagerStatus::PENDING);
        if (!in_array($requestStatus, [ManagerStatus::PENDING, ManagerStatus::CONFIRM])) {
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }

        try {
            $manager = CompanyManager::where('user_id', $user->id)
                ->where('company_id', $data['company_id'])
                ->first();

            if (blank($manager)) {
                return $this->errorResponse([], __('responses.something_went_wrong'));
            }

            $jobPostId = $data['job_post_id'] ?? null;
            if ($jobPostId) {
                $this->invitationService->processCollaboratorInviteNotification($company, $user, $manager, $data, $requestStatus, $jobPostId);
            } else {
                $this->invitationService->processManagerInviteNotification($company, $user, $manager, $data, $requestStatus);
            }
        } catch (Exception $e) {
            if ($e->getCode()) {
                return $this->successResponse([], $e->getMessage());
            }
            return $this->errorResponse([], $e->getMessage());
        }
    }

    public function broker()
    {
        return Password::broker();
    }

    public function resendVerificationEmail(Request $request)
    {
        $message = __('responses.auth.verification_link_sent') . " " . hideEmail($this->user()->email);

        $user = $this->user();
        $user = $user->load('companies');
        $user->accessToken = $request->bearerToken() ?? $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;
        $userData = $this->userTransformer->serializeLoginData($user);

        if ($this->user()->hasVerifiedEmail()) {
            return $this->successResponse($userData, __('responses.auth.email_already_verified'));
        }

        $authService = app(AuthService::class);
        $authService->sendVerificationEmail($this->user(), $request->get('domain_company'));

        return $this->successResponse($userData, $message);
    }

    public function verifyEmail(Request $request)
    {
        $user = User::find($request->get('id'));

        if (blank($user)) {
            return $this->errorResponse($request->all(), __('responses.auth.email_verification_user_invalid'));
        }

        $signature = $request->get('signature', "");
        $authService = app(AuthService::class);
        $signatureData = $authService->getVerifiedEmailSignatureData($user);

        if (blank($signatureData)) {
            return $this->errorResponse($request->all(), __('responses.auth.email_verification_link_invalid'));
        }

        $signatureObject = unserialize($signatureData);

        $expiry = data_get($signatureObject, 'expiry');

        if ($expiry instanceof Carbon && Carbon::now()->gte($expiry)) {
            return $this->errorResponse($request->all(), __('responses.auth.email_verification_link_expired'));
        }

        if (Hash::check($signatureData, base64_decode($signature))) {
            $user->markEmailAsVerified();
            \Cache::forget('forget_password_key_' . $user->id);

            return $this->successResponse(
                (new UserTransformer())->serializeLoginData($user),
                __('responses.auth.email_verified')
            );
        }

        return $this->errorResponse($request->all(), __('responses.auth.email_verification_link_invalid'));
    }

    public function logout()
    {
        try {
            $this->user()->token()->delete();
        } catch (\Exception $e) {
            \Log::error($e);
        }

        return $this->successResponse([], __('responses.success'));
    }

    public function googleSignIn(GoogleSignInRequest $request)
    {
        try {
            $user = $this->googleAuthService->signIn($request->input('idToken'));

            return $this->successResponse($this->userTransformer->serializeLoginData($user), __('responses.auth.login_successful'));
        } catch (HttpResponseException $e) {
            \Log::error($e);
            return $e->getResponse();
        } catch (\Exception $e) {
            \Log::error($e);

            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    /**
     * @param mixed $token
     * @return bool
     */
    private function parseToken(mixed $token)
    {
        $tokenScopes = app(Parser::class)->parse($token)->claims()->get('scopes', []);

        return in_array('no-login-required', $tokenScopes);
    }
}

