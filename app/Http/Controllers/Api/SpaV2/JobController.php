<?php

namespace App\Http\Controllers\Api\SpaV2;

use App\Models\JobApplicant;
use App\Services\JobPostService;
use Illuminate\Http\JsonResponse;

class JobController extends ApiController
{
    protected JobPostService $jobPostService;

    public function __construct(JobPostService $jobPostService)
    {
        parent::__construct();

        $this->jobPostService = $jobPostService;
    }

    /**
     * @return JsonResponse
     */
    public function jobInfoMetaData()
    {
        return response()->json(
            $this->jobPostService->jobInfoMetaData()
        );
    }

    /**
     * @param $applicant
     * @return JsonResponse
     */
    public function applicantExists($applicant): JsonResponse
    {
        $jobApplicant = JobApplicant::findByGeneratedId($applicant);
        if ($jobApplicant) {
            return $this->successResponse([], __('responses.job.ok'));
        } else {
            return $this->errorResponse([], __('responses.context_not_found', ['context' => 'Applicant']));
        }
    }

}
