<?php

namespace App\Http\Controllers\Api\SpaV2;

use App\Enums\CompanyEmailType;
use App\Enums\CustomResponseCode;
use App\Enums\JobApplyStatus;
use App\Enums\PackageRule;
use App\Enums\ResponseStatus;
use App\Enums\UserPermission;
use App\Models\JobApplicant;
use App\Models\JobPost;
use App\Models\User;
use App\Services\AuthService;
use App\Services\CompanyEmailService;
use App\Services\JobCandidateService;
use App\Transformer\ApplicantTransformer;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Throwable;

class JobCandidateController extends ApiController
{
    protected JobCandidateService $jobCandidateService;

    public function __construct(JobCandidateService $jobCandidateService)
    {
        parent::__construct();

        $this->jobCandidateService = $jobCandidateService;

        $this->middleware('api.verify.same.company')->only([
            'getCandidates',
            'getInvitations',
            'deleteInvitation',
            'addCandidate',
            'deleteCandidate',
            'pendingCandidates',
            'invitePendingCandidates'
        ]);

    }

    /**
     * Get job all candidate list
     * @param Request $request
     * @param ApplicantTransformer $applicantTransformer
     * @return JsonResponse
     */
    public function getCandidates(Request $request, ApplicantTransformer $applicantTransformer)
    {
        $jobPost = $request->get('jobPost');

        if (!$this->hasPermission(UserPermission::CANDIDATE_VIEW, $jobPost->company_id, $jobPost->id)) {
            return $this->unauthorizedAccess();
        }

        $candidates = $this->jobCandidateService->getFilteredCandidatesWithPagination($jobPost, $request->all());
        $pendingCandidates = $this->jobCandidateService->getPendingCandidates($jobPost->id);

        return $this->successResponse([
            'ai_score_show' => $this->company->ai_score_show,
            'candidates' => $applicantTransformer->getJobCandidateList($candidates),
            'job' => $applicantTransformer->serializeJobForCandidateList($jobPost),
            'pending_candidates' => count($pendingCandidates)
        ]);
    }

    /**
     * Get job invite candidate list
     * @param Request $request
     * @param ApplicantTransformer $applicantTransformer
     * @return JsonResponse
     */
    public function getInvitations(Request $request, ApplicantTransformer $applicantTransformer)
    {
        $jobPost = $request->get('jobPost');
        if (!$this->hasPermission(UserPermission::CANDIDATE_ORGANIZE, $jobPost->company_id, $jobPost->id)) {
            return $this->unauthorizedAccess();
        }

        return $this->successResponse(
            $applicantTransformer->getInviteApplicantList($jobPost->invitations)
        );
    }

    /**
     * * Delete job invite candidate
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteInvitation(Request $request)
    {
        $jobPost = $request->get('jobPost');

        if (!$this->hasPermission(UserPermission::CANDIDATE_ORGANIZE, $jobPost->company_id, $jobPost->id)) {
            return $this->unauthorizedAccess();
        }

        $jobPost->invitations()->where('applicant_email', $request->get('email'))->delete();

        return $this->successResponse([], __('responses.context_removed', ['context' => 'Invitation']));
    }

    /**
     * Invite Candidate
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function addCandidate(Request $request)
    {
        $isFree = strtolower(data_get($this->company, 'creator.package_rules.name')) === 'free';

        if ($isFree) {
            return $this->errorResponse([], data_get(__("subscription.package.errors"), PackageRule::TEAM_ACCOUNTS, ''), CustomResponseCode::PACKAGE_LIMIT_EXCEEDED);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required | email',
        ]);

        $jobPost = $request->get('jobPost');

        if (!$this->hasPermission(UserPermission::CANDIDATE_ORGANIZE, $jobPost->company_id, $jobPost->id)) {
            return $this->unauthorizedAccess();
        }


        $candidateName = '';
        $validator->after(function ($validator) use ($jobPost, $request, &$candidateName) {
            $candidateName = $this->jobCandidateService->verifyApplicantApplied($validator, $jobPost, $request->get('email'));
        });

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        try {
            $type    = CompanyEmailType::CANDIDATE_JOB_INVITATION;
            $company = $jobPost->company;

            $placeholders = [
                "[COMPANY_NAME]"     => data_get($company, 'name', ''),
                "[JOB_TITLE]"        => e(data_get($jobPost, 'title', '')),
                "[SENDER_NAME]"      => data_get($this, 'user.email', ''),
                "[JOB_DETAILS_LINK]" => jobDetailsRoute($jobPost),
                "[JOB_APPLY_LINK]"   => jobDetailsRoute($jobPost),
            ];

            $subject = app(CompanyEmailService::class)->getSubject($type, $placeholders);
            $body    = app(CompanyEmailService::class)->getBody($type, $placeholders);

            $this->jobCandidateService->inviteCandidateToJob($jobPost, [
                'name' => $candidateName,
                'email' => $request->get('email'),
                'subject' => $subject,
                'body' => $body
            ]);

            $companyEmailService = new CompanyEmailService($this->company);
            $companyEmailService->sendJobApplyInviteEmail($request->get('email'), $jobPost, $this->user());

            return $this->successResponse([], __('responses.job.candidate.invitation_link_send_candidate'));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    /**
     * Delete Invite Candidate
     * @param JobPost $job
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteCandidate(Request $request, $job)
    {
        try {
            $jobPost = $request->get('jobPost');
            if (!$this->hasPermission(UserPermission::CANDIDATE_ORGANIZE, $jobPost->company_id, $jobPost->id)) {
                return $this->unauthorizedAccess();
            }

            $this->jobCandidateService->deleteCandidate($jobPost, $this->user(), data_get($request->all(), 'candidates', []));

            return $this->successResponse([], __('responses.candidate.applicant_remove'));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    /**
     * Get Pending Candidate List
     * @param JobPost $job
     * @return JsonResponse
     */
    public function pendingCandidates(Request $request, ApplicantTransformer $applicantTransformer, $job)
    {
        $jobPost = $request->get('jobPost');

        if (!$this->hasPermission(UserPermission::CANDIDATE_VIEW, $jobPost->company_id, $jobPost->id)) {
            return $this->unauthorizedAccess();
        }

        $applicants = $this->jobCandidateService->getPendingCandidates($jobPost->id);

        return $this->successResponse(
            $applicantTransformer->pendingApplicantList($applicants)
        );
    }

    /**
     * Invite Pending Candidate
     * @param JobPost $job
     * @param Request $request
     * @return JsonResponse|void
     * @throws Throwable
     */
    public function invitePendingCandidates(Request $request, $job)
    {
        $jobPost = $request->get('jobPost');

        $applicants = JobApplicant::has('jobPost')->whereJobPostId($jobPost->id)
            ->where('id', $request->get('candidates'))
            ->where('status', JobApplyStatus::PENDING)
            ->get();
        if (!blank($applicants)) {
            $emailService = new CompanyEmailService($jobPost->company);
            foreach ($applicants as $applicant) {
                $emailService->sendJobApplyInviteEmail(data_get($applicant, 'user.email'), $jobPost, $this->user());
            }

            return $this->successResponse([], __('responses.job.candidate.invitation_link_send_candidates'));
        }
    }

    /**
     * Export job candidate
     * @param Request $request
     * @return JsonResponse|BinaryFileResponse
     */
    public function exportCandidate(Request $request)
    {
        ini_set('max_execution_time', 0);
        ini_set('memory_limit', '-1');

        $user = app(AuthService::class)->getUserFromToken($request->get('token'));
        if (!$user) {
            return $this->errorResponse([], __('responses.invalid_token'));
        }

        $jobPost = JobPost::where(['slug' => $request->route('job')])->first();
        if (blank($jobPost)) {
            return $this->errorResponse([], __('responses.context_not_found', ['context' => 'Job']));
        }

        if (!$this->hasPermission(UserPermission::CANDIDATE_VIEW, $jobPost->company_id, $jobPost->id, $user)) {
            return $this->unauthorizedAccess();
        }

        try {
            $candidates = $this->jobCandidateService->getFilteredCandidate($jobPost, $request->all());

            ['path' => $path, 'downloadFileName' => $downloadFileName] = $this->jobCandidateService->getFilteredCandidateXml($jobPost, $candidates);

            return response()->download($path, $downloadFileName)->deleteFileAfterSend();
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

}
