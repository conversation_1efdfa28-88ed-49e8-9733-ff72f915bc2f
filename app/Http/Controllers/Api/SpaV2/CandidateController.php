<?php

namespace App\Http\Controllers\Api\SpaV2;

use App\Enums\JobApplyStatus;
use App\Enums\JobStatus;
use App\Enums\MessageReferenceType;
use App\Events\NewMessageMail;
use App\Enums\PipelineType;
use App\Http\Requests\Candidate\CandidateEducationQualificationRequest;
use App\Http\Requests\Candidate\CandidateEmploymentRequest;
use App\Http\Requests\Candidate\EducationReOrderRequest;
use App\Http\Requests\Candidate\EmploymentReOrderRequest;
use App\Http\Requests\Candidate\ResumeRequest;
use App\Http\Requests\Candidate\StoreCandidateInfoRequest;
use App\Models\ApplicantAssessment;
use App\Models\CandidateContacts;
use App\Models\Company;
use App\Models\Education;
use App\Models\Employment;
use App\Models\JobApplicant;
use App\Models\JobPost;
use App\Models\Messaging;
use App\Models\SocialLink;
use App\Models\User;
use App\Services\CandidateService;
use App\Transformer\UserTransformer;
use Carbon\Carbon;
use DB;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Log;

class CandidateController extends ApiController
{
    private CandidateService $candidateService;

    public function __construct(CandidateService $candidateService)
    {
        parent::__construct();

        $this->candidateService = $candidateService;
    }

    public function getCandidateInfo()
    {
        $user = $this->user();

        $socials = SocialLink::where('user_id', $user->id)->get()->reduce(function ($socials, $social) {
            $socials[$social->social_profile_id] = $social->link;
            return $socials;
        }, []);

        $response = [
            'candidate' => [
                'first_name' => data_get($user, 'first_name'),
                'email' => data_get($user, 'email'),
                'nationality' => data_get($user, 'nationality'),
                'dob' => $user->dob,
                'last_name' => data_get($user, 'last_name'),
                'country_id' => data_get($user, 'country_id'),
                'mobile_number' => data_get($user, 'mobile_number'),
                'identity_number' => data_get($user, 'identity_number'),
                'gender' => $user->getRawOriginal('gender'),
                'social_link' => $socials,
                'intro_video_link' => data_get($user, 'intro_video_link'),
                'current_salary' => getCurrentSalary($user),
                'expected_salary' => getExpectedSalary($user),
                'time_zone' => data_get($user, 'time_zone', 'UTC'),
                'provider' => data_get($user, 'provider'),
                'can_change_password' => data_get($user, 'can_change_password', true),
            ],
            'additional' => [
                'gender' => $user->gender,
                'linkedin' => data_get($socials, 1),
                'facebook' => data_get($socials, 2),
                'twitter' => data_get($socials, 3),
                'dob' => $user->dob ? $user->dob->format('d M Y') : '',
                'photo' => data_get($user, 'profile_image'),
                'hasPhoto' => !!$user->getMedia('avatar')->first(),
            ],
        ];

        return $this->successResponse($response);
    }

    public function storeCandidateInfo(StoreCandidateInfoRequest $request)
    {
        $user = $this->user();

        $userData = $request->all();
        $email = data_get($userData, 'email');
        unset($userData['email']);

        DB::beginTransaction();
        try {

            if (!blank($email) && $user->email != $email) {
                $candidate = $user->contacts()->where('type', CandidateContacts::CONTACT_TYPE_EMAIL)->where('contact', $email)->first();
                if (blank($candidate)) {
                    $user->contacts()->where('type', CandidateContacts::CONTACT_TYPE_EMAIL)->update(['is_active' => false]);
                    $user->contacts()->create([
                        'contact' => $email,
                        'type' => CandidateContacts::CONTACT_TYPE_EMAIL,
                        'is_active' => true,
                    ]);
                } else {
                    $candidate->is_active = true;
                    $candidate->save();
                }
            }

            $userData['name'] = $request->get('first_name').' '.$request->get('last_name');

            if (!blank($request->get('dob'))) {
                $userData['dob'] = Carbon::parse($request->get('dob'))->format('Y-m-d');
            }

            $userData['country_id'] = data_get($request, 'country_id');
            $userData['intro_video_link'] = data_get($request, 'intro_video_link');
            $user->fill($userData);
            $user->save();

            foreach ($request->get('social_link', []) as $key => $link) {

                if ($key == 0) {
                    continue;
                }

                $social = SocialLink::where([
                    'user_id' => $user->id,
                    'social_profile_id' => $key,
                ])->first();

                if (blank($social)) {
                    SocialLink::create([
                        'user_id' => $user->id,
                        'social_profile_id' => $key,
                        'link' => $link,
                    ]);
                } else {
                    $social->update([
                        'link' => $link,
                    ]);
                }

            }
            $user = $user->load('companies');

            $serializeCandidate = (new UserTransformer())->serializeStateUserData($user);

            DB::commit();
            return $this->successResponse($serializeCandidate, __('responses.context_updated', ['context' => 'Candidate']));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return $this->errorResponse([],  __('responses.context_cannot_be_saved', ['context' => 'Candidate']));
        }
    }

    public function saveCandidatePhoto(Request $request)
    {
        $user = $this->user();

        if ($request->hasFile('photo')) {

            $user->addMediaFromRequest('photo')->toMediaCollection('avatar');

            $user = $user->load('companies');

            $serializeUser = (new UserTransformer())->serializeStateUserData($user);

            return $this->successResponse($serializeUser, __('responses.context_updated', ['context' => 'Profile photo']));
        }

        return $this->errorResponse([], __('responses.context_not_found', ['context' => 'Sorry, Image']));
    }


    public function getCandidateEducation(Request $request)
    {
        $educationData   = $this->candidateService->getEducations($this->user(), $request);
        if (is_null($educationData)) {
            return $this->errorResponse([], __('responses.context_not_found', ['context' => 'Education history']));
        }

        return $this->successResponse($educationData);

    }

    public function storeCandidateEducation(CandidateEducationQualificationRequest $request): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->candidateService->saveCandidateEducation($this->user(), $request), __('responses.updated')
            );
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.context_cannot_be_saved', ['context' => 'Education history']));
        }
    }

    public function reOrderCandidateEducation(EducationReOrderRequest $request)
    {
        $educationIds = collect($request->get('educations'))->pluck('id');

        $count = Education::where('user_id', auth()->user()->id)
            ->whereIn('id', $educationIds)
            ->count();

        if ($count !== count($educationIds)) {
            return $this->errorResponse([], __('responses.unauthorized_access'));
        }

        try {
            $this->candidateService->reOrderCandidateEducation($request->get('educations', []));

            return $this->successResponse([], __('responses.sorted'));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.context_cannot_be_sorted', ['context' => 'Education history']));
        }
    }

    public function deleteCandidateEducation($educationId)
    {
        try {
            Education::where(['user_id' => $this->user()->id, 'id' => $educationId])->delete();

            return $this->successResponse([], __('responses.deleted'));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    public function getCandidateEmployment(Request $request): JsonResponse
    {
        $employmentData   = $this->candidateService->getEmployments($this->user(), $request);
        if (is_null($employmentData)) {
            return $this->errorResponse([], __('responses.employment.not_found'));
        }

        return $this->successResponse($employmentData);

    }

    public function storeCandidateEmployment(CandidateEmploymentRequest $request): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->candidateService->saveEmploymentHistory($this->user(), $request), __('responses.updated')
            );
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.context_cannot_be_saved', ['context' => 'Employment history']));
        }
    }

    public function reOrderCandidateEmployment(EmploymentReOrderRequest $request)
    {
        $employmentIds = collect($request->get('employments'))->pluck('id');

        $count = Employment::where('user_id', auth()->user()->id)
            ->whereIn('id', $employmentIds)
            ->count();

        if ($count !== count($employmentIds)) {
            return $this->errorResponse([], __('responses.unauthorized_access'));
        }

        try {
            $this->candidateService->reOrderCandidateEmployment($request->get('employments', []));

            return $this->successResponse([], __('responses.sorted'));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.context_cannot_be_sorted', ['context' => 'Employment history']));
        }
    }


    public function deleteCandidateEmployment($employmentId): JsonResponse
    {
        try {
            Employment::where(['user_id' => $this->user()->id, 'id' => $employmentId])->delete();

            return $this->successResponse([], __('responses.deleted'));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }


    public function getCandidateStats(Request $request)
    {
        $user = $this->user();
        $company = $request->get('domain_company');

        $appliedJobsQuery = JobApplicant::has('job')
            ->where('user_id', $user->id)
            ->where('status', JobApplyStatus::COMPLETE)
            ->whereHas('company', function (Builder $query) use ($company) {
                $query->whereIsEnterprise(false);
                if ($company) {
                    $query->where('id', $company->id);
                }
            });

        $appliedJobs = $appliedJobsQuery->get();

        $newMessages = 0;
        if ($appliedJobs->count() > 0) {
            $newMessages = Messaging::query()
                ->whereIn('job_applicant_id', $appliedJobs->pluck('id'))
                ->whereIn('reference', [MessageReferenceType::JOB_APPLICANT, MessageReferenceType::JOB_APPLICANT_ASSESSMENT, MessageReferenceType::JOB_APPLICANT_INTEGRATION_ASSESSMENT])
                ->whereNotNull('manager_id')
                ->notSeen()
                ->when($company, function ($query) use ($company) {
                    $query->whereHas('jobApplicant.company', function ($q) use ($company) {
                        $q->where('id', $company->id);
                    });
                })
                ->count();
        }

        $unfinishedJobsCount = JobPost::join('job_applicants', function ($join) use ($user) {
            $join->on('job_applicants.job_post_id', 'job_posts.id');
            $join->where('job_applicants.status', JobApplyStatus::PENDING);
            $join->where('job_applicants.user_id', $user->id);
            $join->whereNull('job_applicants.deleted_at');
        })
            ->whereHas('company', function (Builder $query) use ($company) {
                $query->whereIsEnterprise(false);
                if ($company) {
                    $query->where('id', $company->id);
                }
            })
            ->where('job_posts.expire_at', '>', Carbon::now(getTimeZone())->subDays(1))
            ->whereNull('job_applicants.deleted_at')
            ->count();

        $userAppliedJobsCount = $user->appliedJobs()
            ->wherePivot('status', '!=', JobApplyStatus::PENDING)
            ->where('job_posts.status', '!=', JobStatus::DELETED)
            ->when($company, function ($query) use ($company) {
                return $query->where('job_posts.company_id', $company->id);
            })
            ->count();

        $data = [
            'unfinishedJobs' => $unfinishedJobsCount,
            'appliedJobs' => $userAppliedJobsCount,
            'newMessages' => $newMessages,
            'notifications' => $this->getUserNotificationListQuery()->count(),
        ];

        return $this->successResponse($data);
    }

    private function getUserNotificationListQuery()
    {
        return app(CandidateService::class)->getCandidateNotifications($this->user());
    }


    public function getUnfinishedJobs(Request $request)
    {
        $company = $request->get('domain_company');

        $unfinishedJobs = JobPost::join('job_applicants', function ($join) {
                $join->on('job_applicants.job_post_id', 'job_posts.id');
                $join->where('job_applicants.status', JobApplyStatus::PENDING);
                $join->where('job_applicants.user_id', $this->user()->id);
                $join->whereNull('job_applicants.deleted_at');
            })
            ->when($company, function ($query) use ($company) {
                return $query->where('job_posts.company_id', $company->id);
            })
            ->whereHas('company', function (Builder $query) {
                $query->whereIsEnterprise(false);
            })
            ->where('job_posts.expire_at', '>', Carbon::now(getTimeZone())
            ->subDays(1))->get(['job_posts.*', 'job_applicants.id as application_id'])
            ->map(function ($job) {
                return [
                    'id' => data_get($job, 'id'),
                    'application_id' => data_get($job, 'application_id'),
                    'title' => data_get($job, 'title'),
                    'companyName' => $job->company->name,
                    'deadline' => data_get($job, 'expire_at')->format(config('easyjob.user.date_format')),
                    'daysLeft' => data_get($job, 'expire_at')->diffForHumans(),
                    'vacancy' => data_get($job, 'vacancies'),
                    'location' => data_get($job, 'location'),
                    'applyLink' => jobApplyRoute($job),
                    'url' => jobDetailsRoute($job),

                ];
            });
        return $this->successResponse($unfinishedJobs);
    }

    public function getAssessments(Request $request)
    {
        $company = $request->get('domain_company');

        $assessments = ApplicantAssessment::whereHas('applicant', function ($q) {
            $q->where('user_id', $this->user()?->id)
                ->whereNotIn('status', [JobApplyStatus::REJECT, JobApplyStatus::SELECTED]);
        })
        ->has('job')
        ->with('job.company', 'questionGroup')
        ->whereHas('applicant.company', function (Builder $query) use ($company) {
            $query->whereIsEnterprise(false);
            if ($company) {
                $query->where('id', $company->id);
            }
        })
        ->whereNull('started_at')
        ->orderBy('expire_date', 'desc')
        ->paginate(5);

        $assessments->getCollection()->transform(function ($assessment) {
            return [
                'title' => data_get($assessment, 'questionGroup.name'),
                'jobTitle' => data_get($assessment, 'job.title'),
                'companyName' => data_get($assessment, 'job.company.name'),
                'daysLeft' => $assessment->expire_date->diffForHumans(),
                'deadline' => data_get($assessment, 'expire_date')->format(config('easyjob.user.date_format')),
                'applyLink' => jobAssessmentRouteDashboard($assessment->job, $assessment->applicant->generated_id, $assessment->id),
                'duration' => data_get($assessment, 'questionGroup.exam_duration', 0),
            ];
        });
        return $this->successResponse($assessments);
    }

    public function deleteApplication($applicantId)
    {
        try {
            JobApplicant::where(['user_id' => $this->user()->id, 'id'=> $applicantId])->delete();

            return $this->successResponse([], __('responses.candidate.application_discarded'));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }


    public function getAppliedJobs(Request $request)
    {
        $company = $request->get('domain_company');

        $appliedJobs = $this->user()->appliedJobs()
            ->wherePivot('status', '!=', JobApplyStatus::PENDING)
            ->where('job_posts.status', '!=', JobStatus::DELETED)
            ->when($company, function ($query) use ($company) {
                return $query->where('job_posts.company_id', $company->id);
            })
            ->orderBy('submitted_at', 'DESC')
            ->paginate(12);

        $appliedJobs->transform(function ($job) {
                return [
                    'id' => data_get($job, 'id'),
                    'title' => data_get($job, 'title'),
                    'companyName' => $job->company->name,
                    'applyDate' => Carbon::parse($job->pivot->submitted_at)->format(config('easyjob.user.date_format')),
                    'daysLeft' => data_get($job, 'expire_at')->diffForHumans(),
                    'vacancy' => data_get($job, 'vacancies'),
                    'location' => data_get($job, 'location'),
                    'applyLink' => jobApplyRoute($job),
                    'url' => jobDetailsRoute($job),
                    'currentPipeline' => $this->applicantCurrentPipeline($job),
                ];
            });
        return $this->successResponse($appliedJobs);
    }

    private function applicantCurrentPipeline($job) {
        $jobPipelineId = data_get($job, 'pivot.job_pipeline_id');

        return $job->pipelines()->where('id', $jobPipelineId)->whereIn('type', [PipelineType::APPLIED, PipelineType::SELECTED, PipelineType::REJECTED])->first();
    }

    public function getResume()
    {
        $resume['url'] = '';
        $resume['ext'] = '';
        if ($this->user()->resume_url) {
            $resume['url'] = $url = $this->user()->resume_url;
            $resume['ext'] = @pathinfo($url)['extension'];
        }

        return $this->successResponse($resume);
    }

    public function saveResume(ResumeRequest $request)
    {
        try {
            $resume = [];
            $this->user()->addMediaFromRequest('resume')->toMediaCollection('resume');
            if ($this->user()->resume_url) {
                $resume['url'] = $url = $this->user()->resume_url;
                $resume['ext'] = @pathinfo($url)['extension'];
            }

            return $this->successResponse($resume, __('responses.context_updated', ['context' => 'Resume']));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse($request->all(), __('responses.something_went_wrong'));
        }
    }

    public function removeResume(Request $request)
    {
        try {
            $resume = $this->user()->getMedia('resume');
            if ($resume->count()) {
                $resume->map(function ($res) {
                    $res->delete();
                });
            }

            return $this->successResponse([], __('responses.context_removed', ['context' => 'Resume']));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse($request->all(), __('responses.something_went_wrong'));
        }
    }

    public function removeCandidatePhoto(Request $request)
    {
        try {
            $user = $this->user();

            $avatar = $user->getMedia('avatar');
            if ($avatar->count()) {
                $avatar->map(function ($photo) {
                    $photo->delete();
                });
            }

            $user = User::find($user->id);
            $user = $user->load('companies');

            $serializeUser = (new UserTransformer())->serializeStateUserData($user);

            return $this->successResponse($serializeUser, __('responses.context_removed', ['context' => 'Profile photo']));
        } catch (Exception $e) {
            Log::error($e);
            return $this->errorResponse($request->all(), __('responses.something_went_wrong'));
        }
    }

    public function deleteJob(JobPost $job)
    {
        $applicant = JobApplicant::where(['job_post_id' => $job->id, 'user_id' => $this->user()->id])->first();
        if (blank($applicant)) {
            return $this->errorResponse([], __('responses.context_not_found', ['context' => 'Job applicant']));
        }

        DB::beginTransaction();
        try {
            $this->candidateService->deleteCandidateJobApplication($applicant);
            DB::commit();

            return $this->successResponse([], __('responses.context_deleted', ['context' => 'Job']));
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
            return $this->errorResponse([], __('responses.something_went_wrong'));
        }
    }

    public function getAssessmentLoginUrl(ApplicantAssessment $assessment)
    {
        return $this->successResponse([
            'url' => jobAssessmentRouteDashboard($assessment->job, $assessment->applicant->generated_id, $assessment)
        ]);
    }

}
