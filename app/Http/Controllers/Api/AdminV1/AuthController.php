<?php

namespace App\Http\Controllers\Api\AdminV1;

use App\Enums\AdminRole;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Validator;

/**
 * Class AuthController
 * @package App\Http\Controllers\Api\AdminV1
 */
class AuthController extends ApiController
{

    public function __construct()
    {
        parent::__construct();
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => ['required', 'string', 'email', 'max:100'],
            'password' => ['required', 'string', 'min:6'],
        ]);

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        $admin = Admin::where('email', $request->get('email'))->first();

        if (blank($admin)) {
            return $this->errorResponse($request->all(), 'Email not exist', 404);
        }

        $passwordMatched = Hash::check($request->get('password'), $admin->password);

        if (!$passwordMatched) {
            return $this->errorResponse($request->all(), "Password doesn't match");
        }

        $admin->accessToken = $admin->createToken(Admin::ACCESS_TOKEN)->accessToken;
        return $this->successResponse($this->serializeLoginData($admin), 'Logged in successfully!');
    }


    private function serializeLoginData($admin)
    {
        $props = [
            'id', 'name', 'email', 'role', 'permission'
        ];

        $data = [];
        foreach ($props as $prop) {
            $data[$prop] = data_get($admin, $prop);
        }

        $data['role_name'] = getEnumValue(AdminRole::class, $admin->role, true);
        $data['token'] = @$admin->accessToken;

        return $data;
    }
}
