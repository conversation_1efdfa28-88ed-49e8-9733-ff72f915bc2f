<?php

namespace App\Http\Controllers\Api\AdminV1\Report;

use App\Enums\AdminDateFilter;
use App\Enums\PaymentMethod;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Exports\CompanyReportExport;
use App\Exports\TransactionReportExport;
use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Services\AdminReportService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class TransactionReportController extends Controller
{
    public function exportTransactions(Request $request)
    {
        $search = $request->get('query');
        $type = $request->get('type');
        $status = $request->get('status');
        $gatewayType = $request->get('gatewayType');
        $hundredPercentCoupon = $request->get('hundredPercentCoupon');
        $subscriptionPackage = $request->get('subscriptionPackage');

        // Initialize the query
        $query = Transaction::with(['user' => fn($q) => $q->withTrashed(), 'user.sourcePlatform', 'user.package'])
            ->where('price', '>', 0)
            ->when($search, function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->withTrashed()
                        ->where(function ($subQuery) use ($search) {
                            $subQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                        });
                });
            })
            ->when($subscriptionPackage, function ($q) use ($subscriptionPackage) {
                $q->whereHas('user.package', fn($subQuery) => $subQuery->where('id', $subscriptionPackage));
            })
            ->when($type, fn($q) => $q->where('type', $type))
            ->when(!blank($status), fn($q) => $q->where('status', $status))
            ->when($gatewayType, function ($q) use ($gatewayType) {
                if ($gatewayType == PaymentMethod::STRIPE) {
                    $q->whereNotNull('stripe');
                } elseif ($gatewayType == PaymentMethod::PAYPAL) {
                    $q->whereNotNull('paypal');
                }
            })
            ->when($hundredPercentCoupon, function ($q) {
                $q->where('total', 0)
                    ->where('price', '>', 0)
                    ->whereColumn('price', 'discount');
            });

        // Handle date filters
        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);

        if ($filter) {
            if ($filter == AdminDateFilter::CUSTOM) {
                $from = Carbon::parse($request->get('from'))->startOfDay();
                $to = Carbon::parse($request->get('to'))->endOfDay();
            } else {
                [$from, $to] = app(AdminReportService::class)->getFilterDates($filter);
            }
            $query->whereBetween('created_at', [$from, $to]);
        }

        $query->orderBy('created_at', 'DESC');

        $transactions = $query->get()->map(function ($transaction) {
            return [
                'name' => optional($transaction->user)->name,
                'email' => optional($transaction->user)->email,
                'package' => optional($transaction->user->package)->name,
                'price' => $transaction->price,
                'payment_method' => $this->getPaymentMethodName($transaction->payment_method),
                'status' => $this->getPaymentStatusName($transaction->status),
                'date' => $transaction->created_at->setTimezone('Asia/Dhaka')->format(config('easyjob.user.datetime_format')),
                'type' => $this->getPackageTypeName($transaction->type),
                //'total' => $transaction->total,
            ];
        })->toArray();

        return Excel::download(new TransactionReportExport($transactions), 'transaction-reports.xlsx', null, ['Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']);
    }
    private function getPackageTypeName($type)
    {
        return [
            PaymentType::SUBSCRIPTION => 'SUBSCRIPTION',
            PaymentType::RENEWAL => 'RENEWAL',
            PaymentType::PACKAGE_UPGRADE => 'PACKAGE_UPGRADE'
        ][$type];
    }

    private function getPaymentMethodName($method)
    {
        return [
            PaymentMethod::NONE => 'NONE',
            PaymentMethod::PAYPAL => 'PAYPAL',
            PaymentMethod::STRIPE => 'STRIPE'
        ][$method];
    }

    private function getPaymentStatusName($status)
    {
        return [
            PaymentStatus::PAID => 'PAID',
            PaymentStatus::DUE => 'DUE',
        ][$status];
    }
}
