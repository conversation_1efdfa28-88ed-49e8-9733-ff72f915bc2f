<?php

namespace App\Http\Controllers\Web\App;

use App\Events\NewMessageMail;
use App\Http\Controllers\Controller;
use App\Models\JobApplicant;
use App\Models\User;
use App\Models\UserSetting;
use App\Services\ConversationService;
use App\Services\Integration\CalendlyService;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Log;

class WebhookController extends Controller
{
    private CalendlyService $calendlyService;

    public function __construct(CalendlyService $calendlyService)
    {
        $this->calendlyService = $calendlyService;
    }

    public function calendlyWebHook(Request $request)
    {
        return $this->payload($request->all());
    }

    private function payload($payload) {
        $eventType = data_get($payload, 'event');
        $eventUrl = data_get($payload, 'payload.event');
        $uuid = data_get($payload, 'payload.tracking.utm_content');

        if(blank($uuid)) {
            return false;
        }

        if($eventType === 'invitee.created') {
            $userSetting = UserSetting::where('uuid', $uuid)->first();
            if(!blank($userSetting)) {
                try {
                    $meta = json_decode($userSetting->value, true);

                    $applicantId = $meta['applicant_id'] ?? null;
                    $managerUserId = $meta['manager_id'] ?? null;

//                    $url = $meta['url'] ?? '';
//                    if($url) {
//                        $url = explode('?', $url)[0];
//                    }

                    $user = User::find($userSetting->user_id);
                    $applicant = JobApplicant::find($applicantId);

                    if(!blank($user) && !blank($applicant)) {

                        $jobPostTitle = $applicant?->jobPost?->title;

                        $this->calendlyService->setUser($managerUserId);
                        $info = $this->calendlyService->getEventInfo($eventUrl);

                        $startTime = "";
                        $endTime = "";
                        $timezone = data_get($payload, 'payload.timezone', 'UTC');

                        if(!blank($info)) {
                            $eventData = data_get($info, 'resource');
                            $startDateTime = Carbon::parse($eventData['start_time'])->setTimezone($timezone);
                            $endDateTime = Carbon::parse($eventData['end_time'])->setTimezone($timezone);

                            $startTime = Carbon::parse($startDateTime)->format('d M Y H:i A');
                            $endTime = Carbon::parse($endDateTime)->format('d M Y H:i A');
                        }

                        $msg = "Calendly scheduled details:<br/> Job title: $jobPostTitle.<br/>Start Time: $startTime. <br/>End Time: $endTime. <br/>Time Zone: $timezone.";

                        $message = (new ConversationService())->saveCandidateJobMessage($user, $applicant, ['message' => $msg]);

                        $notificationService = new NotificationService($applicant->company);
                        $notificationService->newMessageNotificationToManager($applicant, $user);

                        event(new NewMessageMail($message, $user));
                    }
                } catch (\Exception $e) {
                    Log::error($e);
                }
            }
        }
    }
}
