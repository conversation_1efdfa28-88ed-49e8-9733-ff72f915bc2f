<?php

namespace App\Http\Controllers\Web\App;

use App\Enums\CheckoutProcessStatus;
use App\Enums\JobStatus;
use App\Enums\PackagePlan;
use App\Enums\PaymentMethod;
use App\Enums\PaymentType;
use App\Enums\UserType;
use App\Http\Controllers\Controller;
use App\Jobs\UpdateApplicantInterestStatus;
use App\Jobs\UpdateDefaultPaymentMethod;
use App\Jobs\ZohoSyncDeal;
use App\Mail\SubscriptionFailedMail;
use App\Mail\SubscriptionSuccessMail;
use App\Models\Coupon;
use App\Models\JobPost;
use App\Models\Package;
use App\Models\Transaction;
use App\Models\User;
use App\Notifications\GeneralActivity;
use App\Services\AffiliationService;
use App\Services\PackageService;
use App\Services\PaymentService;
use App\Services\SubscriptionService;
use App\Services\TransactionService;
use App\Services\ZohoCRMService;
use App\Utils\EjLogger;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Log;
use Mail;
use Stripe\Event;

class PaymentController extends Controller
{
    public function showCheckout(Request $request, $package_id)
    {
        $reqPackage = Package::find($package_id);
        $user = auth()->user();
        $currentPackage = $user->package;

        if ($currentPackage->price > 0) {
            $desc = $currentPackage->name . " " . __('to') . " " . $reqPackage->name;
        } else {
            $desc = __('New subscription to') . " " . $reqPackage->name;
        }

        if (blank($reqPackage)) {
            return redirect()->back()->with('error', __('Subscription package not found'));
        }

        if (!blank($currentPackage) && (data_get($currentPackage, 'price') > data_get($reqPackage, 'price'))) {
            return redirect()->back()->with('error', __('Subscription package downgrade is not possible.'));
        }

        $billingAddress = $user->billingAddress();
        if (!$billingAddress) {
            setIntendedUrl();
            return redirect()->route('user.account', ['tab' => 'billing', 'form' => 'billing'])->with('error', __('Please update your billing information.'));
        }

        return view('checkout.show', compact('reqPackage', 'user', 'billingAddress', 'currentPackage', 'desc'));
    }

    public function submitCheckout(Request $request)
    {
        try {
            $paymentService = app(PaymentService::class);

            $user = auth()->user();

            $package = Package::find($request->get('package_id'));

            $srcId = $request->get('stripeSource');
            $source = $paymentService->getStripeSource($srcId);

            $paymentService->createOrUpdateStripeCustomer($request, $source);

            // saving data to user table
            $user->card_brand = $source->card->brand;
            $user->card_last_four = $source->card->last4;
            $user->card_holder_name = $request->card_name;
            $user->save();

            // 3d secure payment process
            if ($source->card->three_d_secure == 'required') {
                $source = $paymentService->createStripeSource($srcId, $package, $user);
                return redirect($source->redirect->url);
            }

            // normal payment process
            if ($source->status == 'chargeable') {
                $subscription = $paymentService->createStripeSubscription($package);

                if ($subscription->status == 'active') {
                    return redirect()->route('user.account')->with('success', __('Subscription will be updated within few seconds.'));
                } else {
                    return redirect()->back()->with('error', __('Sorry! Subscription :status', ['status' => $subscription->status]));
                }
            }

        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function paypalWebHook(Request $request)
    {
        $event_type = $request->get('event_type');
        Log::info($event_type);
        try {
            // Handle the event
            switch ($event_type) {
                case 'BILLING.SUBSCRIPTION.ACTIVATED':
                case 'PAYMENT.CAPTURE.COMPLETED':
                    $this->handlePaypalPaymentSuccess($request);
                    break;
                case 'BILLING.SUBSCRIPTION.UPDATED':
                    $this->handlePaypalSubscriptionUpdate($request);
                    break;
                case 'BILLING.SUBSCRIPTION.CANCELLED':
                    $this->handlePaypalSubscriptionCancel($request);
                    break;
                default:
                    EjLogger::payment()->info($event_type);
                    // Unexpected event type
                    http_response_code(200);
                    exit();
            }

            EjLogger::payment()->info(json_encode($request->all()));
            EjLogger::payment()->info("{$event_type} paypal hook trigger successful.");

            http_response_code(200);
        } catch (\Exception $e) {
            http_response_code(400);
            EjLogger::payment()->error($e->getMessage());
        }
    }

    private function handlePaypalPaymentSuccess($request)
    {
        try {
            if ($request->get('event_type') == 'PAYMENT.CAPTURE.COMPLETED') {
                $customId = data_get($request->get('resource'), 'custom_id');
                $user = User::with('package')->find($customId);
                $package = $user->package;
            } else {
                $customId = data_get($request->get('resource'), 'custom_id');
                $package = Package::whereJsonContains('paypal', ['plan' => data_get($request->get('resource'), 'plan_id')])->first();
            }

            $upgradeTitle = __('Subscription updated.');
            $upgradeMessage = __("Your subscription to :package updated.", ['package' => $package->name]);


            $transService = app(TransactionService::class);
            $user = User::where('id', $customId)->first();
            $transService->createTransactionForPaypal($user, $package, $request);

            \Notification::send($user, new GeneralActivity(
                $upgradeTitle,
                $upgradeMessage,
                appUrl('/my-account')
            ));

            dispatch(new ZohoSyncDeal($user->id));
            dispatch(new UpdateApplicantInterestStatus($user->id));

            $logData['message'] = 'payment_succeeded';
            //\Mail::to($user)->send(new SubscriptionSuccessMail($user));
            EjLogger::payment()->info(json_encode($logData));
        }catch (\Exception $e){
            Log::error($e);
        }
    }
    private function handlePaypalSubscriptionUpdate($request)
    {
        $user = User::where('id', data_get($request->get('resource'), 'custom_id'))->first();
        $package = Package::whereJsonContains('paypal', ['plan' => data_get($request->get('resource'), 'plan_id')])->first();

        try {

            $transService = app(TransactionService::class);
            $transService->createTransactionForPaypal($user, $package, $request);
            dispatch(new ZohoSyncDeal($user->id));
            dispatch(new UpdateApplicantInterestStatus($user->id));
        }catch (\Exception $e){
            \Log::error($e);
        }
    }

    private function handlePaypalSubscriptionCancel(Request $request)
    {
        $user = User::where('id', data_get($request->get('resource'), 'custom_id'))->first();

        try {

            app(PaymentService::class)->cancelPreviousSubscription($user);
            app(SubscriptionService::class)->subscribeToFreePackage($user);
            app(PackageService::class)->limitUserAccesses($user);

            $user->subscription_cancel_request = true;
            $user->save();

            dispatch(new UpdateApplicantInterestStatus($user->id));

        }catch (\Exception $e){
            \Log::error($e);
        }
    }
    public function stripeWebHook(Request $request)
    {
        $payload = @file_get_contents('php://input');
        try {
            $event = Event::constructFrom(
                json_decode($payload, true)
            );

            \Log::debug(json_encode($event));
        } catch (\UnexpectedValueException $e) {
            EjLogger::payment()->error($e->getMessage());
            // Invalid payload
            http_response_code(400);
            exit();
        }
        try {
             // Handle the event
            switch ($event->type) {
                case 'invoice.payment_succeeded':
                case 'invoice.payment_failed':
                    $this->handleStripePaymentSuccess($event);
                    break;
                case 'checkout.session.completed':
                case 'payment_intent.payment_failed':
                    $this->handleStripeCheckoutSuccess($event);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDelete($event);
                    break;

                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdate($event);
                    break;
                case 'charge.refunded':
                    return $this->handleChargeRefunded($event);
                case 'refund.updated':
                case 'refund.failed':
                    return $this->handleRefundUpdated($event);
                default:
                    // Unexpected event type
                    http_response_code(400);
                    exit();
            }

            EjLogger::payment()->info("{$event->type} stripe hook trigger successful.");

            http_response_code(200);
        } catch (\Exception $e) {
            http_response_code(400);
            EjLogger::payment()->error($e->getMessage());
        }
    }

    private function handleStripePaymentSuccess($event)
    {
        $invoice = $event->data->object;

        if ($invoice->object == 'invoice') {
            $planId = data_get(data_get($invoice, 'lines.data')[0], 'plan.id', '');
            // get stripe customer data from invoice
            $user = User::getStripeCustomer($invoice->customer);
            if ($user && $planId) {
                $package = Package::where('stripe->plan', $planId)->first();
                $logData = [
                    'mode' => 'subscription',
                    'user_name' => $user->name,
                    'user_email' => $user->email,
                    'package' => $package->only(['id', 'name', 'plan_name', 'price', 'discount', 'discounted_price']),
                    'message' => ""
                ];

                $coupon = null;

                // Subscribe to the package
                if ($invoice->paid) {
                    $subscriptionId = data_get($invoice, 'lines.data.0.subscription', '');
                    app(PaymentService::class)->cancelStripePreviousSubscriptions($user, $subscriptionId);

                    $subscriptionService = app(SubscriptionService::class);

                    $subscriptionService->subscribe($user, $package, null, $invoice->billing_reason);
                    $stripeCoupon = data_get($invoice, 'discount.coupon', false);

                    if ($stripeCoupon) {
                        $coupon = Coupon::where('code', $stripeCoupon->id)->first();
                        $coupon->applied_count = ($coupon->applied_count ?? 0) + 1;
                        $coupon->save();
                        $logData['coupon'] = $coupon->code;
                    }

                    // Save transaction data
                    $transService = app(TransactionService::class);
                    [$transId, $paymentType] = $transService->createTransaction($user, $invoice, $package);

                    // Sale in first promoter
                    if ($user->affiliate_promoter) {
                        $affiliationService = app(AffiliationService::class);
                        if ($paymentType != PaymentType::PACKAGE_UPGRADE) {
                            $affiliationService->trackSale($user, $package, $user->affiliate_promoter, data_get($invoice, 'amount_paid'), $transId);
                        } else {
                            $user->affiliate_promoter = null;
                        }
                    }

                    $user->type = UserType::EMPLOYER;
                    $user->subscription_cancel_request = 0;
                    $user->is_subscription_pause = 0;
                    $user->save();

                    updateUserStateVersion($user);

                    \Notification::send($user, new GeneralActivity(
                        __('Subscription updated.'),
                        __("Your subscription to :package updated.", ['package' => $package->name]),
                        appUrl('/my-account')
                    ));

                    $logData['message'] = 'payment_succeeded';
                    $appUrl = getPaymentAppUrl($user->company);

                    \Mail::to($user)->send(new SubscriptionSuccessMail($user, $appUrl));

                    EjLogger::payment()->info(json_encode($logData));
                } else {
                    if (!blank($user)) {
                        \Mail::to($user)->send(new SubscriptionFailedMail($user, $invoice));
                        $logData['message'] = 'payment_failed';
                        EjLogger::payment()->error(json_encode([$invoice->paid, $invoice]));

                        // Save transaction data
                        $transService = app(TransactionService::class);
                        $transService->createTransaction($user, $invoice, $package);
                    }

                }

            } else {
                EjLogger::payment()->error('User or plan not found');
            }

            if ($user) {
                try {
                    dispatch(new UpdateDefaultPaymentMethod($user->id));
                    dispatch(new ZohoSyncDeal($user->id));
                    dispatch(new UpdateApplicantInterestStatus($user->id));
                } catch (\Exception $e) {
                    EjLogger::payment()->error("Error dispatching jobs: " . $e->getMessage());
                }
            }
        } else {
            EjLogger::payment()->error('Invoice error');
        }
    }

    private function handleStripeCheckoutSuccess($event)
    {
        $object = $event->data->object;
        if ($object->object == 'checkout.session') {
            $plan = data_get($object, 'metadata.plan');
            // get stripe customer data from invoice
            $user = User::getStripeCustomer($object->customer);
            if ($user && $plan) {

                $package = Package::where('slug', $plan)->first();
                if ($package->plan === PackagePlan::LIFETIME) {

                    $logData = [
                        'mode' => 'payment',
                        'user_name' => $user->name,
                        'user_email' => $user->email,
                        'package' => $package->only(['id', 'name', 'plan_name', 'price', 'discount', 'discounted_price']),
                        'message' => "Package upgraded.",
                        'payment_status' => $object->payment_status,
                    ];

                    $coupon = null;
                    if ($object->payment_status === 'paid') {
                        $user->update([
                            'checkout_process_status' =>  CheckoutProcessStatus::UPGRADING
                        ]);
                        // cancel previous subscriptions
                        app(PaymentService::class)->cancelStripePreviousSubscriptions($user);

                        $subscriptionService = app(SubscriptionService::class);
                        $subscriptionService->subscribe($user, $package);

                        $customer = app(PaymentService::class)->getStripeCustomer($object->customer);
                        $stripeCoupon = data_get($customer, 'discount.coupon.id');

                        if ($stripeCoupon) {
                            $coupon = Coupon::where('code', $stripeCoupon)->first();
                            $coupon->applied_count = ($coupon->applied_count ?? 0) + 1;
                            $coupon->save();

                            $user->coupon_id = $coupon->id;
                            $user->save();

                            $logData['coupon'] = $coupon->code;
                        }

                        $user->fresh();

                        // Save transaction data
                        $transService = app(TransactionService::class);

                        [$transId, $paymentType] = $transService->createCheckoutTransaction($user, $object, $package, $coupon);

                        // Sale in first promoter
                        if ($user->affiliate_promoter) {
                            $affiliationService = app(AffiliationService::class);
                            if ($paymentType != PaymentType::PACKAGE_UPGRADE) {
                                $affiliationService->trackSale($user, $package, $user->affiliate_promoter, data_get($object, 'amount_total'), $transId);
                            } else {
                                $user->affiliate_promoter = null;
                            }
                        }

                        $user->type = UserType::EMPLOYER;
                        $user->subscription_cancel_request = 0;
                        $user->is_subscription_pause = 0;
                        $user->save();

                        \Notification::send($user, new GeneralActivity(
                            __('Subscription updated.'),
                            __("Your subscription to :package updated.", ['package' => $package->name]),
                            appUrl('/my-account')
                        ));
                        EjLogger::payment()->info(json_encode($logData));

                        $appUrl = getPaymentAppUrl($user->company);

                        Mail::to($user)->send(new SubscriptionSuccessMail($user, $appUrl));
                    } else {
                        EjLogger::payment()->error(json_encode([$object->payment_status, $object]));

                        // Save transaction data
                        $transService = app(TransactionService::class);
                        $transService->createCheckoutTransaction($user, $object, $package);
                    }

                } else {
                    $user->checkout_process_status = null;
                    $user->save();
                }
            } else {
                EjLogger::payment()->error('User or plan not found');
            }

            if ($user) {
                try {
                    dispatch(new UpdateDefaultPaymentMethod($user->id));
                    dispatch(new ZohoSyncDeal($user->id));
                    dispatch(new UpdateApplicantInterestStatus($user->id));
                } catch (\Exception $e) {
                    EjLogger::payment()->error("Error dispatching jobs: " . $e->getMessage());
                }
            }
        } else {
            EjLogger::payment()->error('Invoice error');
        }
    }

    public function paymentComplete(Request $request)
    {
        try {
            $paymentService = app(PaymentService::class);
            $source = $paymentService->getStripeSource($request->get('source'));

            $userId = data_get($source, 'metadata.user_id', '');

            if ($userId) {
                $user = User::find($userId);
                $company = $user->company;

                $packageId = data_get($source, 'metadata.package_id', '');
                $package = Package::find($packageId);

                if ($source->status == 'chargeable') {
                    $charge = $paymentService->createStripeChargeFromSource($source, $user);

                    if ($charge->paid == true) {

                        // Save transaction data
                        $transService = app(TransactionService::class);

                        $transService->createTransaction($user, $charge, $package);

                        $subscription = $paymentService->createStripeTrialSubscription($package);

                        return view('templates.easyjobs_blue.payment.success', compact('company'));
//                return redirect()->route('user.account')->with('success', __('Subscription updated.'));
                    }
                }
            }
        } catch (\Exception $exception) {

//            return redirect()->back()->with('error', __('Sorry! Subscription cannot update.'));
        }
        return view('templates.easyjobs_blue.payment.failed', compact('company'));
    }

    public function verifyPayment(Request $request)
    {
        $user = auth()->user();
        if ($user->package_validity >= Carbon::now()->toDateString()) {
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false]);
    }

    private function handleSubscriptionDelete(Event $event)
    {
        $stripeId = data_get($event, 'data.object.customer');
        $user = User::getStripeCustomer($stripeId);
        $subscriptions = [];
        if (!blank($allSubscriptions = app(PaymentService::class)->getCustomerSubscriptions($user->stripe_id))) {
            foreach ($allSubscriptions as $subscription) {
                $subscriptions [] = $subscription->id;
            }
        }

        $user->subscription_cancel_request = false;

        if (blank($subscriptions) && $user->checkout_process_status !== CheckoutProcessStatus::UPGRADING) {
            app(SubscriptionService::class)->subscribeToFreePackage($user);
            app(PackageService::class)->limitUserAccesses($user);
        }

        $user->checkout_process_status = null;
        $user->save();

    }

    private function handleSubscriptionUpdate(Event $event)
    {
        $stripeId = data_get($event, 'data.object.customer');
        $user = User::getStripeCustomer($stripeId);
        if (!blank($allSubscriptions = app(PaymentService::class)->getCustomerSubscriptions($user->stripe_id))) {

            $subscriptionPause  = false;
            $packageValidity = $user->package_validity;
            foreach ($allSubscriptions as $subscription) {
                $subscriptionPause = !is_null($subscription->pause_collection);
                if (is_null($subscription->pause_collection)) {
                   $packageValidity = $subscription->current_period_end;
                }
            }

            if ($subscriptionPause !== (bool)$user->is_subscription_pause) {
                $data = ['is_subscription_pause' => $subscriptionPause, 'subscription_cancel_request' => false];
                if ($subscriptionPause) {
                    $this->handelJobPosts($user);
                } else {
                    $data['package_validity'] = Carbon::parse($packageValidity);
                    $data['subscription_cancel_request'] = false;
                }


                $user->update($data);
                  foreach ($user->companies as $company) {
                      foreach ($company->companyManagers as $manager)
                      updateUserStateVersion($manager->user);

                  }
            }

            dispatch(new UpdateApplicantInterestStatus($user->id));
        }
    }

    private function handelJobPosts(User $user)
    {
        $companies = $user->myCompanies;
        foreach ($companies as $company) {
            try {
                $activeJobCount = $company->jobs()
                    ->where('status', JobStatus::PUBLISHED)
                    ->count();

                if ($activeJobCount > 0) {
                    $latestActiveJob = JobPost::where('company_id', $company->id)
                        ->where('status', JobStatus::PUBLISHED)
                        ->orderBy('id', 'DESC')->first();

                    JobPost::where('company_id', $company->id)
                        ->where('id', '!=', $latestActiveJob->id)
                        ->where('status', JobStatus::PUBLISHED)
                        ->update(['status' => JobStatus::ARCHIVED]);
                }

            } catch (Exception $e) {
                Log::error($e);
            }
        }
    }

    /**
     * Handle charge.refunded event from Stripe
     *
     * @param Event $event The Stripe event object
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleChargeRefunded(Event $event)
    {
        try {
            // Safely extract charge data from the event
            $charge = $event->data->object ?? null;

            if (!$charge || !isset($charge->id)) {
                EjLogger::payment()->error('Invalid charge refund event data: ' . json_encode($event));
                return response()->json(['error' => 'Invalid event data'], 400);
            }

            $chargeId = $charge->id;

            // Try to find the transaction by looking at both trans_id and payment_intent in the stripe JSON
            $transaction = $this->findTransactionByChargeId($chargeId);

            if (!$transaction) {
                EjLogger::payment()->warning('Refund event: transaction not found for charge_id: ' . $chargeId . '. Attempting to create refund transaction directly.');

                // Try to get charge details from Stripe
                try {
                    $paymentService = app(PaymentService::class);
                    $stripeCharge = $paymentService->getStripeCharge($chargeId);

                    if (isset($stripeCharge->customer)) {
                        $customerId = $stripeCharge->customer;
                        $user = User::where('stripe_id', $customerId)->first();

                        if ($user) {
                            // Create a refund transaction directly
                            $amountRefunded = isset($charge->amount_refunded) ? ($charge->amount_refunded / 100) : 0;
                            $refundData = isset($charge->refunds) && isset($charge->refunds->data) ? $charge->refunds->data : [];
                            $refundIds = [];

                            foreach ($refundData as $refund) {
                                if (isset($refund->id)) {
                                    $refundIds[] = $refund->id;
                                }
                            }

                            $refundTransaction = $this->createDirectRefundTransaction($user, $chargeId, $amountRefunded, $refundIds);

                            if ($refundTransaction) {
                                EjLogger::payment()->info('Successfully created direct refund transaction #' . $refundTransaction->trans_id);
                                return response()->json(['status' => 'success', 'message' => 'Direct refund transaction created']);
                            }
                        } else {
                            EjLogger::payment()->error('User not found for Stripe customer ID: ' . $customerId);
                        }
                    } else {
                        EjLogger::payment()->error('Customer ID not found in Stripe charge: ' . $chargeId);
                    }
                } catch (\Exception $e) {
                    EjLogger::payment()->error('Error retrieving charge information: ' . $e->getMessage());
                }

                return response()->json(['error' => 'Transaction not found and could not create direct refund'], 404);
            }

            EjLogger::payment()->info('Found transaction #' . $transaction->id . ' with trans_id: ' . $transaction->trans_id . ' for charge: ' . $chargeId);

            // Get refund data safely
            $amountRefunded = isset($charge->amount_refunded) ? ($charge->amount_refunded / 100) : 0; // from cents to dollars
            $refundData = isset($charge->refunds) && isset($charge->refunds->data) ? $charge->refunds->data : [];

            // Log the refund data for debugging
            EjLogger::payment()->info('Refund data found in charge.refunded event: ' . json_encode($refundData));

            // If we have specific refunds in the data, create a transaction for each one
            if (!empty($refundData)) {
                $createdRefunds = 0;

                foreach ($refundData as $refund) {
                    if (!isset($refund->id)) {
                        continue; // Skip if no refund ID
                    }

                    $refundId = $refund->id;
                    $refundAmount = isset($refund->amount) ? ($refund->amount / 100) : 0;
                    $refundStatus = isset($refund->status) ? $refund->status : 'succeeded';
                    $refundReason = isset($refund->reason) ? $refund->reason : null;
                    $balanceTransaction = isset($refund->balance_transaction) ? $refund->balance_transaction : null;

                    EjLogger::payment()->info('Processing individual refund: ' . $refundId . ', amount: ' . $refundAmount);

                    // Create a transaction for this specific refund
                    $refundTransaction = $this->createRefundTransaction(
                        $transaction,
                        $chargeId,
                        $refundAmount,
                        [$refundId],
                        $refundStatus,
                        $refundReason,
                        $balanceTransaction
                    );

                    if ($refundTransaction) {
                        EjLogger::payment()->info('Successfully created refund transaction #' . $refundTransaction->trans_id . ' for refund ID: ' . $refundId);
                        $createdRefunds++;
                    } else {
                        EjLogger::payment()->warning('Failed to create refund transaction for refund ID: ' . $refundId);
                    }
                }

                EjLogger::payment()->info('Created ' . $createdRefunds . ' refund transactions out of ' . count($refundData) . ' refunds');
            } else {
                // If no specific refunds found, create a single transaction for the total refunded amount
                EjLogger::payment()->info('No specific refunds found, creating a single refund transaction for total amount: ' . $amountRefunded);

                // Generate a unique refund ID
                $refundId = 'refund_' . uniqid();

                // Always create a refund transaction record
                $refundTransaction = $this->createRefundTransaction($transaction, $chargeId, $amountRefunded, [$refundId]);

                // Log the result
                if ($refundTransaction) {
                    EjLogger::payment()->info('Successfully created refund transaction #' . $refundTransaction->trans_id . ' from charge.refunded event');
                } else {
                    EjLogger::payment()->warning('Failed to create refund transaction for charge ID: ' . $chargeId);
                }
            }

            // Update original transaction
            $stripeData = $transaction->stripe ? json_decode($transaction->stripe, true) : [];

            $stripeData['refund'] = [
                'amount_refunded' => $amountRefunded,
                'refunded_at' => now()->toDateTimeString(),
                'refund_status' => 'succeeded',
                'refund_ids' => $refundIds,
            ];

            $transaction->stripe = json_encode($stripeData);
            $transaction->status = 'refunded';
            $transaction->save();

            // Log detailed information about the refund
            EjLogger::payment()->info('Refund processed for transaction: ' . $transaction->id . ', amount: ' . $amountRefunded . ', charge ID: ' . $chargeId);

            return response()->json(['status' => 'success', 'message' => 'Refund processed']);
        } catch (\Exception $e) {
            EjLogger::payment()->error('Error processing refund: ' . $e->getMessage() . '\nStack trace: ' . $e->getTraceAsString());
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Handle refund.updated event from Stripe
     *
     * @param Event $event The Stripe event object
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleRefundUpdated(Event $event)
    {
        try {
            // Safely extract refund data from the event
            $refund = $event->data->object ?? null;

            if (!$refund) {
                EjLogger::payment()->error('Invalid refund update event data: ' . json_encode($event));
                return response()->json(['error' => 'Invalid event data'], 400);
            }

            // Safely access charge ID
            $chargeId = isset($refund->charge) ? $refund->charge : null;
            if (!$chargeId) {
                EjLogger::payment()->error('Refund update event missing charge ID: ' . json_encode($event));
                return response()->json(['error' => 'Missing charge ID'], 400);
            }

            // Try to find the transaction by looking at both trans_id and payment_intent in the stripe JSON
            $transaction = $this->findTransactionByChargeId($chargeId);

            if (!$transaction) {
                EjLogger::payment()->warning('Refund update event: transaction not found for charge_id: ' . $chargeId . '. Attempting to create refund transaction directly.');

                // Try to get charge details from Stripe
                try {
                    $paymentService = app(PaymentService::class);
                    $stripeCharge = $paymentService->getStripeCharge($chargeId);

                    if (isset($stripeCharge->customer)) {
                        $customerId = $stripeCharge->customer;
                        $user = User::where('stripe_id', $customerId)->first();

                        if ($user) {
                            // Safely access refund properties
                            $refundId = isset($refund->id) ? $refund->id : 'refund_' . uniqid();
                            $amount = isset($refund->amount) ? ($refund->amount / 100) : 0; // from cents to dollars
                            $status = isset($refund->status) ? $refund->status : 'unknown';
                            $reason = isset($refund->reason) ? $refund->reason : null;
                            $balanceTransaction = isset($refund->balance_transaction) ? $refund->balance_transaction : null;

                            // Create a refund transaction directly
                            $refundTransaction = $this->createDirectRefundTransaction(
                                $user,
                                $chargeId,
                                $amount,
                                [$refundId],
                                $status,
                                $reason,
                                $balanceTransaction,
                                $failureReason,
                                $failureBalanceTransaction
                            );

                            if ($refundTransaction) {
                                EjLogger::payment()->info('Successfully created direct refund transaction #' . $refundTransaction->trans_id);
                                return response()->json(['status' => 'success', 'message' => 'Direct refund transaction created']);
                            }
                        } else {
                            EjLogger::payment()->error('User not found for Stripe customer ID: ' . $customerId);
                        }
                    } else {
                        EjLogger::payment()->error('Customer ID not found in Stripe charge: ' . $chargeId);
                    }
                } catch (\Exception $e) {
                    EjLogger::payment()->error('Error retrieving charge information: ' . $e->getMessage());
                }

                return response()->json(['error' => 'Transaction not found and could not create direct refund'], 404);
            }

            EjLogger::payment()->info('Found transaction #' . $transaction->id . ' with trans_id: ' . $transaction->trans_id . ' for charge: ' . $chargeId);

            // Safely access refund properties
            $refundId = isset($refund->id) ? $refund->id : 'refund_' . uniqid();
            $amount = isset($refund->amount) ? ($refund->amount / 100) : 0; // from cents to dollars
            $status = isset($refund->status) ? $refund->status : 'unknown';
            $reason = isset($refund->reason) ? $refund->reason : null;
            $balanceTransaction = isset($refund->balance_transaction) ? $refund->balance_transaction : null;
            $failureReason = isset($refund->failure_reason) ? $refund->failure_reason : null;
            $failureBalanceTransaction = isset($refund->failure_balance_transaction) ? $refund->failure_balance_transaction : null;

            // Check for previous attributes to detect status changes
            $previousStatus = null;
            if (isset($event->data->previous_attributes) && isset($event->data->previous_attributes->status)) {
                $previousStatus = $event->data->previous_attributes->status;
                EjLogger::payment()->info('Detected status change for refund ' . $refundId . ': ' . $previousStatus . ' -> ' . $status);
            }

            // Log the refund details
            EjLogger::payment()->info('Processing ' . $event->type . ' event with refund ID: ' . $refundId .
                ', amount: ' . $amount .
                ', status: ' . $status .
                ($failureReason ? ', failure reason: ' . $failureReason : ''));

            // Always create a refund transaction record, regardless of status
            // This ensures we have a record even if the status is 'pending', 'canceled', or other non-succeeded status
            $refundTransaction = $this->createRefundTransaction(
                $transaction,
                $chargeId,
                $amount,
                [$refundId],
                $status,
                $reason,
                $balanceTransaction,
                $failureReason,
                $failureBalanceTransaction
            );

            // Log the result
            if ($refundTransaction) {
                EjLogger::payment()->info('Successfully created/updated refund transaction #' . $refundTransaction->trans_id);
            } else {
                EjLogger::payment()->warning('Failed to create refund transaction for refund ID: ' . $refundId);
            }

            // Check if there are multiple refunds in this event
            if (isset($refund->refunds) && isset($refund->refunds->data) && is_array($refund->refunds->data) && count($refund->refunds->data) > 0) {
                EjLogger::payment()->info('Found additional refunds in refund.updated event: ' . count($refund->refunds->data));

                foreach ($refund->refunds->data as $additionalRefund) {
                    if (!isset($additionalRefund->id) || $additionalRefund->id === $refundId) {
                        continue; // Skip if no refund ID or if it's the same as the main refund
                    }

                    $additionalRefundId = $additionalRefund->id;
                    $additionalAmount = isset($additionalRefund->amount) ? ($additionalRefund->amount / 100) : 0;
                    $additionalStatus = isset($additionalRefund->status) ? $additionalRefund->status : 'unknown';
                    $additionalReason = isset($additionalRefund->reason) ? $additionalRefund->reason : null;
                    $additionalBalanceTransaction = isset($additionalRefund->balance_transaction) ? $additionalRefund->balance_transaction : null;

                    EjLogger::payment()->info('Processing additional refund: ' . $additionalRefundId . ', amount: ' . $additionalAmount);

                    // Create a transaction for this additional refund
                    $additionalRefundTransaction = $this->createRefundTransaction(
                        $transaction,
                        $chargeId,
                        $additionalAmount,
                        [$additionalRefundId],
                        $additionalStatus,
                        $additionalReason,
                        $additionalBalanceTransaction
                    );

                    if ($additionalRefundTransaction) {
                        EjLogger::payment()->info('Successfully created refund transaction #' . $additionalRefundTransaction->trans_id . ' for additional refund ID: ' . $additionalRefundId);
                    } else {
                        EjLogger::payment()->warning('Failed to create refund transaction for additional refund ID: ' . $additionalRefundId);
                    }
                }
            }

            // Update original transaction
            $stripeData = $transaction->stripe ? json_decode($transaction->stripe, true) : [];

            $stripeData['refund'] = [
                'id' => $refundId,
                'amount' => $amount,
                'status' => $status,
                'reason' => $reason,
                'balance_transaction' => $balanceTransaction,
                'updated_at' => now()->toDateTimeString(),
            ];

            $transaction->stripe = json_encode($stripeData);

            if ($status === 'succeeded') {
                $transaction->status = 'refunded';
            }

            $transaction->save();

            // Log detailed information about the refund update
            EjLogger::payment()->info('Refund updated for transaction: ' . $transaction->id .
                ', refund ID: ' . $refundId .
                ', amount: ' . $amount .
                ', status: ' . $status .
                ', charge ID: ' . $chargeId);

            return response()->json(['status' => 'success', 'message' => 'Refund updated']);
        } catch (\Exception $e) {
            EjLogger::payment()->error('Error updating refund: ' . $e->getMessage() . '\nStack trace: ' . $e->getTraceAsString());
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Find a transaction by Stripe charge ID
     * Looks in both trans_id field and payment_intent in the stripe JSON
     *
     * @param string $chargeId The Stripe charge ID to look for
     * @return Transaction|null The found transaction or null
     */
    protected function findTransactionByChargeId(string $chargeId)
    {
        // First try to find by trans_id (direct match)
        $transaction = Transaction::where('trans_id', $chargeId)->first();
        if ($transaction) {
            EjLogger::payment()->info('Found transaction by trans_id: ' . $chargeId);
            return $transaction;
        }

        // If not found, try to find by payment_intent in the stripe JSON
        $transaction = Transaction::whereRaw("JSON_EXTRACT(stripe, '$.payment_intent') = '\"$chargeId\"'")->first();
        if ($transaction) {
            EjLogger::payment()->info('Found transaction by payment_intent: ' . $chargeId);
            return $transaction;
        }

        // If still not found, try to find by invoice ID that might be related to this charge
        $transaction = Transaction::whereRaw("JSON_EXTRACT(stripe, '$.id') LIKE '%$chargeId%'")->first();
        if ($transaction) {
            EjLogger::payment()->info('Found transaction by invoice ID containing charge ID: ' . $chargeId);
            return $transaction;
        }

        // Try to find by subscription ID in the stripe JSON
        $transaction = Transaction::whereRaw("JSON_EXTRACT(stripe, '$.subscription') LIKE '%$chargeId%'")->first();
        if ($transaction) {
            EjLogger::payment()->info('Found transaction by subscription ID containing charge ID: ' . $chargeId);
            return $transaction;
        }

        // If still not found, try to find by any field in the stripe JSON that might contain the charge ID
        $transaction = Transaction::whereRaw("stripe LIKE '%$chargeId%'")->first();
        if ($transaction) {
            EjLogger::payment()->info('Found transaction by searching entire stripe JSON for: ' . $chargeId);
            return $transaction;
        }

        // Try to find by invoice ID in the stripe JSON
        $transaction = Transaction::whereNotNull('stripe')
            ->orderBy('created_at', 'desc')
            ->first();

        if ($transaction) {
            EjLogger::payment()->info('Found most recent transaction with stripe data');
            return $transaction;
        }

        // As a last resort, try to get the customer ID from the PaymentService and find their latest transaction
        try {
            $paymentService = app(PaymentService::class);
            $charge = $paymentService->getStripeCharge($chargeId);

            if (isset($charge->customer)) {
                $customerId = $charge->customer;
                $user = User::where('stripe_id', $customerId)->first();

                if ($user) {
                    $transaction = Transaction::where('user_id', $user->id)
                        ->orderBy('created_at', 'desc')
                        ->first();

                    if ($transaction) {
                        EjLogger::payment()->info('Found most recent transaction for customer: ' . $customerId);
                        return $transaction;
                    }
                }
            }
        } catch (\Exception $e) {
            EjLogger::payment()->error('Error retrieving charge information: ' . $e->getMessage());
        }

        // No transaction found
        return null;
    }

    /**
     * Create a refund transaction directly without an original transaction
     * Used when we can't find the original transaction but still need to record the refund
     *
     * @param User $user The user who is getting the refund
     * @param string $chargeId The Stripe charge ID
     * @param float $amount The refund amount
     * @param array $refundIds Array of refund IDs
     * @param string $status Refund status
     * @param string|null $reason Refund reason
     * @param string|null $balanceTransaction Balance transaction ID
     * @return Transaction|null The newly created refund transaction or null on error
     */
    protected function createDirectRefundTransaction(
        User $user,
        string $chargeId,
        float $amount,
        array $refundIds = [],
        string $status = 'succeeded',
        ?string $reason = null,
        ?string $balanceTransaction = null,
        ?string $failureReason = null,
        ?string $failureBalanceTransaction = null
    ) {
        try {
            // Generate a refund ID if none provided
            $refundId = !empty($refundIds) ? $refundIds[0] : 'refund_' . uniqid();

            // Log the refund ID we're using
            EjLogger::payment()->info('Using refund ID for direct transaction: ' . $refundId);

            // Check if a refund transaction already exists for this specific refund ID with the same status
            $existingRefund = Transaction::where('user_id', $user->id)
                ->where('stripe->id', $refundId)
                ->where('stripe->status', $status) // Only consider it a duplicate if the status is the same
                ->where('type', PaymentType::REFUND)
                ->first();

            if ($existingRefund) {
                EjLogger::payment()->info('Refund transaction already exists for refund ID: ' . $refundId . ' with status: ' . $status);
                return $existingRefund;
            }

            // Log if we found a refund with a different status
            $anyRefund = Transaction::where('user_id', $user->id)
                ->where('stripe->id', $refundId)
                ->where('type', PaymentType::REFUND)
                ->first();

            if ($anyRefund) {
                $oldStatus = isset($anyRefund->stripe['status']) ? $anyRefund->stripe['status'] : 'unknown';
                EjLogger::payment()->info('Found existing refund transaction for ID: ' . $refundId . ' but with different status: ' . $oldStatus . ' -> ' . $status . '. Creating new transaction.');
            }

            // If not found by refund ID, check if there's a refund transaction for this charge without a specific refund ID
            // This is to handle cases where we created a generic refund transaction before
            if (empty($refundIds) || $refundIds[0] === 'refund_' . substr($refundId, 7)) {
                $existingRefund = Transaction::where('user_id', $user->id)
                    ->where('stripe->original_charge_id', $chargeId)
                    ->where('type', PaymentType::REFUND)
                    ->first();

                if ($existingRefund) {
                    EjLogger::payment()->info('Generic refund transaction already exists for charge: ' . $chargeId);
                    return $existingRefund;
                }
            }

            // Get the user's current package
            $package = $user->package;

            if (!$package) {
                EjLogger::payment()->error('Cannot create direct refund transaction: missing package for user ID: ' . $user->id);
                return null;
            }

            $transService = app(TransactionService::class);
            $transId = $transService->generateTransId();

            // Determine the description based on refund status
            $description = 'Refund for charge #' . $chargeId;
            if ($status === 'canceled') {
                $description = 'Canceled refund for charge #' . $chargeId;
                if ($failureReason) {
                    $description .= ' (Reason: ' . $failureReason . ')';
                }
            } else if ($status === 'failed') {
                $description = 'Failed refund for charge #' . $chargeId;
                if ($failureReason) {
                    $description .= ' (Reason: ' . $failureReason . ')';
                }
            } else if ($status === 'pending') {
                $description = 'Pending refund for charge #' . $chargeId;
            }

            // Create a new transaction for the refund
            $refundTransaction = new Transaction([
                'trans_id' => $transId,
                'package_id' => $package->id,
                'payment_method' => PaymentMethod::STRIPE,
                'desc' => $description,
                'type' => PaymentType::REFUND,
                'price' => $amount, // Use positive amount for clarity
                'discount' => 0,
                'total' => $amount,
                'package_validity' => $user->package_validity,
                'stripe' => [
                    'object' => 'refund',
                    'id' => $refundId,
                    'original_charge_id' => $chargeId,
                    'amount' => $amount,
                    'status' => $status,
                    'reason' => $reason,
                    'balance_transaction' => $balanceTransaction,
                    'failure_reason' => $failureReason,
                    'failure_balance_transaction' => $failureBalanceTransaction,
                    'refund_ids' => $refundIds,
                    'created_at' => now()->toDateTimeString(),
                ],
                'status' => $status === 'succeeded', // Only mark as successful if the status is 'succeeded'
            ]);

            $user->transactions()->save($refundTransaction);

            EjLogger::payment()->info('Created direct refund transaction #' . $transId . ' for charge: ' . $chargeId . ', amount: ' . $amount . ', refund ID: ' . $refundId);

            return $refundTransaction;
        } catch (\Exception $e) {
            EjLogger::payment()->error('Error creating direct refund transaction: ' . $e->getMessage() . '\nStack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    protected function createRefundTransaction(
        Transaction $originalTransaction,
        string $chargeId,
        float $amount,
        array $refundIds = [],
        string $status = 'succeeded',
        ?string $reason = null,
        ?string $balanceTransaction = null,
        ?string $failureReason = null,
        ?string $failureBalanceTransaction = null
    ) {
        try {
            // Generate a refund ID if none provided
            $refundId = !empty($refundIds) ? $refundIds[0] : 'refund_' . uniqid();

            // Log the refund ID we're using
            EjLogger::payment()->info('Using refund ID for transaction: ' . $refundId);

            // Check if a refund transaction already exists for this specific refund ID with the same status
            $existingRefund = Transaction::where('user_id', $originalTransaction->user_id)
                ->where('stripe->id', $refundId)
                ->where('stripe->status', $status) // Only consider it a duplicate if the status is the same
                ->where('type', PaymentType::REFUND)
                ->first();

            if ($existingRefund) {
                EjLogger::payment()->info('Refund transaction already exists for refund ID: ' . $refundId . ' with status: ' . $status);
                return $existingRefund;
            }

            // Log if we found a refund with a different status
            $anyRefund = Transaction::where('user_id', $originalTransaction->user_id)
                ->where('stripe->id', $refundId)
                ->where('type', PaymentType::REFUND)
                ->first();

            if ($anyRefund) {
                $oldStatus = isset($anyRefund->stripe['status']) ? $anyRefund->stripe['status'] : 'unknown';
                EjLogger::payment()->info('Found existing refund transaction for ID: ' . $refundId . ' but with different status: ' . $oldStatus . ' -> ' . $status . '. Creating new transaction.');
            }

            // If not found by refund ID, check if there's a refund transaction for this charge without a specific refund ID
            // This is to handle cases where we created a generic refund transaction before
            if (empty($refundIds) || $refundIds[0] === 'refund_' . substr($refundId, 7)) {
                $existingRefund = Transaction::where('user_id', $originalTransaction->user_id)
                    ->where('stripe->original_charge_id', $chargeId)
                    ->where('type', PaymentType::REFUND)
                    ->first();

                if ($existingRefund) {
                    EjLogger::payment()->info('Generic refund transaction already exists for charge: ' . $chargeId);
                    return $existingRefund;
                }
            }

            $user = $originalTransaction->user;
            $package = $originalTransaction->package;

            if (!$user || !$package) {
                EjLogger::payment()->error('Cannot create refund transaction: missing user or package for original transaction ID: ' . $originalTransaction->id);
                return null;
            }

            $transService = app(TransactionService::class);
            $transId = $transService->generateTransId();

            // Determine the description based on refund status
            $description = 'Refund for transaction #' . $originalTransaction->trans_id;
            if ($status === 'canceled') {
                $description = 'Canceled refund for transaction #' . $originalTransaction->trans_id;
                if ($failureReason) {
                    $description .= ' (Reason: ' . $failureReason . ')';
                }
            } else if ($status === 'failed') {
                $description = 'Failed refund for transaction #' . $originalTransaction->trans_id;
                if ($failureReason) {
                    $description .= ' (Reason: ' . $failureReason . ')';
                }
            } else if ($status === 'pending') {
                $description = 'Pending refund for transaction #' . $originalTransaction->trans_id;
            }

            // Create a new transaction for the refund
            $refundTransaction = new Transaction([
                'trans_id' => $transId,
                'package_id' => $package->id,
                'payment_method' => PaymentMethod::STRIPE,
                'desc' => $description,
                'type' => PaymentType::REFUND,
                'price' => $amount, // Use positive amount for clarity
                'discount' => 0,
                'total' => $amount,
                'package_validity' => $originalTransaction->package_validity,
                'stripe' => [
                    'object' => 'refund',
                    'id' => $refundId,
                    'original_charge_id' => $chargeId,
                    'amount' => $amount,
                    'status' => $status,
                    'reason' => $reason,
                    'balance_transaction' => $balanceTransaction,
                    'failure_reason' => $failureReason,
                    'failure_balance_transaction' => $failureBalanceTransaction,
                    'refund_ids' => $refundIds,
                    'created_at' => now()->toDateTimeString(),
                ],
                'status' => $status === 'succeeded', // Only mark as successful if the status is 'succeeded'
            ]);

            $user->transactions()->save($refundTransaction);

            EjLogger::payment()->info('Created refund transaction #' . $transId . ' for charge: ' . $chargeId . ', amount: ' . $amount . ', refund ID: ' . $refundId);

            return $refundTransaction;
        } catch (\Exception $e) {
            EjLogger::payment()->error('Error creating refund transaction: ' . $e->getMessage() . '\nStack trace: ' . $e->getTraceAsString());
            return null;
        }
    }
}
