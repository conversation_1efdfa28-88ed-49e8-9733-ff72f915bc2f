<?php

namespace App\Http\Controllers\Web\App;

use Illuminate\Http\Request;
use App\Models\CompanySetting;
use App\Services\CompanySettingService;
use Illuminate\Support\Facades\Http;
use Log;

class SlackController {
    public function slackWebhook(Request $request)
    {
        if ($request->get('challenge')){
            return response()->json($request->all());
        }

        $eventType = data_get($request, 'event.type');
        try {
            switch ($eventType) {
                case 'app_uninstalled':
                    $this->handleWorkspaceUninstall($request);
                    break;
                default:
                    http_response_code(200);
                    exit();
            }

            http_response_code(200);
        } catch (\Exception $e) {
            Log::error($e);
            http_response_code(400);
        }
    }

    public function handleWorkspaceUninstall($request)
    {
        $workspaceId = $request->get('team_id');
        $companySetting = CompanySetting::with('company')->where('value', $workspaceId)->first();
        $company = $companySetting->company;
        app(CompanySettingService::class)->insertOrUpdateCompanySetting($company, CompanySettingService::SLACK, null);
        CompanySetting::query()->where('key', CompanySettingService::SLACK_WORKSPACE_ID)->where('value', $workspaceId)->delete();
        $company->settings()->where('key', CompanySettingService::SLACK_NOTIFICATIONS)->delete();
        app(CompanySettingService::class)->defaultSlackNotifications($company);
    }

    public function slackInteractiveAction(Request $request)
    {
        Log::info($request->all());
        return response()->json([], 200);
    }
}
