<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckWPVersion
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->header('x-plugin-version') === "2.5.2") {
            return response()->json([
                'status' => 'failed',
                'data'   => [
                    'message' => 'Your plugin version is outdated. Please update to continue.',
                ],
            ], Response::HTTP_FORBIDDEN);
        }

        return $next($request);
    }
}
