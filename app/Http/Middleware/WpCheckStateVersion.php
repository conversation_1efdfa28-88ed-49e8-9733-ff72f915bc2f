<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class WpCheckStateVersion
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {

        // Skip middleware for all POST requests
        if ($request->isMethod('post')) {
            return $next($request);
        }

        $routeName = $request->route()->getName();
        $bypassRoutes = ['company.details', 'company.info', 'subscription.packages', 'job.published'];

        if (in_array($routeName, $bypassRoutes)) {
            return $next($request);
        }

        $appStateVersion = $request->header('x-state-version', 0);
        $userStateVersion = data_get(auth('app')->user(), 'creator.meta.state_version', 0);

        if (!$request->hasHeader('x-state-version')) {
            return $next($request);
        }

        if ($userStateVersion != $appStateVersion) {
            return response()->json([
                'status' => 'failed',
                'data' => [],
                'message' => 'State version mismatch.',
                'status_code' => Response::HTTP_PRECONDITION_FAILED,
            ], Response::HTTP_PRECONDITION_FAILED);
        }

        return $next($request);
    }
}
