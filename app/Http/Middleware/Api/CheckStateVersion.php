<?php

namespace App\Http\Middleware\Api;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CheckStateVersion
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $appStateVersion = $request->header('State-Version', 0);
        $userStateVersion = data_get(auth('api')->user(), 'meta.state_version', 0);

        if ($userStateVersion != $appStateVersion) {
            return response()->json([
                'status' => 'failed',
                'data' => [],
                'message' => 'State version mismatch.'
            ], Response::HTTP_PRECONDITION_FAILED);
        }

        return $next($request);
    }
}
