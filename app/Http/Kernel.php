<?php

namespace App\Http;

use App\Http\Middleware\Api\CheckAdminApiKey;
use App\Http\Middleware\Api\CheckWPKey;
use App\Http\Middleware\Api\CheckStateVersion;
use App\Http\Middleware\Api\CheckZapierKey;
use App\Http\Middleware\Api\ValidateSubscription;
use App\Http\Middleware\ApiPermission;
use App\Http\Middleware\ApplicantExists;
use App\Http\Middleware\AuthAdmin;
use App\Http\Middleware\CheckCompanyAdminDomain;
use App\Http\Middleware\CheckCompanyDomain;
use App\Http\Middleware\CheckCompanySubDomain;
use App\Http\Middleware\CheckSSOLoginURL;
use App\Http\Middleware\CheckWPVersion;
use App\Http\Middleware\CompanyJobs;
use App\Http\Middleware\DynamicThrottle;
use App\Http\Middleware\ExpiredUser;
use App\Http\Middleware\IsAdminTokenValid;
use App\Http\Middleware\JobApplyAuthForCustomDomain;
use App\Http\Middleware\PackagePermission;
use App\Http\Middleware\RemoveCompanyDomain;
use App\Http\Middleware\ResumeParserThrottle;
use App\Http\Middleware\SetAdminDomainCompany;
use App\Http\Middleware\SocialCompanyDomain;
use App\Http\Middleware\UserApiPermission;
use App\Http\Middleware\VerifySameCompany;
use App\Http\Middleware\Permission;
use App\Http\Middleware\WpCheckStateVersion;
use EasyJobs\Evaluation\Http\Middleware\CandidateAuth;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \App\Http\Middleware\CheckForMaintenanceMode::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,

        'set.domain.company' => SetAdminDomainCompany::class,

        'permission' => Permission::class,
        'package.permission' => PackagePermission::class,
        'company.jobs' => CompanyJobs::class,

        'company.subdomain' => CheckCompanySubDomain::class, // For career-page *.easy.jobs domain
        'company.domain' => CheckCompanyDomain::class, // For career-page custom domain
        'company.admin.domain' => CheckCompanyAdminDomain::class, // For Admin Custom app.custom.domain
        'company.domain.remove' => RemoveCompanyDomain::class,
        'social.domain.company' => SocialCompanyDomain::class,

        'wp.auth' => CheckWPKey::class,
        'wp.version' => CheckWPVersion::class,
        'zapier.auth' => CheckZapierKey::class,
        'api.admin.auth' => CheckAdminApiKey::class,
        'api.validate.subscription' => ValidateSubscription::class,
        'api.validate.state-version' => CheckStateVersion::class,
        'wp.api.validate.state-version' => WpCheckStateVersion::class,
        'user.expired' => ExpiredUser::class,
        'auth.admin' => AuthAdmin::class,
        'auth.custom.domain' => JobApplyAuthForCustomDomain::class,
        'sso.validate.login.url' => CheckSSOLoginURL::class,
        'api.application.exits' => ApplicantExists::class,
        'api.verify.same.company' => VerifySameCompany::class,
        'candidate.auth' => CandidateAuth::class,
        'user.api.permission' => UserApiPermission::class,
        'resume.parser.throttle' => ResumeParserThrottle::class,
        'admin.token.valid' => IsAdminTokenValid::class,

        'dynamic_throttle' => DynamicThrottle::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\Language::class,
        ],

        'api' => [
            'dynamic_throttle',
            'bindings',
            ApiPermission::class
        ],

        'wp' => [
            'throttle:300,1',
            'bindings',
        ],

        'zapier' => [
            'throttle:60,1',
            'bindings',
        ]
    ];

    /**
     * The priority-sorted list of middleware.
     *
     * This forces non-global middleware to always be in the given order.
     *
     * @var array
     */
    protected $middlewarePriority = [
        \Illuminate\Session\Middleware\StartSession::class,
        \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        \App\Http\Middleware\Authenticate::class,
        \Illuminate\Session\Middleware\AuthenticateSession::class,
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
        \Illuminate\Auth\Middleware\Authorize::class,
    ];
}
