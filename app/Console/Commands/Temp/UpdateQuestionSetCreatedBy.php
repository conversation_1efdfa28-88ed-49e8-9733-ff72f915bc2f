<?php

namespace App\Console\Commands\Temp;

use App\Models\QuestionSet;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateQuestionSetCreatedBy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'question-set:update-created-by';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update question sets created by.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating started.....');

        QuestionSet::whereNotNull('updated_by')->update(['created_by' => DB::raw('updated_by')]);

        $this->info('Updating completed.');
    }
}
