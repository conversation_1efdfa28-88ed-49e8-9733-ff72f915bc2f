<?php

namespace App\Console\Commands\Temp;

use App\Models\Company;
use App\Models\QuestionSet;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateCompanyTypeFromJson extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update-company-type-from-json';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update question sets created by.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Updating started.....');

        $jsonPath = app_path('Console/Commands/Temp/companies.json');
        $companies = json_decode(file_get_contents($jsonPath), true);

        $types = [
            ['oldTypeId' => 1, 'newTypeId' => null],
            ['oldTypeId' => 2, 'newTypeId' => null],
            ['oldTypeId' => 3, 'newTypeId' => null],
            ['oldTypeId' => 4, 'newTypeId' => null],
            ['oldTypeId' => 5, 'newTypeId' => null],
            ['oldTypeId' => 6, 'newTypeId' => null],
            ['oldTypeId' => 7, 'newTypeId' => null],
            ['oldTypeId' => 8, 'newTypeId' => null],
            ['oldTypeId' => 9, 'newTypeId' => null],
            ['oldTypeId' => 10, 'newTypeId' => null],
            ['oldTypeId' => 11, 'newTypeId' => null],
            ['oldTypeId' => 12, 'newTypeId' => null],
            ['oldTypeId' => 13, 'newTypeId' => null],
            ['oldTypeId' => 14, 'newTypeId' => null],
            ['oldTypeId' => 15, 'newTypeId' => null],
            ['oldTypeId' => 16, 'newTypeId' => null],
            ['oldTypeId' => 17, 'newTypeId' => null],
            ['oldTypeId' => 18, 'newTypeId' => null],
            ['oldTypeId' => 19, 'newTypeId' => null],
            ['oldTypeId' => 20, 'newTypeId' => null],
            ['oldTypeId' => 21, 'newTypeId' => null],
            ['oldTypeId' => 22, 'newTypeId' => null],
            ['oldTypeId' => 23, 'newTypeId' => null],
            ['oldTypeId' => 24, 'newTypeId' => null],
            ['oldTypeId' => 25, 'newTypeId' => null],
            ['oldTypeId' => 26, 'newTypeId' => null],
            ['oldTypeId' => 27, 'newTypeId' => null],
            ['oldTypeId' => 28, 'newTypeId' => null],
            ['oldTypeId' => 29, 'newTypeId' => null],
            ['oldTypeId' => 30, 'newTypeId' => null],
            ['oldTypeId' => 31, 'newTypeId' => null],
            ['oldTypeId' => 32, 'newTypeId' => null],
            ['oldTypeId' => 33, 'newTypeId' => null],
            ['oldTypeId' => 34, 'newTypeId' => null],
            ['oldTypeId' => 35, 'newTypeId' => null],
            ['oldTypeId' => 36, 'newTypeId' => null],
            ['oldTypeId' => 37, 'newTypeId' => null],
            ['oldTypeId' => 38, 'newTypeId' => null],
            ['oldTypeId' => 39, 'newTypeId' => null],
            ['oldTypeId' => 40, 'newTypeId' => null],
            ['oldTypeId' => 41, 'newTypeId' => null],
            ['oldTypeId' => 42, 'newTypeId' => null],
            ['oldTypeId' => 43, 'newTypeId' => null],
            ['oldTypeId' => 44, 'newTypeId' => null],
            ['oldTypeId' => 45, 'newTypeId' => null],
            ['oldTypeId' => 46, 'newTypeId' => null],
            ['oldTypeId' => 47, 'newTypeId' => null],
            ['oldTypeId' => 48, 'newTypeId' => null],
            ['oldTypeId' => 49, 'newTypeId' => null],
            ['oldTypeId' => 50, 'newTypeId' => null],
            ['oldTypeId' => 51, 'newTypeId' => null],
            ['oldTypeId' => 52, 'newTypeId' => null],
            ['oldTypeId' => 53, 'newTypeId' => null],
            ['oldTypeId' => 54, 'newTypeId' => null],
            ['oldTypeId' => 55, 'newTypeId' => null],
            ['oldTypeId' => 56, 'newTypeId' => null],
            ['oldTypeId' => 57, 'newTypeId' => null],
            ['oldTypeId' => 58, 'newTypeId' => null],
            ['oldTypeId' => 59, 'newTypeId' => null],
            ['oldTypeId' => 60, 'newTypeId' => null],
            ['oldTypeId' => 61, 'newTypeId' => null],
            ['oldTypeId' => 62, 'newTypeId' => null],
            ['oldTypeId' => 63, 'newTypeId' => null],
            ['oldTypeId' => 64, 'newTypeId' => null],
            ['oldTypeId' => 65, 'newTypeId' => null],
            ['oldTypeId' => 66, 'newTypeId' => null],
            ['oldTypeId' => 67, 'newTypeId' => null],
            ['oldTypeId' => 68, 'newTypeId' => null],
            ['oldTypeId' => 69, 'newTypeId' => null],
            ['oldTypeId' => 70, 'newTypeId' => null],
            ['oldTypeId' => 71, 'newTypeId' => null],
            ['oldTypeId' => 72, 'newTypeId' => null],
            ['oldTypeId' => 73, 'newTypeId' => null],
            ['oldTypeId' => 74, 'newTypeId' => null],
            ['oldTypeId' => 75, 'newTypeId' => null],
            ['oldTypeId' => 76, 'newTypeId' => null],
            ['oldTypeId' => 77, 'newTypeId' => null],
            ['oldTypeId' => 78, 'newTypeId' => null],
            ['oldTypeId' => 79, 'newTypeId' => null],
            ['oldTypeId' => 80, 'newTypeId' => null],
            ['oldTypeId' => 81, 'newTypeId' => null],
            ['oldTypeId' => 82, 'newTypeId' => null],
            ['oldTypeId' => 83, 'newTypeId' => null],
            ['oldTypeId' => 84, 'newTypeId' => null],
            ['oldTypeId' => 85, 'newTypeId' => null],
            ['oldTypeId' => 86, 'newTypeId' => null],
            ['oldTypeId' => 87, 'newTypeId' => null],
            ['oldTypeId' => 88, 'newTypeId' => null],
            ['oldTypeId' => 89, 'newTypeId' => null],
            ['oldTypeId' => 90, 'newTypeId' => null],
            ['oldTypeId' => 91, 'newTypeId' => null],
            ['oldTypeId' => 92, 'newTypeId' => null],
            ['oldTypeId' => 93, 'newTypeId' => null],
            ['oldTypeId' => 94, 'newTypeId' => null],
            ['oldTypeId' => 95, 'newTypeId' => null],
            ['oldTypeId' => 96, 'newTypeId' => null],
            ['oldTypeId' => 97, 'newTypeId' => null],
            ['oldTypeId' => 98, 'newTypeId' => null],
            ['oldTypeId' => 99, 'newTypeId' => null],
            ['oldTypeId' => 100, 'newTypeId' => null],
        ];

        $newTypeWiseOldTypes = [
            'Banking & Financial Services' => [
                7, 9, 10, 11, 45, 48, 49, 60, 63,
            ],
            'Construction & Real Estate' => [
                42, 75, 90,
            ],
            'Consumer Goods' => [
                12, 21, 22, 23, 64,
            ],
            'Digital Marketing Agencies' => [
                61,
            ],
            'Design & Creative Services' => [
                41, 87, 78,
            ],
            'Education & Training' => [
                44, 57
            ],
            'Energy & Utilities' => [
                13, 18, 25, 20,
            ],
            'Executive Search & Headhunting' => [
                68,
            ],
            'Healthcare & Medical' => [
                16, 26, 27, 34, 38, 65, 77,
            ],
            'Hospitality & Tourism' => [
                14, 28, 43,
            ],
            'HR & Talent Acquisition Services' => [
                62,
            ],
            'Insurance' => [
                31,
            ],
            'Internet & SaaS Platforms' => [
                30,
            ],
            'IT & Technology Services' => [
                58, 74, 79,88, 95, 91,
            ],
            'Legal Services' => [
                32,
            ],
            'Logistics & Transportation' => [
                6, 33, 52,
            ],
            'Manufacturing & Industrial' => [
                1, 2, 3, 5, 15, 47, 51,
            ],
            'Marketing & Advertising' => [
                8, 99, 89,
            ],
            'Nonprofit & Social Impact' => [
                29, 37,
            ],
            'Pharmaceuticals & Biotech' => [
                17, 40, 81, 82, 83, 98,
            ],
            'Publishing & Printing' => [
                36,
            ],
            'Recruitment & Staffing' => [
                67, 80,
            ],
            'Retail & E-commerce' => [
                4, 24, 92,
            ],
            'Software Development' => [
                55,
            ],
            'Sports & Recreation' => [
                19, 46,
            ],
            'Startups & Emerging Technologies' => [
                66, 71, 73, 84, 93,
            ],
            'Telecommunications' => [
                50,
            ],
            'UX/UI Design & Creative Tech' => [
                35, 53, 54, 72, 100,
            ],
            'Wellness, Beauty & Spas' => [
                39, 69, 70, 76,
            ],
            'Others' => [
                56, 59, 85, 86, 94, 96, 97,
            ],
        ];

        // Update $types with corresponding newTypeId based on $newTypeWiseOldTypes
        $newTypeIdIndex = 1; // Start assigning newTypeId from 1
        foreach ($newTypeWiseOldTypes as $category => $oldTypeIds) {
            foreach ($oldTypeIds as $oldTypeId) {
                foreach ($types as &$type) {
                    if ($type['oldTypeId'] === $oldTypeId) {
                        $type['newTypeId'] = $newTypeIdIndex;
                    }
                }
            }
            $newTypeIdIndex++; // Increment newTypeId for the next category
        }


        $myTypes = collect($types)->toArray();

        $newTypes = [];
        foreach ($myTypes as $type) {
            $newTypes[$type['oldTypeId']] = $type['newTypeId'];
        }

        foreach ($companies as $company) {
            $oldCompanyTypeId = $company['company_type_id'] ?? null;

            $newTypeId = $newTypes[$oldCompanyTypeId] ?? null;

            $modelCompany = Company::query()->find($company['id']);
            if (!blank($modelCompany)) {
                $modelCompany->update(['company_type_id' => $newTypeId, 'zoho_sync' => 0]);
                $this->info('Updating ' . $company['id']);
            }

        }


        $this->info('Updating completed.');
    }
}
