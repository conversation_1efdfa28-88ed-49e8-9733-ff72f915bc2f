<?php

namespace App\Console\Commands;

use App\Enums\AllowedFileType;
use App\Models\CustomApplyField;
use Illuminate\Console\Command;
use function Clue\StreamFilter\fun;

class injectAllowedFileTypes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inject:allowed-file-types';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $fileTypes = AllowedFileType::array();
        CustomApplyField::query()->whereNull('meta')->where('type', 'file')->get()->each(function ($item) use ($fileTypes){
            $data['allowed_types'] = array_keys($fileTypes);
            $item->meta = $data;
            $item->save();
        });

        $this->info("File allowed types successfully injected");
        return 0;
    }
}
