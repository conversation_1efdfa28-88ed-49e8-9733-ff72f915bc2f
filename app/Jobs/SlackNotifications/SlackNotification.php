<?php

namespace App\Jobs\SlackNotifications;

use <PERSON><PERSON><PERSON>s\SlackNotificationBuilder\Builder;
use <PERSON><PERSON><PERSON>s\SlackNotificationBuilder\Component\Action;
use EasyJ<PERSON>s\SlackNotificationBuilder\Component\Context;
use Easy<PERSON><PERSON>s\SlackNotificationBuilder\Component\Section;
use EasyJ<PERSON>s\SlackNotificationBuilder\Element\Button;
use EasyJobs\SlackNotificationBuilder\Element\Content;
use EasyJobs\SlackNotificationBuilder\Element\Image;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class SlackNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $company;
    public function __construct($company)
    {
        $this->company = $company;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $blockKit = $this->notificationBlockKit();
        Http::withBody($blockKit, 'application/json')
            ->post($this->company->slack_integration_webhook);
    }

    public function notificationBlockKit()
    {
        $notifyText = "This Integration for Slack is done with EasyJobs.";
        $content = "EasyJobs Advanced Recruiting Solution With Everything You Need To Accelerate The Hiring Process. Remote Hiring or Local - easy.jobs Streamline The Whole Recruitment Process & Make It Easier Than Ever Before.";

        return (new Builder())
            ->setText($notifyText)
            ->addSection(function (Section $section) use ($content) {
                return $section
                    ->setText($content)
                    ->setType("mrkdwn");
            })
            ->toJson();
    }
}
