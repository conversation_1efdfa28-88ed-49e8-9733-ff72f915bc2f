<?php


namespace App\Transformer;

use App\Enums\UserRole;
use App\Models\Company;
use App\Models\CompanyManager;

class UserTransformer
{
    public function serializeLoginData($user, $withToken = true): array
    {
        $props = [
            'first_name', 'last_name', 'name', 'email', 'status', 'dob', 'gender',
            'package_id', 'package_rules', 'profile_image', 'current_company', 'type',
            'package_validity', 'is_subscription_pause', 'last_seen_version', 'subscription_cancel_request',
            'is_enterprise'
        ];

        $data = [];

        foreach ($props as $prop) {
            $data[$prop] = data_get($user, $prop);
        }

        $data['token'] = @$user->accessToken;
        $data['state_version'] = data_get($user, 'meta.state_version', 0);
        $data['email_verified_at'] = (bool)data_get($user, 'email_verified_at');
        $data['total_my_company'] = $user->myCompanies()->count();

        if (!blank(request()->get('domain_company'))) {
            $data['companies'] = $user->companies()->where('companies.id', data_get(request()->get('domain_company'),  'id'))->get()->map(function ($company) use ($user) {
                return $this->serializeUserCompany($company, $user);
            });
        } else {
            $data['companies'] = $user->companies()->where(function ($query){
                $query->whereNull('admin_custom_domain');
                $query->orWhereHas('creator', function ($creator){
                    $creator->whereJsonContains('package_rules->is_enterprise', 0);
                });
            })->get()->map(function ($company) use ($user) {
                return $this->serializeUserCompany($company, $user);
            });
        }

        $data['domain_company'] = $this->transformDomainCompany(request()->get('domain_company'));
        $data['total_my_company'] = $data['companies']->count();

        return $data;
    }

    private function transformDomainCompany($company): array
    {
        return [
            'id' => data_get($company, 'id'),
            'name' => data_get($company, 'name', 'easy.jobs'),
            'admin_logo' => data_get($company, 'admin_logo', '/app-easy-jobs/svg/logo-white.svg'),
            'admin_favicon' => data_get($company, 'admin_favicon', '/favicon.png'),
            'sidebar_color' => data_get($company, 'sidebar_color', '#597dfc')
        ];
    }

    public function serializeStateUserData($user): array
    {
        $props = [
            'id', 'first_name', 'last_name', 'name', 'email', 'status', 'dob', 'gender', 'last_seen_version',
            'email_verified_at', 'package_id', 'package_rules', 'profile_image', 'current_company', 'type', 'package_validity',
            'is_subscription_pause', 'subscription_cancel_request',
        ];

        $data = [];

        foreach ($props as $prop) {
            $data[$prop] = data_get($user, $prop);
        }

        $data['state_version'] = data_get($user, 'meta.state_version', 0);
        $data['companies']     = $user->companies->map(function ($company) use ($user) {
            return $this->serializeUserCompany($company, $user);
        });
        return $data;
    }

    /**
     * Transform Company Info
     * @param $company
     * @param $user
     * @return array
     */
    public function serializeUserCompany($company, $user): array
    {
        $companyData = [];
        $companyData['url'] = companyPageRoute($company);
        $companyData['showAiScore'] = data_get($company, 'ai_score_enabled', false);
        $companyData['sidebar_color'] = data_get($company, 'sidebar_color');
        $companyData['hasBenefits'] = !!data_get($company, 'benefits', null);
        $companyData['canDeleteCompany'] = $company->created_by === $user->id || data_get($company, 'pivot.role') === UserRole::SUPER_ADMIN;
        $companyData['is_subscription_pause'] = data_get($company, 'creator.is_subscription_pause', 0);
        $companyData['subscription_cancel_request'] = data_get($company, 'creator.subscription_cancel_request', false);
        $companyData['companyTypeId'] = data_get($company, 'company_type_id');
        $companyData['enabledCandidateTinyIcon'] = data_get($company, 'enabled_candidate_tiny_icon', false);
        $companyData['is_enterprise_company'] = data_get($company, 'creator.package_rules.is_enterprise', 0) && !blank(data_get($company, 'admin_custom_domain'));

        $props = ['id', 'name', 'username', 'logo', 'is_verified', 'verification_code', 'website', 'companyCoverImage', 'admin_logo', 'admin_custom_domain',
            'meta_title', 'meta_description', 'meta_keywords', 'meta_site_name', 'meta_creator', 'meta_image', 'site_title', 'site_tagline'
        ];

        foreach ($props as $col) {
            $companyData[$col] = data_get($company, $col);
        }

        $companyManager = CompanyManager::with('jobPermissions.jobPost')
            ->where('company_id', $company->id)
            ->where('user_id', $user->id)
            ->first();

        if ($company->created_by == $user->id) {
            $userPermission = array_keys(config('permissions'));
        } else {
            $userPermission = data_get($companyManager, 'permission', []);
        }

        $companyData['permission'] = $this->transformManagerPermission($userPermission);
        $companyData['job_permission'] = $this->transformManagerJobPermission($companyManager);
        $companyData['role'] = $companyManager->role;
        $companyData['package_rule'] = $company?->creator?->package_rules ? $this->transformCreatorPackageRule($company->creator->package_rules) : null;
        $companyData['verify_url'] = getCompanyVerifyUrl($company);
        $companyData['state_version'] = $user->meta['state_version'] ?? 0;
        $companyData['verification'] = [
            'status' => company_verification_status($company),
            'label' => company_verification_label($company)
        ];
        $companyData['logged_in_by_company_owner'] = $company->created_by === $user->id;

        return $companyData;
    }

    private function transformManagerPermission($userPermissions): array
    {
        $userPermissions = $userPermissions ?? [];
        $availablePermissions = array_keys(config('permissions')) ?? [];

        $data = [];
        foreach ($availablePermissions as $val) {
            $data[$val] = in_array($val, $userPermissions);
        }

        return $data;
    }

    /**
     * @param $packagePermissions
     * @return mixed
     */
    private function transformCreatorPackageRule($packagePermissions): mixed
    {
        foreach ($packagePermissions as $key => $value) {
            $packagePermissions[$key] = $value ?? null;
        }

        $packagePermissions['quiz'] = (bool)($packagePermissions['quizzes'] ?? null);

        unset($packagePermissions['id']);

        return $packagePermissions;
    }

    /**
     * @param CompanyManager $companyManager
     * @return array
     */
    private function transformManagerJobPermission(CompanyManager $companyManager): array
    {
        $data = [];
        if ($companyManager->role !== UserRole::JOB_COLLABORATOR) {
            return $data;
        }

        foreach ($companyManager->jobPermissions as $jobPermission) {
            $data[$jobPermission->job_post_id] = [
                'slug' => data_get($jobPermission, 'jobPost.slug'),
                'permissions' => $this->transformManagerPermission(data_get($jobPermission, 'permission'))
            ];
        }

        return $data;
    }
}
