<?php

namespace App\Transformer;

/**
 * Class DepartmentTransformer
 * @package App\Transformer
 */
class DepartmentTransformer
{

    /**
     * Api response transformer for all department listing
     * @param $departments
     * @return mixed
     */
    public function getDepartmentList($departments): mixed
    {
        $departments->transform(function ($department) {
            return $this->getSingleDepartment($department);
        });

        return $departments;
    }

    /**
     * Api response transformer for single department
     * @param $department
     * @return array
     */
    public function getSingleDepartment($department): array
    {
        $data = [];
        $data['id'] = data_get($department, 'id');
        $data['name'] = data_get($department, 'name');
        $data['short_name'] = data_get($department, 'short_name');
        $data['total_use'] = data_get($department, 'employments_count');

        return $data;
    }
}
