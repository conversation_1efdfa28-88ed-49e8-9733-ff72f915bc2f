<?php

namespace App\Services;

use App\Enums\UserPermission;
use Arr;
use Lang;

class AppPermissionService
{

    public function getAppPermissions(): array
    {
        $permissions        = collect(Lang::get('manager-permissions'));
        $groupByPermissions = $permissions->groupBy('group')->toArray();
        $permissions        = [];

        foreach ($groupByPermissions as $key => $groupByPermission) {
            $permissions [] = [
                'name'        => ucfirst($key),
                'permissions' => $this->formatSinglePermission($groupByPermission),
            ];
        }
        return $permissions;
    }

    public function jobPermissions(): array
    {
        $permissions        = collect(Lang::get('manager-permissions'));
        $groupByPermissions = $permissions->whereIn('group', ['jobs', 'candidates'])->groupBy('group')->toArray();
        $permissions        = [];

        $defaultSelectedPermissions = [UserPermission::JOB_VIEW, UserPermission::CANDIDATE_VIEW];

        foreach ($groupByPermissions as $key => $groupByPermission) {
            $permissions [] = [
                'name'        => ucfirst($key),
                'permissions' => $this->formatSingleJobPermission($groupByPermission, $defaultSelectedPermissions),
            ];
        }
        return $permissions;
    }

    /**
     * Collaborator job based permission.
     * @param $groupByPermission
     * @param array $defaultSelectedPermissions
     * @return array
     */
    private function formatSingleJobPermission($groupByPermission, array $defaultSelectedPermissions = []): array
    {
        $gpData = [];
        $collaboratorMessages = Lang::get('collaborator-permissions');

        foreach ($groupByPermission as $permission) {
            $permissionKey = data_get($permission, 'key');
            $message = $collaboratorMessages[$permissionKey] ?? [];
            $defaultSelectedPermission = in_array($permissionKey, $defaultSelectedPermissions);
            $gpData [] = [
                'key'             => $permissionKey,
                'name'            => data_get($message, 'name', ''),
                'hint'            => data_get($message, 'hint', ''),
                'icon'            => data_get($permission, 'icon'),
                'dependencies'    => $defaultSelectedPermission ? [$permissionKey] : data_get($permission, 'dependencies'),
                'dependency_lock' => $defaultSelectedPermission,
                'selected'        => $defaultSelectedPermission
            ];
        }
        return $gpData;
    }

    private function formatSinglePermission($groupByPermission): array
    {
        $defaultSelectedPermissions = [];
        $gpData = [];
        foreach ($groupByPermission as $permission) {
            $permissionKey = data_get($permission, 'key');
            $defaultSelectedPermission = in_array($permissionKey, $defaultSelectedPermissions);
            $gpData [] = [
                'key'             => $permissionKey,
                'name'            => data_get($permission, 'name'),
                'hint'            => data_get($permission, 'hint'),
                'icon'            => data_get($permission, 'icon'),
                'dependencies'    => $defaultSelectedPermission ? [$permissionKey] : data_get($permission, 'dependencies'),
                'dependency_lock' => $defaultSelectedPermission,
                'selected'        => $defaultSelectedPermission
            ];
        }

        return $gpData;
    }

    public function getRoleWisePermissions()
    {
        $allPermissions = array_keys(config('permissions'));
        $roles = config('easyjob.roles');

        foreach ($roles as $key => $val) {
            if ($val === '*') {
                $roles[$key] = $allPermissions;
            }

        }

        return $roles;
    }
}
