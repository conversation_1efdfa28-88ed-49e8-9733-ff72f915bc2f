<?php


namespace App\Services;

use App\Enums\SocialAuthProvider;
use App\Enums\UserType;
use App\Events\UserRegistered;
use App\Mail\UserVerificationEmail;
use App\Models\Company;
use App\Models\DeletedUsers;
use App\Models\JobApplicant;
use App\Models\User;
use App\Notifications\VerificationEmail;
use Cache;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Log;
use Mail;

class AuthService
{
    /**
     * @param  array  $data
     * @return mixed
     */
    private SubscriptionService $subscriptionService;

    public function __construct(SubscriptionService $sService)
    {
        $this->subscriptionService = $sService;
    }


    /**
     * @throws Exception
     */
    public function registration(array $data, $candidateMode = false, $employerMode = false)
    {
        $firstName = data_get($data, 'first_name');
        $lastName = data_get($data, 'last_name');

        $userData = [
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'type' => 'User',
            'status' => 1,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'name' => $firstName." ".$lastName,
        ];

        if ($candidateMode) {
            $userData['type'] = UserType::CANDIDATE;
        }

        if ($employerMode) {
            $userData['type'] = UserType::EMPLOYER;
        }

        try {
            $user = User::create($userData);

            $this->subscriptionService->subscribeToFreePackage($user);

            return $user;
        } catch (Exception $e) {
            Log::error($e);

            throw new Exception($e->getMessage());
        }
    }


    public function updateUser($user, array $data)
    {
        $firstName = data_get($data, 'first_name');
        $lastName = data_get($data, 'last_name');

        $userData = [
            'password' => Hash::make($data['password']),
            'type' => 'User',
            'status' => 1,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'name' => $firstName." ".$lastName,
        ];

        try {

            $user->fill($userData)->save();
            $this->subscriptionService->subscribeToFreePackage($user);
            return $user;

        } catch (\Exception $e) {

            throw new $e;

        }
    }


    public function login(array $data)
    {

        $remember = isset($data['remember_me']);

        if (Auth::attempt(['email' => $data['email'], 'password' => $data['password'], 'status' => 1], $remember)) {
            return [true, 'User logedin!'];
        }

        return [false, 'Login credentials wrong! or you were used social login.'];

    }

    /**
     * @param  array  $data
     * @return array
     */
    public function loginUser(array $data)
    {
        $res = $this->login($data);
        return $res;
    }

    /**
     * @param $providerUser
     * @param $provider
     * @return array
     * @throws Exception
     */
    protected function socialRegistration($providerUser, $provider)
    {

        if (blank($providerUser->email)) {
            return [false, "Can't get email information!"];
        }

        [$firstName, $lastName] = separateName($providerUser->name);
        $user = User::create([
            'first_name' => $firstName,
            'last_name' => $lastName,
            'name' => $providerUser->name,
            'email' => $providerUser->email,
            'provider' => getEnumValue(SocialAuthProvider::class, strtoupper($provider)),
            'provider_user_id' => $providerUser->id,
            'type' => 'User',
            'status' => 1,
            'email_verified_at' => Carbon::now(),
        ]);

        $this->subscriptionService->subscribeToFreePackage($user);

        $verificationLink = route('auth.email.verify', [
            'id' => $user->id,
            'signature' => app(AuthService::class)->generateVerifiedEmailSignature($user)
        ]);

        event(new UserRegistered($user, request()->get('domain_company'), $verificationLink));
        return [$user, "User registered!"];
    }

    /**
     * @param $providerUser
     * @param $provider
     * @param bool $candidateMode
     * @return array
     */
    public function socialLogin($providerUser, $provider, bool $candidateMode = false)
    {
        $providerEmail = $providerUser->getEmail();

        $user = User::query()
            ->where('provider', getEnumValue(SocialAuthProvider::class, strtoupper($provider)))
            ->where('provider_user_id', $providerUser->getId())
            ->first();

        if (blank($user)) {
            $user = User::where('email', $providerEmail)->first();
        }

        if (!$user) {
            //now register the user
            [$user, $message] = $this->socialRegistration($providerUser, $provider);
            if ($candidateMode && $user) {
                if (!blank($user)) {
                    $user->type = UserType::CANDIDATE;
                    $user->save();
                }
            }
        }

        if ($user) {
            return [true, 'User logedin!', $user];
        }

        return [false, $message ?? "Something went wrong!", null];
    }

    /**
     * @param  User  $user
     * @param $data
     */
    public function saveUserInfo(User $user, $data)
    {
        unset($user->accessToken);
        $user->fill($data);
        $user->save();
    }

    /**
     * @param  User  $user
     * @return bool
     */
    public function forceLogin(User $user)
    {
        try {
            \Auth::login($user);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function changePassword(array $data)
    {
        try {
            $user = \auth()->user();
            $user->password = Hash::make($data['password']);
            $user->save();
            return $user;
        } catch (\Exception $e) {
            throw new $e;
        }
    }

    public function deleteAccount()
    {
        \DB::beginTransaction();
        try {
            $user = \auth()->user();

            JobApplicant::where('user_id', $user->id)->delete();
            DeletedUsers::create([
                'email' => $user->email,
                'user_id' => $user->id,
            ]);
            $user->email = "deleteduser".$user->id."@easy.jobs";
            $user->save();
            $user->delete();

            Company::where('created_by', $user->id)->get()->each(function ($company) {
                $cs = app(CompanyService::class);
                $cs->deleteCompanyData($company->id);
            });

            \DB::commit();

            \auth()->logout();
            return true;
        } catch (\Exception $e) {
            \DB::rollBack();
            throw new $e;
        }
    }

    /**
     * Get User from token
     * @param $token
     * @return Model|Builder|null
     */
    public function getUserFromToken($token): Model|Builder|null
    {
        // Define the API endpoint URL
        $apiUrl = config('app.url') . '/api/v2/user/id';

        // Make the API request with token authentication
        $response = Http::withOptions(['verify' => false])
            ->timeout(60)
            ->withToken($token)
            ->get($apiUrl);

        // Extract the user ID from the API response
        $userId = data_get($response, 'data.user_id');

        // If the user ID is blank, return null
        if (blank($userId)) {
            return null;
        }

        // Fetch the user from the database along with their associated companies
        $user = User::with('companies')
            ->where('id', $userId)
            ->firstOrFail();

        // Generate and assign a new access token if one is not provided
        $user->accessToken = $token ?? $user->createToken(User::USER_ACCESS_TOKEN)->accessToken;

        return $user;
    }

    /**
     * Send User To Verification Email
     * @param User $user
     * @return void
     */
    public function sendVerificationEmail(User $user, $company=null, $link=null): void
    {

        $link = $link ?? route('auth.email.verify', [
            'id' => $user->id,
            'signature' => $this->generateVerifiedEmailSignature($user)
        ]);

        $mailEvent = new UserVerificationEmail($company, $link);

        Mail::to($user->email)->send($mailEvent);
    }

    /**
     * Generate verification Email Signature
     * @param $user
     * @return string
     */
    public function generateVerifiedEmailSignature($user): string
    {
        $expiry = Carbon::now()->addDays(7);
        $forgetPasswordKey = 'forget_password_key_'.$user->id;

        $data = $user->only(['id', 'email', 'name']);
        $data['expiry'] = $expiry;
        $serializeData = serialize($data);

        Cache::put($forgetPasswordKey, $serializeData, $expiry->addDays(3));

        return base64_encode(Hash::make($serializeData));
    }

    public function getVerifiedEmailSignatureData($user)
    {
        $forgetPasswordKey = 'forget_password_key_'.$user->id;

        return Cache::get($forgetPasswordKey);
    }

    public function validateTokenForUser(string $token, User $user): bool
    {
        try {
            // Get user from token
            $tokenUser = $this->getUserFromToken($token);

            // Check if token is valid and belongs to the correct user
            if (blank($tokenUser) || $tokenUser->id !== $user->id) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Token validation error: ' . $e->getMessage());
            return false;
        }
    }

    public function expireToken(string $token): bool
    {
        try {
            $user = $this->getUserFromToken($token);

            if (!$user) {
                return false;
            }

            // Find the token in the database and revoke it
            $tokenId = \DB::table('oauth_access_tokens')
                ->where('user_id', $user->id)
                ->where('revoked', 0)
                ->orderBy('created_at', 'desc')
                ->value('id');

            if ($tokenId) {
                \DB::table('oauth_access_tokens')
                    ->where('id', $tokenId)
                    ->update(['revoked' => true]);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Token expiration error: ' . $e->getMessage());
            return false;
        }
    }
}
