<?php

namespace App\Services\EvaluationQuestion;

abstract class BaseService
{
    const SORT_BY_NAME = 'name';
    const SORT_BY_TYPE = 'type';
    const SORT_BY_CREATED = 'created';
    const SORT_BY_UPDATED = 'updated';
    const SORT_BY_QUESTION = 'question';


    function questionGroupSort($query, $sortBy, $orderBy)
    {
        $orderBy = strtolower($orderBy);
        $orderBy = in_array($orderBy, ['asc', 'desc']) ? $orderBy : 'desc';

        switch ($sortBy) {
            case self::SORT_BY_NAME:
                $query->orderBy('name', $orderBy);
                break;
            case self::SORT_BY_TYPE:
                $query->orderBy('exam_type', $orderBy);
                break;
            case self::SORT_BY_CREATED:
                $query->orderBy('created_at', $orderBy);
                break;
            case self::SORT_BY_UPDATED:
                $query->orderBy('updated_at', $orderBy);
                break;
            case self::SORT_BY_QUESTION:
                $query->orderBy('questions_count', $orderBy);
                break;
            default;
                $query->orderBy('created_at', $orderBy);
        }
        return $query;
    }
}
