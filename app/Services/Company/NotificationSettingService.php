<?php

namespace App\Services\Company;

use App\Enums\NotificationTypeValue;
use App\Models\CompanyManager;
use App\Services\CompanySettingService;
use App\Transformer\NotificationSettingTransformer;
use Exception;
use Illuminate\Support\Arr;
use Lang;

class NotificationSettingService
{
    /**
     * @throws \Exception
     */
    public function getCompanyManger($company, $user)
    {
        $manager = CompanyManager::where('company_id', $company->id)->where('user_id', $user->id)->first();
        if(blank($manager)) {
            throw new \Exception("Something went wrong.");
        }
        return $manager;
    }

    public function getManagerNotificationSetting($company, $user): array
    {
        $companyManager = $this->getCompanyManger($company, $user);
        return app(NotificationSettingTransformer::class)->transformSingleUserNotification(data_get($companyManager, 'notifications'), data_get($companyManager, 'permission'));
    }

    public function getCompanySlackNotificationSetting($company)
    {
        return app(NotificationSettingTransformer::class)->transformSlackNotification($company);
    }

    public function saveManagerNotificationSetting($company, $user, $data): array
    {
        $companyManager   = $this->getCompanyManger($company, $user);
        $permissionBasedNotification = app(NotificationSettingService::class)->notificationSettingBasedOnPermission($companyManager->permission);

        $notificationData     = [];
        $notifications        = array_column(\Lang::get('notifications'), 'key');
        foreach ($notifications as $key) {
            $notificationValue = [];
            if (in_array($key, array_keys($data)) && in_array($key, $permissionBasedNotification)) {
                $dataValue = data_get($data, $key);
                if (data_get($dataValue, NotificationTypeValue::EMAIL)) {
                    $notificationValue[] = NotificationTypeValue::EMAIL;
                }

                if (data_get($dataValue, NotificationTypeValue::PUSH)) {
                    $notificationValue[] = NotificationTypeValue::PUSH;
                }
            }

            $notificationData[$key] = $notificationValue;
        }

//        $notificationData = $this->prepareNotificationData($data, data_get($companyManager, 'permission'));
        $companyManager->fill(['notifications' => $notificationData])->save();
        return app(NotificationSettingTransformer::class)->transformSingleUserNotification(data_get($companyManager, 'notifications'), data_get($companyManager, 'permission'));
    }

    /**
     * @throws Exception
     */
    public function saveSlackNotificationSetting($company, $notifications): void
    {
        $company->settings()->where('key', CompanySettingService::SLACK_NOTIFICATIONS)->update([
                "company_id" => $company->id,
                "key" => CompanySettingService::SLACK_NOTIFICATIONS,
                "value" => json_encode($notifications),
                "user_id" => $company->created_by,
            ]
        );
    }

    public function prepareNotificationData($data, $permissions): array
    {
        $currentNotifications = app(NotificationSettingTransformer::class)->transformSingleUserNotification($data, $permissions);
        $notificationData     = [];
        $notifications        = array_column(\Lang::get('notifications'), 'key');
        foreach ($notifications as $key) {
            $notificationValue = [];
            if (in_array($key, array_keys($currentNotifications)) && $dataValue = data_get($currentNotifications, $key)) {
                if (data_get($dataValue, NotificationTypeValue::EMAIL)) {
                    $notificationValue[] = NotificationTypeValue::EMAIL;
                }
                if (data_get($dataValue, NotificationTypeValue::PUSH)) {
                    $notificationValue[] = NotificationTypeValue::PUSH;
                }
            }

            $notificationData[$key] = $notificationValue;
        }
        return $notificationData;
    }

    public function managerDefaultNotification($permissions): array
    {
        $permissionBasedNotification = app(NotificationSettingService::class)->notificationSettingBasedOnPermission($permissions);
        $notificationData            = [];
        $notifications               = array_column(\Lang::get('notifications'), 'key');
        foreach ($notifications as $key) {
            $notificationValue = [];
            if (in_array($key, $permissionBasedNotification)) {
                $notificationValue[] = NotificationTypeValue::EMAIL;
                $notificationValue[] = NotificationTypeValue::PUSH;
            }

            $notificationData[$key] = $notificationValue;
        }

        return $notificationData;
    }


    public function notificationSettingBasedOnPermission(array $managerPermissions): array
    {
        $permissions = collect(Lang::get('manager-permissions'))->whereIn('key', $managerPermissions)->toArray();
        return array_unique(Arr::flatten(array_column($permissions, 'notifications')));
    }


    public function jobRelatedNotifications(): array
    {
        $permissions = collect(\Lang::get('manager-permissions'))->whereIn('group', ['jobs', 'candidates'])->pluck('key')->toArray();
        $permissionBasedNotification = app(NotificationSettingService::class)->notificationSettingBasedOnPermission($permissions);
        $notificationData            = [];
        $notifications               = array_column(\Lang::get('notifications'), 'key');
        foreach ($notifications as $key) {
            $notificationValue = [];
            if (in_array($key, $permissionBasedNotification)) {
                $notificationValue[] = NotificationTypeValue::EMAIL;
                $notificationValue[] = NotificationTypeValue::PUSH;
                $notificationData[$key] = $notificationValue;
            }

        }

        return $notificationData;
    }


}
