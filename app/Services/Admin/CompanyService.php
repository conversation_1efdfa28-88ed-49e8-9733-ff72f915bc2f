<?php

namespace App\Services\Admin;

use App\Models\Company;
use App\Models\CompanyManager;
use App\Models\EnterpriseUser;
use App\Models\JobApplicant;
use App\Models\Package;
use App\Services\CompanySettingService;

class CompanyService extends BaseService
{
    private CompanySettingService $companySettingService;

    public function __construct(CompanySettingService $companySettingService)
    {
        $this->companySettingService = $companySettingService;
    }

    /**
     * @param Company $company
     * @return void
     */
    public function manageEnterpriseCompany(Company $company): void
    {
        $this->manageEnterpriseUser($company);

        // Company Manager State Version Update
        updateUserStateVersion($company->creator);

        $company->managers()->get()->each(function ($manager) {
            updateUserStateVersion($manager);
        });
    }


    /**
     * Add/Update/Delete Enterprise User
     * @param $company
     * @return void
     */
    private function manageEnterpriseUser($company): void
    {
        $isCompanyEnterprise = (bool)$company->is_enterprise;

        if ($isCompanyEnterprise) {
            JobApplicant::query()
                ->has('user')
                ->with('user')
                ->where('company_id', $company->id)
                ->get()
                ->each(function ($applicant) {
                    $this->addEnterpriseUser($applicant->company_id, $applicant->user_id, $applicant->user->password);
                });

            CompanyManager::query()
                ->has('user')
                ->with('user')
                ->where('company_id', $company->id)
                ->get()
                ->each(function ($manager) {
                    $this->addEnterpriseUser($manager->company_id, $manager->user_id, $manager->user->password);
                });

            $this->companySettingService->updateLoginOptions($company->creator, $company, true, 'email');
        } else {
            EnterpriseUser::query()->where('company_id', $company->id)->delete();
        }

        $company->creator()->update(['is_enterprise' => $isCompanyEnterprise]);

        $this->changeEnterpriseUserPackage($company->creator, $isCompanyEnterprise);

    }

    /**
     * Set Package Rule for enterprise User
     * @param $user
     * @param bool $isEnterPrise
     * @return void
     */
    private function changeEnterpriseUserPackage($user, bool $isEnterPrise = false): void
    {
        if ($isEnterPrise) {
            $plan = Package::query()->find(config('easyjob.enterprise_package_id'));
        } else {
            $plan = Package::query()->find(config('easyjob.free_package_id'));
        }

        if (!blank($plan) && !blank($user)) {
            $user->package_id = $plan->id;
            $user->package_validity = now()->addYear();
            $user->package_rules = [
                "id" => $plan->id,
                "jobs" => $plan->jobs,
                "name" => $plan->name,
                "plan" => $plan->plan,
                "type" => $plan->type,
                "price" => $plan->price,
                "quizzes" => $plan->quizzes,
                "managers" => $plan->managers,
                "analytics" => $plan->analytics,
                "companies" => $plan->companies,
                "messaging" => $plan->messaging,
                "screening" => $plan->screening,
                "easyjobs_ai" => $plan->easyjobs_ai,
                "applications" => $plan->applications,
                "job_pipeline" => $plan->job_pipeline,
                "custom_domain" => $plan->custom_domain,
                "discounted_price" => $plan->discounted_price,
                "remote_interview" => $plan->remote_interview
            ];

            $user->save();
        }
    }

    /**
     * Add Enterprise User
     * @param $companyId
     * @param $userId
     * @param $password
     * @return void
     */
    private function addEnterpriseUser($companyId, $userId, $password): void
    {
        EnterpriseUser::query()->updateOrCreate(
            ['company_id' => $companyId, 'user_id' => $userId],
            ['password' => $password],
        );
    }
}