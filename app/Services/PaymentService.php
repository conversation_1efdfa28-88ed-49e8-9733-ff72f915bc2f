<?php


namespace App\Services;


use App\Enums\BillingCycle;
use App\Enums\DiscountType;
use App\Enums\PackagePlan;
use App\Enums\PaymentStatus;
use App\Enums\StripeCouponDuration;
use App\Models\Invoice;
use App\Models\Package;
use App\Models\Transaction;
use App\Models\User;
use App\Services\Paypal\PaypalService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
use Stripe\Charge;
use Stripe\Checkout\Session;
use Stripe\Coupon;
use Stripe\Customer;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\Plan;
use Stripe\Price;
use Stripe\Product;
use Stripe\Refund;
use Stripe\Source;
use Stripe\Stripe;
use Stripe\Subscription;
use Stripe\WebhookEndpoint;

class PaymentService
{
    private $paypalClient;
    public function __construct()
    {
        $this->paypalClient =  new PayPalClient;
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    public function saveCardToStripe(Request $request, $user)
    {
        $cardType = $request->get('cardType');
        if ($user->stripe_id) {
            $paymentMethod = new PaymentMethod();
            $methods       = $paymentMethod->all(['customer' => $user->stripe_id, 'type' => 'card']);
            if (count($methods) > 0) {
                foreach ($methods as $method) {
                    $method->detach();
                }
            }

            // update customer to stripe
            $customer = Customer::update(
                $user->stripe_id,
                [
                    'source' => $request->stripeToken,
                    'email'  => $user->email,
                    'name'   => $user->name,
                    ['invoice_settings' => [
                        'default_payment_method' => '',
                    ],
                    ],
                ]);
        } else {
            // create new customer to stripe
            $customer = Customer::create([
                'source' => 'tok_' . strtolower($cardType),
                'email'  => $user->email,
                'name'   => $user->name,
            ]);
        }

        // retrieve card details
        $card = Customer::retrieveSource($customer->id, $customer->default_source);

        // saving data to user table
        $user->stripe_id        = $customer->id;
        $user->card_brand       = $card->brand;
        $user->card_last_four   = $card->last4;
        $user->card_holder_name = $request->card_name;
        $user->save();
    }

    public function createOrUpdateStripeCustomer($request, $source = null, $guard = '')
    {
        $user = auth($guard)->user();

        $data = [
            'email' => $user->email,
            'name'  => $user->name,
        ];

        if ($source || $request->get('stripeToken')) {
            $data['source'] = $source ? $source->id : $request->get('stripeToken');
        }

        if ($user->stripe_id) {
            // update customer to stripe
            $customer = Customer::update($user->stripe_id, $data);
        } else {
            // create new customer to stripe
            $customer = Customer::create($data);

            $user->stripe_id = $customer->id;
            $user->save();
        }

        return $customer;
    }

    public function createStripeSubscription($package, $guard = '')
    {
        $user = auth($guard)->user();

        $stripe = data_get($package, 'stripe');

        $data = [
            'customer' => $user->stripe_id,
            'items'    => [
                [
                    'plan' => $stripe->plan,
                ],
            ],
        ];

        if ($package->coupon) {
            $data['coupon'] = $package->coupon->code;
        }

        if ($package->discounted_price > 0) {
            return $subscription = Subscription::create($data);
        } elseif ($package->discounted_price == 0 && $package->coupon) {
            return $subscription = $this->createStripeTrialSubscription($package, $guard);
        }
    }

    public function createStripeTrialSubscription($package, $guard)
    {
        switch ($package->plan) {
            case PackagePlan::DAILY:
                $trialEnd = Carbon::now()->addDay()->timestamp;
                break;
            case PackagePlan::WEEKLY:
                $trialEnd = Carbon::now()->addWeek()->timestamp;
                break;
            case PackagePlan::MONTHLY:
                $trialEnd = Carbon::now()->addMonth()->timestamp;
                break;
            case PackagePlan::LIFETIME:
            case PackagePlan::YEARLY:
                $trialEnd = Carbon::now()->addYear()->timestamp;
                break;
            default :
                $trialEnd = Carbon::now()->addYear()->timestamp;
        }

        $user   = auth($guard)->user();
        $stripe = data_get($package, 'stripe');
        $data   = [
            'customer'  => $user->stripe_id,
            'trial_end' => $trialEnd,
            'items'     => [
                [
                    'plan' => $stripe->plan,
                ],
            ],
        ];

        if ($package->coupon) {
            $data['coupon'] = $package->coupon->code;
        }

        $subscription = Subscription::create($data);

        if ($subscription->status == 'trialing') {
            // Subscribe to the package
            $subscriptionService = app(SubscriptionService::class);
            $subscriptionService->subscribe($user, $package);
        }

        return $subscription;

    }

    public function getStripeCustomer($customerId)
    {
        return Customer::retrieve($customerId);
    }

    public function createOrUpdateInvoice($package)
    {
        $user = auth()->user();

        $invoice = Invoice::where('user_id', $user->id)->where('package_id', $package->id)->whereStatus(PaymentStatus::DUE)->first();

        if (!$invoice) {
            $invoiceNo = $this->generateInvoiceNo();
            $data      = [
                'user_id'      => $user->id,
                'package_id'   => $package->id,
                'invoice_no'   => $invoiceNo,
                'invoice_date' => Carbon::now()->toDateString(),
                'bill_date'    => Carbon::today()->toDateString(),
                'price'        => $package->price,
                'discount'     => $package->discount,
                'status'       => PaymentStatus::DUE,
            ];

            $invoice = Invoice::create($data);
        }

        return $invoice;
    }

    private function generateInvoiceNo()
    {
        $lastFour = '0001';
        $invoice  = Invoice::latest()->first();
        if ($invoice) {
            $prevLastFour = substr($invoice->invoice_no, -4, 4);
            $lastFour     = sprintf('%04d', $prevLastFour + 1);
        }

        return date('ym') . $lastFour;
    }

    private function generateTransId()
    {
        $lastFour = '0001';
        $trans    = Transaction::latest()->first();
        if ($trans) {
            $prevLastFour = substr($trans->trans_id, -4, 4);
            $lastFour     = sprintf('%04d', $prevLastFour + 1);
        }

        return date('ym') . $lastFour;
    }

    public function getStripeAllPlans()
    {
        return Plan::all();
    }

    public function getStripeProduct($id)
    {
        return Product::retrieve($id);
    }

    public function cancelSubscription($guard = '')
    {
        $user = auth($guard)->user();
        if ($user->stripe_id) {
            if (!blank($allSubscriptions = $this->getCustomerSubscriptions($user->stripe_id))) {
                foreach ($allSubscriptions as $subscription) {
                    $subscription->cancel();
                }
            }
        }

    }

    public function updateSubscriptionToEndOfPeriod($guard = '')
    {
        $user = auth($guard)->user();
        try {
            if ($user->stripe_id) {
                if (!blank($allSubscriptions = $this->getCustomerSubscriptions($user->stripe_id))) {
                    foreach ($allSubscriptions as $subscription) {
                      Subscription::update($subscription->id, ['cancel_at_period_end' => true]);
//                        $subscription->cancel();

                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error($e);
        }
    }

    public function userCancelSubscription($stripeId, $status)
    {
        try {
            if ($stripeId) {
                if (!blank($allSubscriptions = $this->getCustomerSubscriptions($stripeId))) {
                    foreach ($allSubscriptions as $subscription) {
                        if($status == 'now') {
                            $subscription->cancel();
                        } else {
                            Subscription::update($subscription->id, ['cancel_at_period_end' => true]);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error($e);
        }
    }

    public function updateSubscriptionToPausePaymentCollection($guard = '')
    {
        $user = auth($guard)->user();
        try {
            if ($user->stripe_id) {
                if (!blank($allSubscriptions = $this->getCustomerSubscriptions($user->stripe_id))) {
                    foreach ($allSubscriptions as $subscription) {
                        Subscription::update($subscription->id, [
                            'pause_collection' => [
                                'behavior' => 'void',
                            ]]);
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error($e);
        }


    }

    public function updateSubscriptionToResumePaymentCollection($guard = '')
    {
        $user = auth($guard)->user();
        try {
            if ($user->stripe_id) {
                if (!blank($allSubscriptions = $this->getCustomerSubscriptions($user->stripe_id))) {
                    foreach ($allSubscriptions as $subscription) {
                        Subscription::update($subscription->id, ['pause_collection' =>'']);
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error($e);
        }


    }


    public function getCustomerSubscriptions($stripeId)
    {
        $subscriptions = new Subscription();
        return $subscriptions->all(['customer' => $stripeId]);
    }

    public function cancelStripePreviousSubscriptions($user, $subscriptionId = null)
    {
        if ($user->stripe_id) {
            if (!blank($allSubscriptions = $this->getCustomerSubscriptions($user->stripe_id))) {
                foreach ($allSubscriptions as $subscription) {
                    if ($subscriptionId !== $subscription->id) {
                        $subscription->cancel();
                    }
                }
            }
        }

        return true;

    }

    public function createStripePaymentIntent($package, $desc)
    {
        return PaymentIntent::create([
            'amount'               => 100,
            'currency'             => 'usd',
            'payment_method_types' => ['card'],
            'metadata'             => ['package_id' => $package->id],
            //            'setup_future_usage' => 'off_session',
            //            'statement_descriptor' => $desc,
        ]);
    }

    public function getStripePaymentIntent($paymentIntent)
    {
        return PaymentIntent::retrieve($paymentIntent);
    }

    public function getStripePaymentMethod($id)
    {
        return PaymentMethod::retrieve($id);
    }

    public function stripeAttachCustomerToPaymentMethod($pmId)
    {
        $user          = auth()->user();
        $paymentMethod = $this->getStripePaymentMethod($pmId);
        if (!$paymentMethod->customer) {
            return $paymentMethod->attach([
                'customer' => $user->stripe_id,
            ]);
        }
        return $paymentMethod;
    }

    public function refundCardSetupCharge($pi_id)
    {
        Refund::create([
            'payment_intent' => $pi_id,
        ]);
    }

    public function createStripeProductPlan($packageId)
    {
        $package = Package::find($packageId);
        $data    = [
            'currency' => 'usd',
            'interval' => stripePlanDuration($package->plan),
            'product'  => env('STRIPE_PRODUCT_ID'),
            'nickname' => $package->name,
            'amount'   => ($package->price * 100),
        ];

        return Plan::create($data);
    }

    public function createPaypalProductPlan($data)
    {
        $plan = $data['plan'];
        $cycle = BillingCycle::FOREVER;
        $originalPrice = $data['price'];
        $discountedPrice = $originalPrice - $data['discount'];

        $data = [
            "product_id" => env('PRODUCT_ID'),
            "name" => $data['name'],
            "status" => "ACTIVE",
            "billing_cycles" => app(PaypalService::class)->getProductBillingCycle($plan, $cycle, $originalPrice, $discountedPrice),
            "payment_preferences" => [
                "auto_bill_outstanding" => true,
                "setup_fee" => [
                    "value" => 0.0,
                    "currency_code" => "USD"
                ],
                "setup_fee_failure_action" => "CONTINUE",
                "payment_failure_threshold" => 3
            ],
        ];

        $tokenResponse = $this->paypalClient->getAccessToken();
        $this->paypalClient->setAccessToken($tokenResponse);
        return $this->paypalClient->createPlan($data, Str::uuid()->toString());
    }

    public function updatePaypalPlanToDB($response, $package)
    {
        $data = [
            'plan' => data_get($response, 'id'),
            'product' => data_get($response, 'product_id'),
        ];

        $package->paypal = $data;
        $package->save();
    }

    public function deactivePaypalPlan($package)
    {
        $planId = data_get($package, 'paypal.plan');
        $tokenResponse = $this->paypalClient->getAccessToken();
        $this->paypalClient->setAccessToken($tokenResponse);
        $this->paypalClient->deactivatePlan($planId);
    }

    public function cancelPaypalSubscription($subscriptionId)
    {
        $tokenResponse = $this->paypalClient->getAccessToken();
        $this->paypalClient->setAccessToken($tokenResponse);
        $this->paypalClient->cancelSubscription($subscriptionId, config('easyjob.payment.paypal.cancel_reason'));
    }

    public function cancelPreviousSubscription($user)
    {
        $userTransaction = Transaction::where('user_id', $user->id)->first();

        if ($userTransaction && $userTransaction->paypal) {
            $subscriptionId = data_get($userTransaction->paypal, 'resource.id', null);

            if ($subscriptionId && $userTransaction->package->plan !== PackagePlan::LIFETIME){
                app(PaymentService::class)->cancelPaypalSubscription($subscriptionId);
            }
        }
    }

    public function createStripeProductPrice($packageId)
    {
        $package = Package::find($packageId);
        $data    = [
            'nickname'    => $package->name,
            'product'     => env('STRIPE_PRODUCT_ID'),
            'unit_amount' => ($package->price * 100),
            'currency'    => 'usd',
        ];

        return Price::create($data);
    }

    public function deleteStripeAllPlans()
    {
        $plans = $this->getStripeAllPlans();
        foreach ($plans as $plan) {
            $plan->delete();
            echo "{$plan->nickname} deleted";
        }
    }

    public function deleteStripePlan($planId)
    {
        try {
            $plan = Plan::retrieve($planId);
            $plan->delete();
//            echo "{$plan->nickname} deleted";
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    public function updateStripAllPlansToDB()
    {
        $plans = $this->getStripeAllPlans();

        foreach ($plans->data as $plan) {

            $data = [
                'plan'     => $plan->id,
                'product'  => $plan->product,
                'amount'   => $plan->amount,
                'currency' => $plan->currency,
                'interval' => $plan->interval,
            ];

            Package::where('name', $plan->nickname)->update(['stripe' => json_encode($data)]);

            echo "{$plan->nickname} updated in db. \r\n";
        }
    }

    public function getStripeSource($srcId)
    {
        if ($srcId) {
            return Source::retrieve($srcId);
        }
        return false;
    }

    public function createStripeSource($src, $package, $user)
    {
        return Source::create([
            'amount'         => ($package->discounted_price * 100), // convert to cent
            'currency'       => 'usd',
            'type'           => 'three_d_secure',
            'three_d_secure' => [
                'card' => $src,
            ],
            'redirect'       => [
                'return_url' => route('payment.complete'),
            ],
            'metadata'       => [
                'package_id' => $package->id,
                'user_id'    => $user->id,
            ],
        ]);
    }

    public function createPaypalOrderForLifetime($package, $discountedPrice, $user, array $urls = [], $forceFreePackage = false)
    {
        $data = [
            'intent' => 'CAPTURE',
            'purchase_units' => [
                [
                    'reference_id' => $package->id,
                    'amount' => [
                        'currency_code' => 'USD',
                        'value' => $discountedPrice,
                        'breakdown' => [
                            'item_total' => [
                                'currency_code' => 'USD',
                                'value' => $discountedPrice,
                            ]
                        ],
                    ],
                    'custom_id' => $user->id,
                    'items' => [
                        [
                            'name' => $package->name,
                            'quantity' => 1,
                            'unit_amount' => [
                                'currency_code' => 'USD',
                                'value' => $discountedPrice,
                            ],
                            'category' => 'DIGITAL_GOODS',
                        ]
                    ]
                ]
            ],
            'application_context' => [
                'brand_name' => config('easyjob.payment.paypal.application_context_create.brand_name'),
                'locale' => config('easyjob.payment.paypal.application_context_create.locale'),
                'shipping_preference' => config('easyjob.payment.paypal.application_context_create.shipping_preference'),
                'user_action' => 'PAY_NOW',
                'return_url' => data_get($urls, 'success', "https://app.easy.jobs"),
                'cancel_url' => data_get($urls, 'cancel',"https://app.easy.jobs"),
            ],
        ];

        $tokenResponse = $this->paypalClient->getAccessToken();
        $this->paypalClient->setAccessToken($tokenResponse);

        if ($forceFreePackage){
            $subscriptionService = app(SubscriptionService::class);
            $subscriptionService->subscribeToFreePackage($user);
        }

        return $this->paypalClient->createOrder($data);

    }

    public function createPaypalSessionWithSubscription($package, $price, $user, array $urls = [], ?array $plan = null, $forceFreePackage = false)
    {
        $data = [
            'plan_id' => data_get($package, 'paypal.plan'),
            'custom_id' => $user->id,
            //'start_time' => now()->addMinutes(5)->format('Y-m-d\TH:i:sp'),
            'subscriber' => [
                'name' => [
                    'given_name' => $user->first_name,
                    'surname' => $user->last_name,
                ],
                'email_address' => $user->email,
                'shipping_address' => [
                    'name' => [
                        'full_name' => $user->name
                    ],
                    'address' => config('easyjob.payment.paypal.address')
                ],
            ],
            'application_context' => [
                'brand_name' => config('easyjob.payment.paypal.application_context_create.brand_name'),
                'locale' => config('easyjob.payment.paypal.application_context_create.locale'),
                'shipping_preference' => config('easyjob.payment.paypal.application_context_create.shipping_preference'),
                'user_action' => config('easyjob.payment.paypal.application_context_create.user_action'),
                //'payment_method' => config('easyjob.payment.paypal.application_context_create.payment_method'),
                'return_url' => data_get($urls, 'success', "https://app.easy.jobs"),
                'cancel_url' => data_get($urls, 'cancel',"https://app.easy.jobs"),
            ],
        ];

        if ($plan) {
            $data['plan'] = $plan;
        }

        if ($forceFreePackage){
            $subscriptionService = app(SubscriptionService::class);
            $subscriptionService->subscribeToFreePackage($user);
        }

        $tokenResponse = $this->paypalClient->getAccessToken();
        $this->paypalClient->setAccessToken($tokenResponse);
        return $this->paypalClient->createSubscription($data);

    }

    public function createStripeSessionWithSubscription($package, $user, array $urls = [])
    {
        $data = [
            'payment_method_types' => ['card'],
            'success_url'          => data_get($urls, 'success', ''),
            'cancel_url'           => data_get($urls, 'cancel', ''),
            'metadata'             => [
                'plan' => data_get($package, 'slug', ''),
            ],
        ];

        if ($package->plan === PackagePlan::LIFETIME) {
            $data['mode']       = 'payment';
            $data['line_items'] = [
                [
                    'price'       => data_get($package, 'stripe.plan'),
                    'quantity'    => 1,
                    'description' => $package->name,
                ],
            ];

            $customer   = $this->getStripeCustomer($user->stripe_id);
            $couponCode = data_get($customer, 'discount.coupon.id', '');
            if ($couponCode) {
                $data['discounts'] = [[
                                          'coupon' => $couponCode,
                                      ]];
            }
        } else {
            $data['subscription_data'] = [
                'items' => [
                    [
                        'plan' => data_get($package, 'stripe.plan'),
                    ],
                ],
            ];
        }

        if ($user->stripe_id) {
            $data['customer'] = $user->stripe_id;
        }

        return Session::create($data);
    }

    public function getStripeSession($id)
    {
        return Session::retrieve($id);
    }

    public function updateStripeCustomerSource($source)
    {
        $user = auth()->user();
        return Customer::update($user->stripe_id, ['source' => $source]);
    }

    public function updateStripeCustomerCoupon($user, $coupon = null)
    {
        if ($coupon) {
            return Customer::update($user->stripe_id, ['coupon' => $coupon->code]);
        } else {
            $customer = Customer::retrieve($user->stripe_id);
            if ($customer->discount) {
                $customer->deleteDiscount();
            }
            return $customer;
        }
    }

    public function createStripeChargeFromSource($source, $user)
    {
        return Charge::create([
            'amount'   => $source->amount,
            'currency' => $source->currency,
            'customer' => $user->stripe_id,
            'source'   => $source->id,
        ]);
    }

    public function createStripeHook()
    {
        $hooks = WebhookEndpoint::all();

        if ($hooks) {
            foreach ($hooks as $hook) {
                if ($hook->url == route('stripe.hooks')) {
                    echo "Webhook already exists \r\n";
                    return $hook;
                }
            }
        }

        $hook = WebhookEndpoint::create(
            [
                "url"            => route('stripe.hooks'),
                "enabled_events" => [
                    "invoice.payment_failed",
                    "invoice.payment_succeeded",
                ],
            ]
        );

        echo "Webhook created \r\n";
        return $hook;
    }


    public function cancelUserSubscription($userId)
    {
        $user = User::find($userId);

        if ($user->stripe_id) {
            $customer = Customer::retrieve($user->stripe_id);

            if ($customer->subscriptions) {
                foreach ($customer->subscriptions as $subscription) {
                    $subscription->cancel();
                }
            }
        }

    }

    public function createStripeCoupon($data)
    {
        $stripeCouponData = [
            'id'       => data_get($data, 'code'),
            'name'     => data_get($data, 'name'),
            'currency' => 'USD',
        ];

        if ($duration = data_get($data, 'duration', '')) {
            $stripeCouponData['duration'] = $duration;

            if ($duration === StripeCouponDuration::REPEATING) {
                $stripeCouponData['duration_in_months'] = data_get($data, 'duration_in_months', 1);
            }
        }

        if ($qty = data_get($data, 'quantity')) {
            $stripeCouponData['max_redemptions'] = $qty;
        }

        if (data_get($data, 'discount_type') === DiscountType::PERCENT) {
            $stripeCouponData['percent_off'] = data_get($data, 'amount');
        } else {
            $stripeCouponData['amount_off'] = (data_get($data, 'amount') * 100);
        }

        return Coupon::create($stripeCouponData);
    }

    public function deleteStripeCoupon($coupon)
    {
        $id = data_get($coupon, 'meta.stripe.id');
        if ($id) {
            $coupon = Coupon::retrieve($id);
            $coupon->delete();
        }
    }

    public function getStripeCoupon($id)
    {
        return Coupon::retrieve($id);
    }

    public function getStripeSubscription($id)
    {
        return Subscription::retrieve($id);
    }

    public function getStripeInvoice($invoice_no)
    {
        return \Stripe\Invoice::retrieve($invoice_no);
    }

    /**
     * Retrieve a Stripe charge by ID
     *
     * @param string $chargeId The Stripe charge ID
     * @return \Stripe\Charge The Stripe charge object
     */
    public function getStripeCharge($chargeId)
    {
        return \Stripe\Charge::retrieve($chargeId);
    }

    public function cancelSingleSubscription($user, $subsId)
    {
        if ($user->stripe_id) {
            $customer     = Customer::retrieve($user->stripe_id);
            $subscription = $customer->subscriptions->retrieve($subsId);

            if ($subscription) {
                return $subscription->cancel();
            }
        }

        return false;

    }

    public function getSubscriptionDetails(string $subscriptionId)
    {
        $tokenResponse = $this->paypalClient->getAccessToken();
        $this->paypalClient->setAccessToken($tokenResponse);

        return $this->paypalClient->showSubscriptionDetails($subscriptionId);
    }

    public function getCaptureDetails(string $orderId)
    {
        $tokenResponse = $this->paypalClient->getAccessToken();
        $this->paypalClient->setAccessToken($tokenResponse);

        return $this->paypalClient->capturePaymentOrder($orderId);

        //return $this->paypalClient->showOrderDetails($orderId);
    }

}
