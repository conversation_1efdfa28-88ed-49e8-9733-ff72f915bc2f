<?php


namespace App\Services;

use App\Enums\DiscountType;
use App\Enums\PaymentMethod;
use App\Enums\PaymentType;
use App\Models\Coupon;
use App\Models\Package;
use App\Models\Transaction;
use App\Utils\EjLogger;

class TransactionService
{
    public function createTransaction($user, $invoice, $newPackage)
    {
        $oldInvoice = Transaction::byInvoiceId($invoice->id);
        [$paymentType, $desc] = $this->generatePackageDescription($user, $newPackage);

        $stripeCoupon = data_get($invoice, 'discount.coupon', false);
        $couponData = [];
        $price = floatval($newPackage->price);

        if ($stripeCoupon) {
            $coupon = Coupon::where('code', $stripeCoupon->id)->first();

            if ($coupon) {
                if ($coupon->discount_type == DiscountType::PERCENT) {
                    $discount = round($price * $coupon->amount / 100, 2);
                } else {
                    $discount = round($coupon->amount, 2);
                }
                $total = $price - $discount;
                $couponData = $coupon->toArray();
            }
        } else {
            $discount = 0;
            $total = $price;
        }


        if (blank($oldInvoice)) {

            $transId = $this->generateTransId();

            $transData = [
                'trans_id'         => $transId,
                'package_id'       => $newPackage->id,
                'payment_method'   => PaymentMethod::STRIPE,
                'desc'             => $desc,
                'type'             => $paymentType,
                'price'            => $price,
                'discount'         => $discount,
                'total'            => $total,
                'package_validity' => data_get($user, 'package_validity'),
                //                'meta' => base64_encode(serialize($invoice)),
                'stripe'           => [
                    'object'             => data_get($invoice, 'object'),
                    'id'                 => $id = data_get($invoice, 'id'),
                    'hosted_invoice_url' => data_get($invoice, 'hosted_invoice_url'),
                    'invoice_pdf'        => data_get($invoice, 'invoice_pdf'),
                    'receipt_url'        => data_get($invoice, 'receipt_url'),
                    'subscription'       => data_get($invoice, 'subscription'),
                    'payment_intent'     => data_get($invoice, 'payment_intent'),
                    'error_message'     => $this->getTransactionErrorMessage(data_get($invoice, 'payment_intent')),
                ],
                'coupon'           => $couponData,
                'status'           => data_get($invoice, 'paid', false),
            ];

            $trans = $user->transactions()->create($transData);
            EjLogger::payment()->info("{$id} transaction created");
        } else {
            $transData = [
                'price' => $price,
                'discount' => $discount,
                'total' => $total,
                'package_validity' => data_get($user, 'package_validity'),
                'stripe' => [
                    'object' => data_get($invoice, 'object'),
                    'id' => $id = data_get($invoice, 'id'),
                    'hosted_invoice_url' => data_get($invoice, 'hosted_invoice_url'),
                    'invoice_pdf'        => data_get($invoice, 'invoice_pdf'),
                    'receipt_url'        => data_get($invoice, 'receipt_url'),
                    'subscription'       => data_get($invoice, 'subscription'),
                    'payment_intent'     => data_get($invoice, 'payment_intent'),
                    'error_message'     => $this->getTransactionErrorMessage(data_get($invoice, 'payment_intent')),
                ],
                'status' => data_get($invoice, 'paid', false)
            ];

            $oldInvoice->update($transData);
            EjLogger::payment()->info("{$id} transaction updated");
            $transId = data_get($oldInvoice, 'trans_id');
        }

        EjLogger::payment()->info("'{$user->email}' -> {$desc}");

        return [$transId, $paymentType];
    }
    public function createTransactionForPaypal($user, $newPackage, $request)
    {
        try {
            [$paymentType, $desc] = $this->generatePackageDescription($user, $newPackage);

            $price = floatval($newPackage->price);
            $discount = floatval($newPackage->discount);
            $total = floatval($newPackage->discounted_price);
            $couponData = [];

             if ($user->coupon_id) {
                 $coupon = Coupon::where('id', $user->coupon_id)->first();

                 if ($coupon) {
                     if ($coupon->discount_type == DiscountType::PERCENT) {
                         $discount = round($price * $coupon->amount / 100, 2);
                     } else {
                         $discount = round($coupon->amount, 2);
                     }
                     $total = $price - $discount;
                     $couponData = $coupon->toArray();
                 }
             } else {
                 $total = $price;
             }

            $transId = $this->generateTransId();

            $transData = [
                'trans_id'         => $transId,
                'package_id'       => $newPackage->id,
                'payment_method'   => PaymentMethod::PAYPAL,
                'desc'             => $desc,
                'type'             => $paymentType,
                'price'            => $price,
                'discount'         => $discount,
                'total'            => $total,
                'package_validity' => data_get($user, 'package_validity'),
                //                'meta' => base64_encode(serialize($invoice)),
                'coupon'           => $couponData,
                'paypal'           => $request->all(),
                'status'           => $newPackage->status,
            ];

            $user->transactions()->create($transData);

            EjLogger::payment()->info("Paypal transaction created");
            EjLogger::payment()->info("'{$user->email}' -> {$desc}");

            return [$transId, $paymentType];
        } catch (\Exception $e) {
            EjLogger::payment()->info($e->getMessage());
        }
    }

    private function getTransactionErrorMessage($paymentIntentId)
    {
        // Get Payment Intent
        $paymentIntent = app(PaymentService::class)->getStripePaymentIntent($paymentIntentId);

        return data_get($paymentIntent, 'last_payment_error.message', '');
    }

    public function createCheckoutTransaction($user, $checkout, $newPackage, $coupon = null): array
    {
        $oldInvoice = Transaction::byInvoiceId($checkout->id);

        [$paymentType, $desc] = $this->generatePackageDescription($user, $newPackage);

        $price = floatval($newPackage->price);
        $discount = floatval($newPackage->discount);
        $total = floatval($newPackage->discounted_price);
        $couponData = [];

        if ($user->coupon_id) {
            $coupon = Coupon::where('id', $user->coupon_id)->first();

            if ($coupon) {
                if ($coupon->discount_type == DiscountType::PERCENT) {
                    $discount = round($price * $coupon->amount / 100, 2);
                } else {
                    $discount = round($coupon->amount, 2);
                }
                $total = $price - $discount;
                $couponData = $coupon->toArray();
            }
        }


        if (blank($oldInvoice)) {
            $transId = $this->generateTransId();
            $transData = [
                'trans_id' => $transId,
                'package_id' => $newPackage->id,
                'payment_method' => PaymentMethod::STRIPE,
                'desc' => $desc,
                'type' => $paymentType,
                'price' => $price,
                'discount' => $discount,
                'total' => $total,
                'package_validity' => data_get($user, 'package_validity'),
                'stripe' => [
                    'object' => data_get($checkout, 'object'),
                    'id' => $id = data_get($checkout, 'id'),
                    'hosted_invoice_url' => data_get($checkout, 'hosted_invoice_url'),
                    'invoice_pdf' => data_get($checkout, 'invoice_pdf'),
                    'receipt_url' => data_get($checkout, 'receipt_url'),
                    'subscription' => data_get($checkout, 'subscription'),
                    'payment_intent' => data_get($checkout, 'payment_intent'),
                    'error_message' => $this->getTransactionErrorMessage(data_get($checkout, 'payment_intent')),
                ],
                'coupon' => $couponData,
                'status' => data_get($checkout, 'payment_status', false) === 'paid'
            ];

            $user->transactions()->create($transData);
            EjLogger::payment()->info("{$id} transaction created");
        } else {
            $transData = [
                'price' => $price,
                'discount' => $discount,
                'total' => $total,
                'package_validity' => data_get($user, 'package_validity'),
                'stripe' => [
                    'object' => data_get($checkout, 'object'),
                    'id' => $id = data_get($checkout, 'id'),
                    'hosted_invoice_url' => data_get($checkout, 'hosted_invoice_url'),
                    'invoice_pdf'        => data_get($checkout, 'invoice_pdf'),
                    'receipt_url'        => data_get($checkout, 'receipt_url'),
                    'subscription'       => data_get($checkout, 'subscription'),
                    'payment_intent'     => data_get($checkout, 'payment_intent'),
                    'error_message'     => $this->getTransactionErrorMessage(data_get($checkout, 'payment_intent')),
                ],
                'status' => data_get($checkout, 'paid', false)
            ];

            $oldInvoice->update($transData);
            EjLogger::payment()->info("{$id} transaction updated");
            $transId = data_get($oldInvoice, 'trans_id');
        }

        return [$transId, $paymentType];
    }

    public function createFreeTransaction($user)
    {
        $package = Package::find($user->package_id);
        $paymentType = PaymentType::SUBSCRIPTION;
        $desc = "Subscribed to ”{$package->name}” package.";

        $price = floatval($package->price);
        $discount = $package->discount;
        $total = $package->total;

        $transData = [
            'trans_id' => $this->generateTransId(),
            'package_id' => $package->id,
            'payment_method' => PaymentMethod::NONE,
            'desc' => $desc,
            'type' => $paymentType,
            'package_validity' => data_get($user, 'package_validity'),
            'price' => $price,
            'discount' => $discount,
            'total' => $total,
        ];

        return $user->transactions()->create($transData);
    }

    private function generatePackageDescription($user, $newPackage): array
    {

        $lastTrans = Transaction::where('user_id', $user->id)->latest()->first();

        if (blank($lastTrans)) {
            $paymentType = PaymentType::SUBSCRIPTION;
            $desc = "Subscribed to “{$newPackage->name}” package.";
        } elseif ($lastTrans->package_id == $newPackage->id) {
            $paymentType = PaymentType::RENEWAL;
            $desc = "Subscription package “{$newPackage->name}” renewed.";
        } else {
            $paymentType = PaymentType::PACKAGE_UPGRADE;
            $packageName = $lastTrans->package ? $lastTrans->package->name : 'Free';
            $desc = "Subscription package upgrade from “{$packageName}” to “{$newPackage->name}”";
        }
        return [$paymentType, $desc];
    }

    public function createLifetimeFreeTransaction($user, $newPackage, $coupon)
    {
        [$paymentType, $desc] = $this->generatePackageDescription($user, $newPackage);

        $couponData = [];
        $price = floatval($newPackage->price);

        if ($coupon) {
            if ($coupon->discount_type == DiscountType::PERCENT) {
                $discount = round($price * $coupon->amount / 100, 2);
            } else {
                $discount = round($coupon->amount, 2);
            }
            $total = $price - $discount;
            $couponData = $coupon->only(['id', 'name', 'code', 'discount_type', 'amount', 'meta', 'start_date', 'end_date']);
        } else {
            $discount = 0;
            $total = $price;
        }

        $transData = [
            'trans_id' => $this->generateTransId(),
            'package_id' => $newPackage->id,
            'payment_method' => PaymentMethod::NONE,
            'desc' => $desc,
            'type' => $paymentType,
            'price' => $price,
            'discount' => $discount,
            'total' => $total,
            'coupon' => $couponData,
            'status' => true,
        ];

        $user->transactions()->create($transData);
        EjLogger::payment()->info("{$transData['trans_id']} => '{$user->email}' -> {$desc}");
    }

    /**
     * Generate a unique transaction ID
     *
     * @return string The generated transaction ID
     */
    public function generateTransId(): string
    {
        $lastFour = '0001';
        $trans = Transaction::latest()->first();
        if ($trans) {
            $prevLastFour = substr($trans->trans_id, -4, 4);
            $lastFour = sprintf('%04d', $prevLastFour + 1);
        }

        return date('ym') . $lastFour;
    }
}
