<?php


namespace App\Services;


use App\Enums\ManagerStatus;
use App\Enums\NotificationCategory;
use App\Enums\NotificationType;
use App\Enums\UserStatus;
use App\Models\Company;
use App\Models\CompanyManager;
use App\Models\JobPost;
use App\Models\Notification;
use App\Notifications\GeneralActivity;
use App\Models\User;

class NotificationService
{
    const JOB_UPDATE_NOTIFICATION = NotificationType::JOB_UPDATE;

    private $company;
    private $appUrl;

    private $sendableType = null;
    private $sendableId = null;

    public function __construct(Company $company = null)
    {
        $this->company = $company;
        $this->appUrl  = config('app.url');
    }

    public function setCompany(Company $company)
    {
        $this->company = $company;
        return $this;
    }

    private function setSender($sendableType, $sendableId): void
    {
        $this->sendableType = $sendableType;
        $this->sendableId = $sendableId;
    }

    // Notes notification start
    public function sendNewNoteTagNotificationToManager($manager, $note, $loggedInUser)
    {
        $data = [
            'subject' => $this->getNotificationSubject(NotificationType::CANDIDATE_NOTE_TAGGED),
            'message' => $this->getNotificationMessage(NotificationType::CANDIDATE_NOTE_TAGGED),
            'url'     => $this->appUrl . '/applicant/' . $note->applicant->generated_id,
            'category' => NotificationCategory::MENTION_IN_NOTE->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25),
            ],
        ];

        $this->setSender(User::class, data_get($loggedInUser, 'id'));

        $this->sendNotification($manager, $data);
    }


    /**
     * Sent manager invitation link
     * @param $manager
     * @param $key
     * @param $jobPost
     * @return void
     */
    public function sentManagerInvitation($manager, $key, $jobPost): void
    {
        $jobPostId = !blank($jobPost) ? data_get($jobPost, 'id') : null;

        $notificationType = !blank($jobPost) ? NotificationType::SENT_COLLABORATOR_INVITATION : NotificationType::SENT_MANAGER_INVITATION;

        $data = [
            'subject' => $this->getNotificationSubject($notificationType),
            'message' => $this->getNotificationMessage($notificationType),
            'url'     => '#',
            'category' => NotificationCategory::RECEIVED_INVITATION->value,
            'keys'    => [
                'IS_KEY' => $key,
                'IS_INVITE' => true,
                'JOB_POST_ID' => $jobPostId,
                'ROLE_NAME' => trans("constants.user_role.{$manager->role}"),
                'COMPANY_NAME' => data_get($manager, 'company.name', ''),
                'JOB_TITLE' => data_get($jobPost, 'title', '')
            ],
        ];

        $this->setSender(Company::class, data_get($manager, 'company_id'));

        $this->sendNotification($manager, $data);
    }

    public function noteNotificationToManager($applicant, $loggedInUser, $action = '')
    {
        $data = [
            'subject' => $this->getNotificationSubject(NotificationType::CANDIDATE_NOTE),
            'message' => $this->getNotificationMessage(NotificationType::CANDIDATE_NOTE),
            'url'     => $this->appUrl . '/applicant/' . $applicant->generated_id,
            'category' => NotificationCategory::NOTE_FOR_APPLICANT->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25),
                'APPLICANT_NAME'   => data_get($applicant, 'user.name', ''),
                'ACTION'           => $action,
            ],
        ];

        $this->setSender(User::class, data_get($applicant, 'user_id'));

        $this->sendNotificationToManagers(NotificationType::CANDIDATE_PIPELINE_CHANGED, $data, $loggedInUser, $applicant->job_post_id);
    }
    // Notes notification end

    // Candidate attachment start
    public function attachmentNotificationToManager($applicant, $loggedInUser, $action = '')
    {
        $data = [
            'subject' => $this->getNotificationSubject(NotificationType::CANDIDATE_ATTACHMENT),
            'message' => $this->getNotificationMessage(NotificationType::CANDIDATE_ATTACHMENT),
            'url'     => $this->appUrl . '/applicant/' . $applicant->generated_id . '?tab=evaluation-attachment',
            'category' => NotificationCategory::ATTACHMENT_FOR_APPLICANT->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25),
                'APPLICANT_NAME'   => data_get($applicant, 'user.name', ''),
                'ACTION'           => $action,
            ],
        ];

        $this->setSender(User::class, data_get($applicant, 'user_id'));

        $this->sendNotificationToManagers(NotificationType::CANDIDATE_PIPELINE_CHANGED, $data, $loggedInUser, $applicant->job_post_id);
    }

    // Candidate attachment end

    public function jobUpdateNotificationToManager($job, $loggedInUser)
    {
        $type = NotificationType::JOB_UPDATE;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . "/job/{$job->slug}/edit/",
            'category' => NotificationCategory::JOB_UPDATED->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25),
                'JOB_TITLE'        => data_get($job, 'title', ''),
            ],
        ];

        $this->setSender(User::class, data_get($loggedInUser, 'id'));

        $this->sendNotificationToManagers($type, $data, $loggedInUser, $job->id);
    }

    public function newManagerOnboardNotificationToManager($user)
    {
        $data = [
            'subject' => $this->getNotificationSubject(NotificationType::COMPANY_MANAGER_ONBOARD),
            'message' => $this->getNotificationMessage(NotificationType::COMPANY_MANAGER_ONBOARD),
            'category' => NotificationCategory::NEW_MANAGER_JOINED->value,
            'url'     => $this->appUrl . '/company/setting/basic/user',
            'keys'    => [],
        ];

        $this->setSender(User::class, data_get($user, 'id'));

        $this->sendNotificationToManagers(NotificationType::COMPANY_MANAGERS, $data);
    }

    /**
     * @param $jobPostId
     * @return void
     */
    public function newCollaboratorOnboardNotificationToManager($jobPostId, $user): void
    {
        $url = '#';
        $jobPost = JobPost::find($jobPostId);
        $userName = shortUserName(data_get($user, 'name', ''), 25);

        if($jobPostId && !blank($jobPost)) {
            $url = $this->appUrl . '/job/' . $jobPost->slug .'/collaborator';
        }

        if (blank($userName)) {
            $userName = shortUserName(data_get($user, 'email'), 25);
        }

        $data = [
            'subject' => $this->getNotificationSubject(NotificationType::COMPANY_COLLABORATOR_ONBOARD),
            'message' => $this->getNotificationMessage(NotificationType::COMPANY_COLLABORATOR_ONBOARD),
            'url'     => $url,
            'category' => NotificationCategory::NEW_COLLABORATOR_JOINED->value,
            'keys'    => [
                'USER_NAME' => $userName,
                'JOB_TITLE' => data_get($jobPost, 'title', ''),
            ],
        ];

        $this->setSender(User::class, data_get($user, 'id'));

        $this->sendNotificationToManagers(NotificationType::COMPANY_MANAGERS, $data);
    }

    public function companySettingNotificationToManageer($loggedInUser)
    {
        $type = NotificationType::COMPANY_SETTINGS;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . '/company/setting/',
            'category' => NotificationCategory::COMPANY_SETTING_UPDATED->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25)
            ],
        ];

        $this->setSender(User::class, data_get($loggedInUser, 'id'));

        $this->sendNotificationToManagers($type, $data, $loggedInUser);

    }

    public function newManagerNotificationNotificationToManager($loggedInUser)
    {
        $type = NotificationType::COMPANY_MANAGERS;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . '/company/setting/basic/user',
            'category' => NotificationCategory::NEW_MANAGER_JOINED->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25),
            ],
        ];

        $this->sendNotificationToManagers($type, $data, $loggedInUser);
    }

    public function assessmentCompleteNotificationToManager($assessment)
    {
        $this->appUrl = getAppUrl();

        $type = NotificationType::CANDIDATE_ASSESSMENT_COMPLETED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . "/applicant/{$assessment->applicant->generated_id}?tab=evaluation-assessment",
            'category' => NotificationCategory::ASSESSMENT_COMPLETED->value,
            'keys'    => [
                'APPLICANT_JOB_TITLE' => data_get($assessment, 'job.title', ''),
                'APPLICANT_NAME'      => data_get($assessment, 'applicant.user.name', ''),
                'ASSESSMENT_NAME'     => data_get($assessment, 'questionGroup.name', '')
            ],
        ];

        $this->setSender(User::class, data_get($assessment, 'applicant.user.id'));

        $this->sendNotificationToManagers(NotificationType::NEW_CANDIDATE_APPLIED, $data, null, $assessment->job_post_id);
    }

    //New Message notification
    public function newMessageNotificationToManager($applicant, $loggedInUser)
    {
        $type = NotificationType::NEW_MESSAGE_RECEIVED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . '/applicant/' . $applicant->generated_id . '?tab=conversation',
            'category' => NotificationCategory::NEW_MESSAGE->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25)
            ],
        ];

        $this->setSender(User::class, data_get($applicant, 'user_id'));

        $this->sendNotificationToManagers($type, $data, $loggedInUser, $applicant->job_post_id);
    }

    public function newMessageSentNotificationToManager($applicant, $loggedInUser)
    {
        $type = NotificationType::NEW_MESSAGE_SENT;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . '/applicant/' . $applicant->generated_id . '?tab=conversation',
            'category' => NotificationCategory::NEW_MESSAGE->value,
            'keys'    => [
                'APPLICANT_NAME'   => data_get($applicant, 'user.name', ''),
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25)
            ],
        ];

        $this->setSender(User::class, data_get($loggedInUser, 'id'));

        $this->sendNotificationToManagers(NotificationType::NEW_MESSAGE_RECEIVED, $data, $loggedInUser, $applicant->job_post_id);
    }

    //Pipeline change notification
    public function sendPipelineChangedNotificationToManager($applicant, $pipelineName, $notificationType, $loggedInUser = null): void
    {
        $type = NotificationType::CANDIDATE_PIPELINE_CHANGED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . '/applicant/' . $applicant->generated_id,
            'category' => NotificationCategory::PIPELINE_CHANGED->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25),
                'APPLICANT_NAME'   => data_get($applicant, 'user.name', ''),
                'PIPELINE_NAME'    => $pipelineName,
            ],
        ];

        $this->setSender(User::class, data_get($applicant, 'user_id'));

        $this->sendNotificationToManagers($notificationType, $data, $loggedInUser, $applicant->job_post_id);
    }

    // Send from manager to others manager rating add/remove notification

    /**
     * Send Applicant Rating Notification To For Applicant
     * @param $applicant
     * @param $loggedInUser
     * @return void
     */
    public function ratingNotificationToManager($applicant, $loggedInUser): void
    {
        $type = NotificationType::CANDIDATE_RATING_ADDED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . '/applicant/' . $applicant->generated_id,
            'category' => NotificationCategory::RATING_FOR_APPLICANT->value,
            'keys'    => [
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25),
                'APPLICANT_NAME'   => data_get($applicant, 'user.name', ''),
                'RATING_MARK'      => data_get($applicant, 'rating', ''),
            ],
        ];

        $this->setSender(User::class, data_get($applicant, 'user_id'));

        $this->sendNotificationToManagers(NotificationType::CANDIDATE_PIPELINE_CHANGED, $data, $loggedInUser, $applicant->job_post_id);
    }


    //Job application complete notification
    public function newJobApplicationNotificationToManager($applicant)
    {
        $this->appUrl = getAppUrl();

        $type = NotificationType::NEW_CANDIDATE_APPLIED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => $this->appUrl . '/applicant/' . $applicant->generated_id,
            'category' => NotificationCategory::NEW_APPLICATION->value,
            'keys'    => [
                'APPLICANT_JOB_TITLE' => data_get($applicant, 'job.title', ''),
                'APPLICANT_NAME'      => data_get($applicant, 'user.name', '')
            ],
        ];

        $this->setSender(User::class, data_get($applicant, 'user_id'));

        $this->sendNotificationToManagers($type, $data, null,  $applicant->job_post_id);

    }

    public function sendRejectedNotificationToCandidate($applicant): void
    {
        $type = NotificationType::APPLICANT_REJECTED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => jobDetailsRoute($applicant->job),
            'keys'    => [
                'JOB_TITLE'    => data_get($applicant, 'job.title', ''),
                'COMPANY_NAME' => data_get($applicant, 'job.company.name', ''),
            ],
        ];

        $this->setSender(Company::class, data_get($applicant, 'job.company.id'));

        $this->sendNotification($applicant, $data);
    }

    public function sendSelectedNotificationToCandidate($applicant): void
    {
        $type = NotificationType::APPLICANT_SELECTED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => jobDetailsRoute($applicant->job),
            'keys'    => [
                'JOB_TITLE'    => data_get($applicant, 'job.title', ''),
                'COMPANY_NAME' => data_get($applicant, 'job.company.name', ''),
            ],
        ];

        $this->setSender(Company::class, data_get($applicant, 'job.company.id'));

        if (data_get($applicant, 'meta.pipeline_data.selected', false)) {
            $this->sendNotification($applicant, $data);
        }
    }

    public function sendPipelineChangedNotificationToCandidate($applicant, $pipelineName)
    {
        $type = NotificationType::APPLICANT_PIPELINE_CHANGED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => jobDetailsRoute($applicant->job),
            'keys'    => [
                'JOB_TITLE'     => data_get($applicant, 'job.title', ''),
                'PIPELINE_NAME' => $pipelineName,
            ],
        ];

        $this->sendNotification($applicant, $data);
    }


    public function sendJobApplicationCompleteNotificationToCandidate($applicant)
    {
        $type = NotificationType::APPLICANT_JOB_APPLICATION_COMPLETE;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => jobDetailsRoute($applicant->job),
            'keys'    => [
                'COMPANY_NAME' => data_get($applicant, 'company.name', ''),
                'JOB_TITLE'    => data_get($applicant, 'job.title', '')
            ],
        ];

        $this->setSender(Company::class, data_get($applicant, 'company_id'));

        $this->sendNotification($applicant, $data);
    }

    public function newMessageNotificationToCandidate($applicant, $loggedInUser)
    {
        $type = NotificationType::APPLICANT_MESSAGE_RECEIVED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => "/candidate/conversations/" . $applicant->generated_id,
            'keys'    => [
                'COMPANY_NAME'     => data_get($applicant, 'company.name'),
                'LOGGED_USER_NAME' => shortUserName(data_get($loggedInUser, 'name', ''), 25)
            ],
        ];

        $this->setSender(Company::class, data_get($applicant, 'company_id'));

        $this->sendNotification($applicant, $data);
    }

    public function sendPackageUpdateNotificationToUser($user, $package)
    {
        $type = NotificationType::USER_SUBSCRIPTION_UPDATED;
        $data = [
            'subject' => $this->getNotificationSubject($type),
            'message' => $this->getNotificationMessage($type),
            'url'     => appUrl('/my-account'),
            'keys'    => [
                'PACKAGE_NAME' => data_get($package, 'name'),
            ],
        ];

        $this->sendNotification($user, $data);

    }


    private function sendNotificationToManagers($notificationType, $data, $loggedInUser = null, $jobPostId = null)
    {
        $query = CompanyManager::with('user')
            ->where('company_id', $this->company->id)
            ->where('status', ManagerStatus::CONFIRM);

        if ($loggedInUser instanceof User) {
            $query->where('user_id', '!=', $loggedInUser->id);
        }

        $managers = $query->get();

        foreach ($managers as $manager) {
            if (hasPushNotificationPermission($notificationType, $manager, $jobPostId)) {
                $this->sendNotification($manager, $data);
            }
        }
    }

    private function getNotificationMessage($notificationType)
    {
        return $this->getNotificationContent($notificationType, 'message');
    }

    private function getNotificationSubject($notificationType)
    {
        return $this->getNotificationContent($notificationType, 'subject');
    }

    private function getNotificationContent($notificationType, $key)
    {
        return \Lang::get("notification_messages.$notificationType.$key");
    }

    /**
     * Send notification
     * @param $user
     * @param $data
     * @return void
     */
    private function sendNotification($user, $data): void
    {
        $subject = data_get($data, 'subject', '');
        $message = data_get($data, 'message', '');
        $url     = data_get($data, 'url', '#');
        $keys    = data_get($data, 'keys', []);
        $companyId    = data_get($this->company, 'id');
        $notificationCategory = data_get($data, 'category');

        \Notification::send($user, new GeneralActivity($subject, $message, $url, $keys, [
            'sendableType' => $this->sendableType,
            'sendableId' => $this->sendableId,
            'category' => $notificationCategory,
            'companyId' => $companyId,
        ]));
    }
}
