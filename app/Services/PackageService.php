<?php


namespace App\Services;


use App\Enums\DiscountType;
use App\Enums\JobApplyStatus;
use App\Enums\JobApplyStep;
use App\Enums\JobStatus;
use App\Enums\PackagePlan;
use App\Enums\PackageRule;
use App\Models\Company;
use App\Models\CompanyManager;
use App\Models\JobApplicant;
use App\Models\JobPost;
use App\Models\Package;
use App\Models\QuestionAnswer;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

class PackageService
{
    private $js;

    private $mapFn = [
        PackageRule::COMPANIES         => 'hasAvailableCompany',
        PackageRule::ACTIVE_JOBS       => 'hasAvailableJobPost',
        PackageRule::ACTIVE_CANDIDATES => 'hasTeamAccount',
        PackageRule::TEAM_ACCOUNTS     => 'hasMoreTeamAccount',
        PackageRule::QUIZ              => 'isEligibleToTakeQuiz',
        PackageRule::IN_APP_MESSAGING  => 'isEligibleForMessaging',
        PackageRule::DETAILS_ANALYTICS => 'isEligibleToSeeAnalytics',
        PackageRule::CUSTOM_DOMAIN     => 'isEligibleForCustomDomain',
        PackageRule::IS_ENTERPRISE     => 'isEligibleForCompanyCustomDomain',
        PackageRule::SMART_WORKFLOW    => 'isEligibleForManageJobPipeline',
        //        PackageRule::PERSONALITY_TEST => 'isEligibleForPersonalityTest',
        PackageRule::IQ_TEST           => 'isEligibleForIQTest',
        PackageRule::EASYJOBS_AI       => 'hasAiPermission',
    ];

    public function __construct(JobService $jobService)
    {
        $this->js = $jobService;
    }

    public function availableActiveJobs($user)
    {
        $packageRule    = $user->package_rules ?? [];
        $activeJobLimit = data_get($packageRule, PackageRule::ACTIVE_JOBS, 0);

        if (is_null($activeJobLimit)) {
            return 1000;
        }

        $currentActiveJobs = $this->js->getActiveJobCountByUser($user);
        return ($activeJobLimit - $currentActiveJobs);
    }

    public function hasAiPermission($user)
    {
        $packageRule = $user->package_rules ?? [];
        return !!data_get($packageRule, PackageRule::EASYJOBS_AI, false);
    }

    public function availableTeamAccount($user)
    {
        $packageRule        = $user->package_rules ?? [];
        $limit              = data_get($packageRule, PackageRule::TEAM_ACCOUNTS, 0);
        $cs                 = app(CompanyService::class);
        $currentTeamAccount = $cs->getManagersCountByUser($user);

        return ($limit - $currentTeamAccount);
    }

    public function availableTeamAccountByCompany($company)
    {
        $packageRule        = $company->creator->package_rules ?? [];
        $limit              = data_get($packageRule, PackageRule::TEAM_ACCOUNTS, 0);
        $cs                 = app(CompanyService::class);
        $currentTeamAccount = $cs->getManagerCountByCompany($company);
        return ($limit - $currentTeamAccount);
    }

    public function canCreateRemoteInterview($company)
    {
        $packageRule = $company->creator->package_rules ?? [];
        return (bool)data_get($packageRule, PackageRule::REMOTE_INTERVIEW, 0);
    }

    public function availableActiveCandidates($user)
    {
        $packageRule = $user->package_rules ?? [];
        $limit       = data_get($packageRule, PackageRule::ACTIVE_CANDIDATES, 0);

        if (is_null($limit)) {
            return 1000;
        }

        $cs                      = app(CandidateService::class);
        $currentActiveCandidates = $cs->countCandidateForPublishedJobs($user);

        return ($limit - $currentActiveCandidates);
    }

    public function availableCompany(User $user)
    {
        $packageRule  = $user->package_rules ?? [];
        $limit        = data_get($packageRule, PackageRule::COMPANIES, 0);
        $totalCompany = $user->myCompanies()->count();
        return ($limit - $totalCompany);
    }

    public function hasAvailableCompany($user)
    {
        return $this->availableCompany($user) > 0;
    }

    public function hasAvailableJobPost($user)
    {
        return $this->availableActiveJobs($user) > 0;
    }

    public function hasMoreTeamAccount($company)
    {
        return $this->availableTeamAccountByCompany($company) > 0;
    }

    public function hasAvailableCandidate($user)
    {
        return $this->availableActiveCandidates($user) > 0;
    }

    public function isEligibleToTakeQuiz($user)
    {
        return $this->getPackageRuleAsBoolean($user, PackageRule::QUIZ);
    }

    public function isEligibleForMessaging($user)
    {
        return $this->getPackageRuleAsBoolean($user, PackageRule::IN_APP_MESSAGING);
    }

    public function isEligibleToSeeAnalytics($user)
    {
        return $this->getPackageRuleAsBoolean($user, PackageRule::DETAILS_ANALYTICS);
    }

    public function isEligibleForCustomDomain($user)
    {
        return $this->getPackageRuleAsBoolean($user, PackageRule::CUSTOM_DOMAIN);
    }

    public function isEligibleForCompanyCustomDomain($user): bool
    {
        return $this->getPackageRuleAsBoolean($user, PackageRule::IS_ENTERPRISE);
    }

    public function isEligibleForManageJobPipeline($user)
    {
        return $this->getPackageRuleAsBoolean($user, PackageRule::SMART_WORKFLOW);
    }

    public function isEligibleForPersonalityTest($user)
    {
        return $this->getPackageRuleAsBoolean($user, PackageRule::PERSONALITY_TEST);
    }

    public function isEligibleForIQTest($user)
    {
        return $this->getPackageRuleAsBoolean($user, PackageRule::IQ_TEST);
    }

    public function getUserPackagePlan($user)
    {
        return $this->getPackageRuleAsBoolean($user, 'plan');
    }

    public function getUserPackageType($user)
    {
        return $this->getUserPackagePlan($user);
    }

    private function getPackageRuleAsBoolean($user, $rule, $default = 0)
    {
        $packageRule = $user->package_rules ?? [];
        return (bool)data_get($packageRule, $rule, $default);
    }

    public function hasPackagePermission($action, $user)
    {
        if (!array_key_exists($action, $this->mapFn)) {
            throw new Exception(__("This action is not valid"), 900);
        }

        return call_user_func_array([self::class, $this->mapFn[$action]], [$user]);
    }

    public function getAllPackages()
    {
        return Package::latest()->paginate(10);
    }

    public function savePackage($data)
    {

        $this->calculateCouponDiscount($data, $data['price']);
        $data['slug'] = \Str::slug($data['name']);

        $package = Package::create($data);

        $paymentService = app(PaymentService::class);
        if ($package->plan === PackagePlan::LIFETIME) {
            $plan = $paymentService->createStripeProductPrice($package->id);
        } else {
            $plan = $paymentService->createStripeProductPlan($package->id);
        }
        $this->updateStripPlanToDB($package, $plan);

        if (data_get($data, 'plan') !== PackagePlan::LIFETIME){
            $response = $paymentService->createPaypalProductPlan($data);
            $paymentService->updatePaypalPlanToDB($response, $package);
        }

        return $package;
    }

    public function updatePackage($package, $data)
    {
        foreach (getPackageAttributes() as $k => $name) {
            if (!array_key_exists($k, $data)) {
                $data[$k] = 0;
            }
        }

        if ($data['coupon'] != $package->coupon_id) {
            $this->calculateCouponDiscount($data, $package->price);
        }

        $package->update($data);
    }

    private function calculateCouponDiscount(&$data, $price)
    {
        $data['discount'] = 0;
        if ($data['coupon']) {
            $couponService = app(CouponService::class);
            $coupon        = $couponService->getCoupon($data['coupon']);

            if ($coupon) {
                if ($coupon->discount_type == DiscountType::PERCENT) {
                    $data['discount'] = $price * $coupon->amount / 100;
                } else {
                    $data['discount'] = $coupon->amount;
                }
            }
        }

        $data['coupon_id'] = $data['coupon'];
        unset($data['coupon']);
    }


    public function updateStripPlanToDB($package, $plan)
    {
        $data = [
            'plan'     => $plan->id,
            'product'  => $plan->product,
            'amount'   => $plan->amount,
            'currency' => $plan->currency,
            'interval' => $plan->interval,
        ];

        $package->stripe = $data;
        $package->save();
    }

    public function deletePackage($package)
    {
        $planId = data_get($package, 'stripe.plan');

        if ($planId) {
            $paymentService = app(PaymentService::class);
            $paymentService->deleteStripePlan($planId);
        }

        $package->delete();
    }

    public function isPlanUpgradeable($user, $package): array
    {
        $data = [
            'status'  => true,
            'message' => '',
        ];

        $messages = array_values(array_unique($this->isPackageUpgradeable($package, $user)));
        if (!blank($user->package_validity) && Carbon::parse($user->package_validity)->endOfDay()>now() && data_get($user, 'package.id', null) === data_get($package, 'id')) {
            $data['status']  = false;
            $data['message'] = "You're already in this package.";
        } elseif (!count(array_unique(array_values($messages))) == 1) {
            $data['status']  = false;
            $messages []     = "Subscription package downgrade is not possible. Please contact with support.";
            $data['message'] = $messages;
        }


        return $data;
    }


    public function serializePackages($package, $user)
    {
        $data                 = $package->only(['id', 'type', 'name', 'slug', 'plan', 'price', 'discount', 'discounted_price', 'stripe']);
        $data['plan']         = data_get(trans("subscription.package.plan"), $package->plan, '');
        $data['has_discount'] = data_get($package, 'discount', 0) > 0;
        $messages             = array_values($this->isPackageUpgradeable($package, $user));
        $data['messages']     = $messages;
        $data['upgradable']   = count(array_unique($messages)) == 1;
        $data['isDowngrade'] = (float)data_get($user, 'package.price') > (float)data_get($package, 'price');
        $isSelected          = (data_get($user, 'package.id', '') == $package->id);
        $data['is_selected'] = $isSelected;
        $packageValidity      = data_get($user, 'package_validity');
        $data['is_expired']   = $packageValidity && $isSelected && now()->gte($packageValidity);
        $data['attrs']       = [];
        $numberGen           = [PackageRule::COMPANIES, PackageRule::ACTIVE_JOBS, PackageRule::ACTIVE_CANDIDATES, PackageRule::TEAM_ACCOUNTS];
        $attributes          = array_keys(trans('subscription.package.attribute'));
        if ($isSelected) {
            $package                       = $user->package_rules;
            $package[PackageRule::SUPPORT] = true;
        }
        foreach ($attributes as $key) {
            $data['attrs'][] = subscriptionAttrLevel($package, $key, in_array($key, $numberGen));
        }
        return $data;
    }

    private function isPackageUpgradeable($package, $user)
    {
        $messages = [
            PackageRule::ACTIVE_JOBS   => null,
            PackageRule::COMPANIES     => null,
            PackageRule::TEAM_ACCOUNTS => null,
            'same_package'             => null,
        ];

        if (blank($user->package)) {
            return $messages;
        }

        if ($user->package->id === $package->id && !blank($user->package_validity) && Carbon::parse($user->package_validity)->endOfDay()< now()) {
            return $messages;
        }

        if ($user->package->id === $package->id) {
            $messages ['same_package'] = 'Cannot upgrade to same package.';
            return $messages;
        }

        if (blank(data_get($user, 'myCompanies'))) {
            return $messages;
        }

        if ($user->myCompanies->count() > (int)data_get($package, 'companies')) {
            $messages[PackageRule::COMPANIES] = 'You have created more companies then your selected package.';
            return $messages;
        }

        foreach ($user->myCompanies as $company) {

            if (!blank(data_get($package, 'jobs'))) {
                $activeJobCount = $company->jobs()
                    ->where('status', JobStatus::PUBLISHED)
                    ->nonExpired()
                    ->count();

                if ($activeJobCount > (int)data_get($package, 'jobs')) {
                    $messages[PackageRule::ACTIVE_JOBS] = 'You have  more active jobs then your selected package.';
                    return $messages;
                }
            }

            if (!blank(data_get($package, 'managers'))) {
                $companyManagerCount = $company->managers->count();

                if ($companyManagerCount - 1 > (int)data_get($package, 'managers')) {
//                    dd($companyManagerCount, (int)data_get($package, 'managers'));
                    $messages [PackageRule::TEAM_ACCOUNTS] = 'You have more managers then your selected package.';
                    return $messages;
                }
            }


            if (!blank(data_get($package, 'applications'))) {
                // Test the active applicants when logic is added
            }
        }

        return $messages;
    }

    /**
     * @throws \Exception
     */
    public function limitUserAccesses($user)
    {
        $companies = $user->myCompanies;
        foreach ($companies as $company) {
            try {
                $activeJobCount = $company->jobs()
                    ->where('status', JobStatus::PUBLISHED)
                    ->count();

                if ($activeJobCount > 0) {
                    $latestActiveJob = JobPost::where('company_id', $company->id)
                        ->where('status', JobStatus::PUBLISHED)
                        ->orderBy('id', 'DESC')->first();

                    $pendingApplicants = JobApplicant::withTrashed()->where('job_post_id', $latestActiveJob->id)->where('status', JobApplyStatus::PENDING)->get();
                    if (!blank($pendingApplicants)) {
                        $this->updatePendingApplicantsData($pendingApplicants);
                    }
                    $latestActiveJob->update(['screening_id' => null]);

                    JobPost::where('company_id', $company->id)
                        ->where('id','!=', $latestActiveJob->id)
                        ->where('status', JobStatus::PUBLISHED)
                        ->update(['status' => JobStatus::ARCHIVED]);
                }

                CompanyManager::where('company_id', $company->id)->where('user_id', '!=', $user->id)->delete();
                app(CompanySettingService::class)->insertOrUpdateCompanySetting($company, CompanySettingService::REMOVE_POWERED_BY, false);
                app(CompanySettingService::class)->insertOrUpdateCompanySetting($company, CompanySettingService::AI_SCORE_ENABLE, false);
                Company::where('id', $company->id)->update(['custom_domain' => null]);
            }catch (Exception $e) {
                Log::error($e);
            }

        }

    }

    private function updatePendingApplicantsData($pendingApplicants)
    {
        foreach ($pendingApplicants as $applicant) {
            //apply_step_tab
            $currentStep = (int)data_get($applicant->apply_step_tab, 'step');
            if ($currentStep > JobApplyStep::RESUME) {
                $pendingApplicantData['apply_step_tab'] = ['step' => null, 'tab' => JobApplyStep::RESUME];
            }
            $pendingApplicantData['quiz_marks_total']      = null;
            $pendingApplicantData['quiz_marks_obtain']     = null;
            $pendingApplicantData['quiz_started_at']       = null;
            $pendingApplicantData['quiz_ends_at']          = null;
            $pendingApplicantData['quiz_last_question_id'] = null;
            $pendingApplicantData['screening_id']          = null;
            $pendingApplicantData['quiz_id']               = null;
            $applicant->update($pendingApplicantData);

            QuestionAnswer::where('user_id', $applicant->user_id)
                ->where('job_post_id', $applicant->job_post_id)->forceDelete();
        }

    }


}
