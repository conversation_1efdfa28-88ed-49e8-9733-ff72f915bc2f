<?php


namespace App\Services;


use App\Enums\AdminDashboardAnalytics;
use App\Enums\AdminDateFilter;
use App\Enums\AdminIndexType;
use App\Enums\JobApplyStatus;
use App\Enums\JobBoardStatus;
use App\Enums\JobStatus;
use App\Enums\PackagePlan;
use App\Enums\PaymentMethod;
use App\Enums\PaymentType;
use App\Enums\SocialAuthProvider;
use App\Enums\UserStatus;
use App\Enums\UserType;
use App\Models\Company;
use App\Models\CompanySetting;
use App\Models\JobApplicant;
use App\Models\JobPost;
use App\Models\Package;
use App\Models\SourcePlatform;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminReportService
{
    protected $timezone;

    public function __construct()
    {
//        $this->timezone = 'Asia/Dhaka';
        $this->timezone = 'UTC';
    }

    public function countUser($type, $fromDate, $toDate)
    {
        return User::whereType($type)->whereBetween('created_at', [$fromDate, $toDate])->count();
    }

    public function customerCounterInfo($from, $to, $filter, $titlePrefix = '')
    {
        $count = $this->countUser(UserType::EMPLOYER, $from, $to);
        $customerData = [
            'title' => "{$titlePrefix} Customers",
            'count' => $count,
        ];

        [$prevFrom, $prevTo] = $this->getFilterPrevDates($filter);
        $prevCount = $this->countUser(UserType::EMPLOYER, $prevFrom, $prevTo);
        $customerData['previousCount'] = $prevCount;

        [$percent, $indexType] = $this->calculatePercent($count, $prevCount);

        $customerData['percent'] = $percent;
        $customerData['indexType'] = $indexType;
        return $customerData;
    }

    public function companyCounterInfo($from, $to, $filter, $titlePrefix = '')
    {
        $count = Company::whereBetween('created_at', [$from, $to])->count();
        $data = [
            'title' => "{$titlePrefix} Companies",
            'count' => $count,
        ];

        [$prevFrom, $prevTo] = $this->getFilterPrevDates($filter);
        $prevConut = Company::whereBetween('created_at', [$prevFrom, $prevTo])->count();
        $data['previousCount'] = $prevConut;

        [$percent, $indexType] = $this->calculatePercent($count, $prevConut);

        $data['percent'] = $percent;
        $data['indexType'] = $indexType;
        return $data;
    }

    public function jobCounterInfo($from, $to, $filter, $titlePrefix = '')
    {
        $count = JobPost::whereBetween('created_at', [$from, $to])->count();
        $data = [
            'title' => "{$titlePrefix} Jobs",
            'count' => $count,
        ];

        [$prevFrom, $prevTo] = $this->getFilterPrevDates($filter);
        $prevConut = JobPost::whereBetween('created_at', [$prevFrom, $prevTo])->count();
        $data['previousCount'] = $prevConut;

        [$percent, $indexType] = $this->calculatePercent($count, $prevConut);

        $data['percent'] = $percent;
        $data['indexType'] = $indexType;
        return $data;
    }

    public function openJobCounterInfo($from, $to, $filter, $titlePrefix = '')
    {
        $count = JobPost::whereBetween('created_at', [$from, $to])
            ->where('expire_at', '>=', $to)
            ->where('status', JobStatus::PUBLISHED)
            ->count();

        $data = [
            'title' => "{$titlePrefix} Open Jobs",
            'count' => $count,
        ];

        [$prevFrom, $prevTo] = $this->getFilterPrevDates($filter);
        $prevConut = JobPost::whereBetween('created_at', [$prevFrom, $prevTo])
            ->where('status', JobStatus::PUBLISHED)
            ->count();
        $data['previousCount'] = $prevConut;

        [$percent, $indexType] = $this->calculatePercent($count, $prevConut);

        $data['percent'] = $percent;
        $data['indexType'] = $indexType;
        return $data;
    }

    public function candidateCounterInfo($from, $to, $filter, $titlePrefix = '')
    {
        $count = $this->countUser(UserType::CANDIDATE, $from, $to);
        $candidateData = [
            'title' => "{$titlePrefix} Candidates",
            'count' => $count,
        ];

        [$prevFrom, $prevTo] = $this->getFilterPrevDates($filter);
        $prevCount = $this->countUser(UserType::CANDIDATE, $prevFrom, $prevTo);
        $candidateData['previousCount'] = $prevCount;

        [$percent, $indexType] = $this->calculatePercent($count, $prevCount);

        $candidateData['percent'] = $percent;
        $candidateData['indexType'] = $indexType;
        return $candidateData;
    }

    public function candidateHiredCounterInfo($from, $to, $filter)
    {
        $count = JobApplicant::whereStatus(JobApplyStatus::SELECTED)->whereBetween('pipeline_updated_at', [$from, $to])->count();
        $candidateData = [
            'title' => "Successful Hired",
            'count' => $count,
        ];

        [$prevFrom, $prevTo] = $this->getFilterPrevDates($filter);
        $prevCount = JobApplicant::whereStatus(JobApplyStatus::SELECTED)->whereBetween('pipeline_updated_at', [$prevFrom, $prevTo])->count();
        $candidateData['previousCount'] = $prevCount;

        [$percent, $indexType] = $this->calculatePercent($count, $prevCount);

        $candidateData['percent'] = $percent;
        $candidateData['indexType'] = $indexType;
        return $candidateData;
    }

    public function calculatePercent($currentValue, $previousValue)
    {
        if ($currentValue > $previousValue) {
            $indexType = AdminIndexType::INCREASE;
            $increment = $currentValue - $previousValue;
            $percent = ($increment * 100) / (!empty($previousValue) ? $previousValue : 1);
        } elseif ($currentValue < $previousValue) {
            $indexType = AdminIndexType::DECREASE;
            $decrement = $previousValue - $currentValue;
            $percent = ($decrement * 100) / (!empty($previousValue) ? $previousValue : 1);
        } else {
            $indexType = AdminIndexType::NOTHING;
            $percent = 0;
        }

        return [$percent, $indexType];
    }

    public function getFilterPrevDates($filter)
    {
        $prevFilter = $this->getPrevFilter($filter);
        return $this->getFilterDates($prevFilter);
    }

    public function getPrevFilter($filter)
    {
        switch ($filter) {
            case AdminDateFilter::TODAY:
                return AdminDateFilter::YESTERDAY;
            case AdminDateFilter::YESTERDAY:
                return AdminDateFilter::BEFORE_YESTERDAY;
            case AdminDateFilter::THIS_WEEK:
                return AdminDateFilter::LAST_WEEK;
            case AdminDateFilter::SEVEN_DAYS:
                return AdminDateFilter::BEFORE_SEVEN_DAYS;
            case AdminDateFilter::LAST_WEEK:
                return AdminDateFilter::BEFORE_LAST_WEEK;
            case AdminDateFilter::THIS_MONTH:
                return AdminDateFilter::LAST_MONTH;
            case AdminDateFilter::THIRTY_DAYS:
                return AdminDateFilter::BEFORE_THIRTY_DAYS;
            case AdminDateFilter::LAST_MONTH:
                return AdminDateFilter::BEFORE_LAST_MONTH;
            case AdminDateFilter::THIS_YEAR:
                return AdminDateFilter::LAST_YEAR;
            case AdminDateFilter::LAST_YEAR:
                return AdminDateFilter::BEFORE_LAST_YEAR;
            default:
                return null;
        }
    }

    public function getFilterDates($filter)
    {
        $fromDate = Carbon::today($this->timezone);
        $toDate = Carbon::today($this->timezone);

        switch ($filter) {
            case AdminDateFilter::YESTERDAY:
                $fromDate = Carbon::yesterday($this->timezone);
                $toDate = Carbon::yesterday($this->timezone);
                break;
            case AdminDateFilter::BEFORE_YESTERDAY:
                $fromDate = Carbon::today($this->timezone)->subDays(2);
                $toDate = Carbon::today($this->timezone)->subDays(2);
                break;
            case AdminDateFilter::THIS_WEEK:
                $fromDate = Carbon::today($this->timezone)->startOfWeek();
                break;
            case AdminDateFilter::LAST_WEEK:
                $fromDate = Carbon::today($this->timezone)->subWeek()->startOfWeek();
                $toDate = Carbon::today($this->timezone)->subWeek()->endOfWeek();
                break;
            case AdminDateFilter::BEFORE_LAST_WEEK:
                $fromDate = Carbon::today($this->timezone)->subWeeks(2)->startOfWeek();
                $toDate = Carbon::today($this->timezone)->subWeeks(2)->endOfWeek();
                break;
            case AdminDateFilter::SEVEN_DAYS:
                $fromDate = Carbon::today($this->timezone)->subDays(7);
                break;
            case AdminDateFilter::BEFORE_SEVEN_DAYS:
                $fromDate = Carbon::today($this->timezone)->subDays(14);
                $toDate = Carbon::today($this->timezone)->subDays(7);
                break;
            case AdminDateFilter::THIS_MONTH:
                $fromDate = Carbon::today($this->timezone)->startOfMonth();
                break;
            case AdminDateFilter::LAST_MONTH:
                $fromDate = Carbon::today($this->timezone)->subMonth()->startOfMonth();
                $toDate = Carbon::today($this->timezone)->subMonth()->endOfMonth();
                break;
            case AdminDateFilter::BEFORE_LAST_MONTH:
                $fromDate = Carbon::today($this->timezone)->subMonths(2)->startOfMonth();
                $toDate = Carbon::today($this->timezone)->subMonths(2)->endOfMonth();
                break;
            case AdminDateFilter::THIRTY_DAYS:
                $fromDate = Carbon::today($this->timezone)->subDays(30);
                break;
            case AdminDateFilter::BEFORE_THIRTY_DAYS:
                $fromDate = Carbon::today($this->timezone)->subDays(60);
                $toDate = Carbon::today($this->timezone)->subDays(30);
                break;
            case AdminDateFilter::THIS_YEAR:
                $fromDate = Carbon::today($this->timezone)->startOfYear();
                break;
            case AdminDateFilter::LAST_YEAR:
                $fromDate = Carbon::today($this->timezone)->subYear()->startOfYear();
                $toDate = Carbon::today($this->timezone)->subYear()->endOfYear();
                break;
            case AdminDateFilter::BEFORE_LAST_YEAR:
                $fromDate = Carbon::today($this->timezone)->subYears(2)->startOfYear();
                $toDate = Carbon::today($this->timezone)->subYears(2)->endOfYear();
                break;
            case AdminDateFilter::LAST_SIX_MONTH:
                $fromDate = Carbon::today($this->timezone)->subMonths(5)->startOfMonth();
                break;
            case AdminDateFilter::LAST_TWELVE_MONTH:
                $fromDate = Carbon::today($this->timezone)->subMonths(11)->startOfMonth();
                break;
            case AdminDateFilter::ALL_TIME:
                $fromDate = Carbon::parse('01-07-2019');
                $toDate = Carbon::today($this->timezone);
                break;
        }

        return [$fromDate->startOfDay(), $toDate->endOfDay()];
    }

    public function getRecentCustomers($request)
    {
        $customers = \DB::table('users as u')
            ->leftJoin('packages as p', 'p.id', '=', 'u.package_id')
            ->leftJoin('companies as c', 'c.created_by', '=', 'u.id')
            ->leftJoin('source_platforms as sp', function ($join) {
                $join->on('sp.subject_id', '=', 'u.id')->whereIn('sp.id', SourcePlatform::where('subject_type', User::class)->pluck('id'));

            })
            ->select([
                'u.id', 'u.name', 'u.email', 'p.name as package_name', 'p.id as package_id', 'u.created_at', 'p.plan', 'p.discounted_price as price', 'sp.source',
                \DB::raw('count(c.id) as company_count'),
                \DB::raw('(CASE when p.slug="free" then 1 else 0 end) as isFree'),
            ])
            ->where('u.status', UserStatus::ACTIVE)
            ->where('u.is_test_user', 0)
            ->where('u.type', UserType::EMPLOYER)
            ->where('u.package_id', '!=', null)
            ->whereRaw('c.created_by=u.id')
            ->where('u.deleted_at', '=', null);

        $slug = $request->get('slug');
        if ($slug) {
            $customers->where('p.slug', $slug);
        }

        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);
        if ($filter) {
            if ($filter == AdminDateFilter::CUSTOM) {
                $from = Carbon::parse($request->get('from'))->startOfDay();
                $to = Carbon::parse($request->get('to'))->endOfDay();
            } else {
                [$from, $to] = $this->getFilterDates($filter);
            }
            $customers->whereBetween('u.created_at', [$from, $to]);
        }

        return $customers
            ->groupBy('u.id')
            ->orderBy('u.created_at', 'desc')
            ->limit(10)
            ->get();
    }

    public function getCustomers($request, $report = false)
    {
        $search = $request->get('query');
        $package = $request->get('package');
        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);
        $others = $request->get('others');

        $customersQuery = \DB::table("users")
            //->where('status' ,UserStatus::ACTIVE)
            ->where('type', UserType::EMPLOYER)
            ->whereNotNull('package_id');
//            ->whereNull('deleted_at');

        if (!blank($filter)) {
            if ($filter == AdminDateFilter::CUSTOM) {
                $from = Carbon::parse($request->get('from'))->startOfDay();
                $to = Carbon::parse($request->get('to'))->endOfDay();
            } else {
                [$from, $to] = $this->getFilterDates($filter);
            }

            $customersQuery->whereBetween('created_at', [$from, $to]);
        }

        if (!blank($search)) {
            $customersQuery->where(function ($query) use ($search){
                $query->where('name', 'like', "%{$search}%");
                $query->orWhere('email', 'like', "%{$search}%");
            });
        }

        $customers = \DB::table(\DB::raw("(".$customersQuery->toSql().") as u"))
            ->setBindings($customersQuery->getBindings())
            ->leftJoin('packages as p', 'p.id', '=', 'u.package_id')
            ->leftJoin(
                \DB::raw('(SELECT * FROM transactions WHERE status = 1) as t'),
                't.user_id',
                '=',
                'u.id'
            )
            ->leftJoin(
                \DB::raw('(SELECT created_by, COUNT(*) as company_count FROM companies GROUP BY created_by) as c'),
                'c.created_by',
                '=',
                'u.id'
            )
            ->leftJoin(
                \DB::raw('
            (SELECT c.created_by,
                SUM(CASE WHEN jp.status = 2 THEN 1 ELSE 0 END) as published_count,
                SUM(CASE WHEN jp.status = 1 THEN 1 ELSE 0 END) as draft_count,
                SUM(CASE WHEN jp.status = 3 THEN 1 ELSE 0 END) as archived_count
             FROM job_posts jp
             INNER JOIN companies c ON jp.company_id = c.id
             GROUP BY c.created_by
            ) as jp_counts
        '),
                'jp_counts.created_by',
                '=',
                'u.id'
            )
            ->leftJoin(
                \DB::raw('
        (SELECT c.created_by,
            COUNT(ja.id) as total_applicants
         FROM job_applicants ja
         LEFT JOIN companies c ON ja.company_id = c.id
         WHERE ja.status != '.JobApplyStatus::PENDING.'
           AND ja.deleted_at IS NULL
         GROUP BY c.created_by
        ) as applicant_counts
    '),
                'applicant_counts.created_by',
                '=',
                'u.id'
            );

        $customers->select([
            'u.id', 'u.name', 'u.email', 'u.created_at', 'u.package_validity', 'u.is_test_user',
            \DB::raw('(CASE when p.slug="free" then 1 else 0 end) as isFree'),
            'p.name as package_name', 'p.price as package_price', 'p.discount as package_discount',
            'p.discounted_price as package_discounted_price', 'p.id as package_id',
            \DB::raw('COALESCE(SUM(t.total), 0) as total_spent'),
            \DB::raw('COALESCE(c.company_count, 0) as company_count'),
            \DB::raw('COALESCE(jp_counts.published_count, 0) as published_jobs'),
            \DB::raw('COALESCE(jp_counts.draft_count, 0) as draft_jobs'),
            \DB::raw('COALESCE(jp_counts.archived_count, 0) as archived_jobs'),
            \DB::raw('COALESCE(applicant_counts.total_applicants, 0) as total_applicants')
        ]);

        if ($package) {
            if ($package == 'pro') {
                $customers->where('p.slug', '!=', 'free');
            } else {
                $customers->where('p.slug', $package);
            }
        }

        $customers->groupBy('u.id');

        if ($others) {
            $customers->orderByDesc('total_spent');
        } else {
            $customers->orderBy('u.created_at', 'desc');
        }

        if ($report) {
            return $customers->get();
        } else {
            return $customers->paginate(20)->toArray();
        }
    }

    public function getLoginActivities($request)
    {
        $search = $request->get('query');
        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);

        // Start the query on the 'login_activities' table
        $loginActivitiesQuery = \DB::table('login_activities as la')
            ->leftJoin('users as u', 'u.id', '=', 'la.user_id')
            ->select([
                'u.name',
                'u.email',
                'la.ip_address',
                'la.user_agent',
                'la.created_at as login_time',
                \DB::raw("CASE
                    WHEN la.login_type = " . \App\Enums\LoginActivityLogType::MANUAL . " THEN 'Manual'
                    WHEN la.login_type = " . \App\Enums\LoginActivityLogType::SOCIAL . " THEN 'Social'
                    ELSE 'Unknown' END as login_type")
            ]);

        if (!blank($filter)) {
            if ($filter == AdminDateFilter::CUSTOM) {
                // Check if request is an array or instance of Request
                if (is_array($request)) {
                    $from = Carbon::parse($request['from'])->startOfDay();
                    $to = Carbon::parse($request['to'])->endOfDay();
                } else {
                    $from = Carbon::parse($request->get('from'))->startOfDay();
                    $to = Carbon::parse($request->get('to'))->endOfDay();
                }

                $loginActivitiesQuery->whereBetween('la.created_at', [$from, $to]);
            } else {
                [$from, $to] = $this->getFilterDates($filter);
                $loginActivitiesQuery->whereBetween('la.created_at', [$from, $to]);
            }
        }

        if (!blank($search)) {
            $loginActivitiesQuery->where(function ($query) use ($search) {
                $query->where('u.name', 'like', "%{$search}%")
                    ->orWhere('u.email', 'like', "%{$search}%");
            });
        }

        $loginActivities = $loginActivitiesQuery->orderByDesc('la.created_at')
            ->paginate(20);

        foreach ($loginActivities as &$activity) {
            list($browser, $platform) = $this->getUserAgent($activity->user_agent);
            $activity->browser = $browser;
            $activity->platform = $platform;
        }

        return $loginActivities->toArray();
    }

    private static function getUserAgent($agent): array
    {
        $user_agent = strtolower($agent);
        $browser = 'Unknown';
        $platform = 'Unknown';

        // Check platform
        if (strpos($user_agent, 'linux') !== false || strpos($user_agent, 'ubuntu') !== false) {
            $platform = 'Linux';
        } elseif (strpos($user_agent, 'macintosh') !== false || strpos($user_agent, 'mac os x') !== false) {
            $platform = 'Mac';
        } elseif (strpos($user_agent, 'windows') !== false || strpos($user_agent, 'win32') !== false) {
            $platform = 'Windows';
        }

        // Check browser
        if (strpos($user_agent, 'playwright') !== false) {
            $browser = 'Playwright';
        } elseif (strpos($user_agent, 'msie') !== false || strpos($user_agent, 'trident') !== false) {
            $browser = 'Internet Explorer';
        } elseif (strpos($user_agent, 'firefox') !== false) {
            $browser = 'Mozilla Firefox';
        } elseif (strpos($user_agent, 'chrome') !== false) {
            $browser = 'Google Chrome';
        } elseif (strpos($user_agent, 'safari') !== false && strpos($user_agent, 'chrome') === false) {
            $browser = 'Apple Safari';
        } elseif (strpos($user_agent, 'opera') !== false || strpos($user_agent, 'opr') !== false) {
            $browser = 'Opera';
        }

        return [$browser, $platform];
    }


    public function transformCustomerList($customers)
    {
        return User::hydrate(data_get($customers, 'data', []))->load([
            'sourcePlatform',
            'transections' => function ($query) {
                $query->select([
                    'id', 'user_id', 'package_id', 'price', 'discount', 'total', 'created_at','created_at','coupon', 'status'
                ])->latest();
            },
        ])->loadCount('myCompanies')->map(function ($user) {
            return [
                "company_count" => data_get($user, 'my_companies_count', 0),
                "coupon_code" => data_get($user, 'transections.0.coupon.code'),
                "coupon_name" => data_get($user, 'transections.0.coupon.name'),
                "created_at" => data_get($user,'created_at'),
                "discount" => data_get($user, 'transections.0.discount', 'package_discount'),
                "email" => data_get($user, 'email'),
                "id" => data_get($user, 'id'),
                "isFree" => data_get($user, 'isFree'),
                "is_test_user" => data_get($user, 'is_test_user'),
                "name" => data_get($user, 'name'),
                "package_id" => data_get($user, 'package_id'),
                "package_name" => data_get($user, 'package_name'),
                "package_validity" => data_get($user, 'package_validity'),
                "price" => data_get($user, 'transections.0.price', 'package_price'),
                "source" => data_get($user, 'sourcePlatform.source'),
                "total" => data_get($user, 'transections.0.total', 'package_discounted_price'),
                "total_spent" => number_format($user->transections->where('status', 1)->sum('total'), 2),
                "user" => $user,
            ];
        });
    }

    public function getTransactions($request)
    {
        $search = $request->get('query');
        $type = $request->get('type');
        $status = $request->get('status');
        $gatewayType = $request->get('gatewayType');
        $hundredPercentCoupon = $request->get('hundredPercentCoupon');
        $subscriptionPackage = $request->get('subscriptionPackage');

        $query = Transaction::with(['user' => fn($q) => $q->withTrashed(), 'user.sourcePlatform', 'user.package'])->where('price', '>', 0)
            ->whereHas('user', function ($query) use ($search, $subscriptionPackage) {
                $query->withTrashed();
                if(!blank($search)) {
                    $query->where('name', 'like', "%{$search}%");
                    $query->orWhere('email', 'like', "%{$search}%");
                }
                if (!blank($subscriptionPackage)) {
                    $query->whereHas('package', function ($q) use ($subscriptionPackage) {
                        $q->where('id', $subscriptionPackage);
                    });
                }
            })->orderBy('updated_at', 'DESC');

        if($type) {
            $query->where('type', $type);
        }
        if(!blank($status)) {
            $query->where('status', $status);
        }

        if ($gatewayType == PaymentMethod::STRIPE) {
            $query->whereNotNull('stripe');
        }

        if ($gatewayType == PaymentMethod::PAYPAL) {
            $query->whereNotNull('paypal');
        }

        if ($hundredPercentCoupon) {
            $query->where(function ($q) {
                $q->where('total', 0);
                $q->where('price', '>', 0);
                $q->whereColumn('price', 'discount');
            });
        }

        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);
        if ($filter) {
            if ($filter == AdminDateFilter::CUSTOM) {
                $from = Carbon::parse($request->get('from'))->startOfDay();
                $to = Carbon::parse($request->get('to'))->endOfDay();
            } else {
                [$from, $to] = $this->getFilterDates($filter);
            }
            $query->whereBetween('created_at', [$from, $to]);
        }

        $transactions = $query->paginate(20);
        $transactions->transform(function ($transaction) {
            $paid = (bool) $transaction->status;

            if (data_get($transaction, 'total') == 0 && data_get($transaction, 'user.package.plan') === PackagePlan::LIFETIME) {
                $paid = true;
            }

            return [
                'id' => data_get($transaction, 'id'),
                'name' => data_get($transaction, 'user.name'),
                'email' => data_get($transaction, 'user.email'),
                'source' => data_get($transaction, 'user.sourcePlatform.source'),
                'price' => data_get($transaction, 'price'),
                'total' => data_get($transaction, 'total'),
                'coupon' => data_get($transaction, 'coupon.code'),
                'date' => $transaction->created_at->setTimezone('Asia/Dhaka')->format(config('easyjob.user.datetime_format')),
                'package' => data_get($transaction, 'user.package.name') ?? __('responses.subscription.package_deleted'),
                'description' => data_get($transaction, 'desc'),
                'payment_method' => data_get(trans('payment_method'), $transaction->payment_method, ''),
                'status' => data_get($transaction, 'status'),
                'trans_id' => data_get($transaction, 'trans_id'),
                'paid' => $paid,
                'type_id' => data_get($transaction, 'type'),
                'type' => data_get(trans('payment_type'), data_get($transaction, 'type')),
                'error_message' => data_get($transaction, 'stripe.error_message', ''),
                'deleted' => !is_null(data_get($transaction, 'user.deleted_at'))
            ];
        });

        return $transactions;

    }

    public function getCustomerDetails($id)
    {
        return \DB::table('users as u')
            ->leftJoin('packages as p', 'p.id', '=', 'u.package_id')
            ->leftJoin('companies as c', 'c.created_by', '=', 'u.id')
            ->leftJoin('job_posts as jp', 'jp.company_id', '=', 'c.id')
            ->leftJoin(\DB::raw('(SELECT t1.*
                         FROM transactions t1
                         WHERE t1.id = (SELECT MAX(t2.id) FROM transactions t2 WHERE t2.user_id = t1.user_id)
                         ) as t'), function ($join) {
                $join->on('t.user_id', '=', 'u.id');
                $join->on('t.package_id', '=', 'u.package_id');
            })
            ->select([
                'u.*', 'p.name as package_name', 'p.id as package_id', 'u.is_test_user',
                \DB::raw('IFNULL(t.price, p.price) as price, IFNULL(t.discount, p.discount) as discount, IFNULL(t.total, p.discounted_price) as total'),
                't.coupon->code as coupon_code', 't.coupon->name as coupon_name',
                \DB::raw('count(c.id) as company_count'),
                \DB::raw("sum(IF(jp.status = ".JobStatus::PUBLISHED.", 1, 0)) as published_jobs, sum(IF(jp.status = ".JobStatus::DRAFT.", 1, 0)) as draft_jobs, sum(IF(jp.status = ".JobStatus::ARCHIVED.", 1, 0)) as archived_jobs"),
                \DB::raw('(CASE when p.slug="free" then 1 else 0 end) as isFree'),
            ])
            //->where('u.status', UserStatus::ACTIVE)
            ->where('u.type', UserType::EMPLOYER)
            ->where('u.package_id', '!=', null)
//            ->where('u.deleted_at', '=', null)
            ->where('u.id', $id)
            ->first();
    }

    private function getHourlyPackageSalesAmount($packageId, $slot, $filter)
    {
        if ($filter == AdminDateFilter::YESTERDAY) {
            $date = Carbon::yesterday()->toDateString()." ".$slot;
            $dateTime = Carbon::parse($date);
        } else {
            $dateTime = Carbon::parse($slot);
        }
        $sale = Transaction::where('status', 1)
            ->where('package_id', $packageId)
            ->whereBetween('created_at', [$dateTime->startOfHour()->toDateTimeString(), $dateTime->endOfHour()->toDateTimeString()])
            ->selectRaw('SUM(total) as total')
            ->first();

        if ($sale) {
            return $sale->total ?? 0;
        } else {
            return 0;
        }
    }

    private function getPackageSalesAmount($packageId, $from, $to)
    {
        $sale = Transaction::where('status', 1)
            ->where('package_id', $packageId)
            ->whereBetween('created_at', [$from, $to])
            ->selectRaw('SUM(total) as total')
            ->first();

        if ($sale) {
            return $sale->total ?? 0;
        } else {
            return 0;
        }
    }

    private function countHourlyPackageSales($packageId, $slot, $filter)
    {
        if ($filter == AdminDateFilter::YESTERDAY) {
            $date = Carbon::yesterday()->toDateString()." ".$slot;
            $dateTime = Carbon::parse($date);
        } else {
            $dateTime = Carbon::parse($slot);
        }
        return Transaction::where('status', 1)
            ->where('package_id', $packageId)
            ->whereBetween('created_at', [$dateTime->startOfHour()->toDateTimeString(), $dateTime->endOfHour()->toDateTimeString()])
            ->count();
    }

    private function countPackageSales($packageId, $from, $to)
    {
        return Transaction::where('status', 1)
            ->where('package_id', $packageId)
            ->whereBetween('created_at', [$from, $to])
            ->count();
    }

    private function getMonthlyPackageSales($packageId, $slot)
    {
        $sale = Transaction::where('status', 1)
            ->where('package_id', $packageId)
            ->whereBetween('created_at', [$slot->startOfMonth()->toDateTimeString(), $slot->endOfMonth()->toDateTimeString()])
            ->selectRaw('SUM(total) as total')
            ->first();

        if ($sale) {
            return $sale->total ?? 0;
        } else {
            return 0;
        }
    }

    /*public function getAnalyticsChartData($from, $to, $filter)
    {
        $isDaily = in_array($filter, [AdminDateFilter::TODAY, AdminDateFilter::YESTERDAY]);
        $isWeekly = in_array($filter, [AdminDateFilter::THIS_WEEK, AdminDateFilter::LAST_WEEK]);
//        $isMonthly = in_array($filter, [AdminDateFilter::THIS_MONTH, AdminDateFilter::LAST_MONTH]);
        $isYearly = in_array($filter, [AdminDateFilter::THIS_YEAR, AdminDateFilter::LAST_YEAR, AdminDateFilter::ALL_TIME]);
        if ($isDaily) {

            if ($filter == AdminDateFilter::TODAY) {
                $toTime = Carbon::now($this->timezone)->subHour()->toTimeString();
            } else {
                $toTime = '23:00:00';
            }

            $period = createTimeRange('00:00:00', $toTime);
        } elseif ($isYearly) {
            $period = CarbonPeriod::create($from, '1 Month', $to);
        } else {
            $period = CarbonPeriod::create($from, $to);
        }

        return Package::where('price', '>', 0)
            ->orderBy('type', 'asc')
            ->orderBy('price')
            ->get(['name', 'id', 'type'])
            ->map(function ($package) use ($period, $isDaily, $isWeekly, $isYearly, $filter) {
                $data = [];
                $categories = [];
                foreach ($period as $slot) {
                    if ($isDaily) {
                        $data[] = $this->getHourlyPackageSalesAmount($package->id, $slot, $filter);
                    } elseif ($isWeekly) {
                        $data[] = $this->getPackageSalesAmount($package->id, $slot->startOfDay()->toDateTimeString(), $slot->endOfDay()->toDateTimeString());
                        $categories[] = $slot->format('D');
                    } elseif ($isYearly) {
                        $data[] = $this->getPackageSalesAmount($package->id, $slot->startOfMonth()->toDateTimeString(), $slot->endOfMonth()->toDateTimeString());
                        $categories[] = $slot->format('M');
                    } else {
                        $data[] = $this->getPackageSalesAmount($package->id, $slot->startOfDay()->toDateTimeString(), $slot->endOfDay()->toDateTimeString());
                        $categories[] = $slot->format('d');
                    }
                }
                return [
                    'name' => $package->name,
                    'data' => $data,
                    'categories' => $categories,
                    'showByDefault' => ($package->type == PackageType::REGULAR)
                ];
            })->toArray();
    }*/

    /*public function getAnalyticsChartData($from, $to, $filter)
    {
        $isDaily = in_array($filter, [AdminDateFilter::TODAY, AdminDateFilter::YESTERDAY]);
        $isWeekly = in_array($filter, [AdminDateFilter::THIS_WEEK, AdminDateFilter::LAST_WEEK]);
//        $isMonthly = in_array($filter, [AdminDateFilter::THIS_MONTH, AdminDateFilter::LAST_MONTH]);
        $isYearly = in_array($filter, [AdminDateFilter::THIS_YEAR, AdminDateFilter::LAST_YEAR, AdminDateFilter::ALL_TIME]);
        if ($isDaily) {

            if ($filter == AdminDateFilter::TODAY) {
                $toTime = Carbon::now($this->timezone)->subHour()->toTimeString();
            } else {
                $toTime = '23:00:00';
            }

            $period = createTimeRange('00:00:00', $toTime);
        } elseif ($isYearly) {
            $period = CarbonPeriod::create($from, '1 Month', $to);
        } else {
            $period = CarbonPeriod::create($from, $to);
        }

        return Package::orderBy('type', 'asc')
            ->orderBy('price')
            ->get(['name', 'id', 'type'])
            ->map(function ($package) use ($period, $isDaily, $isWeekly, $isYearly, $filter) {
                $data = [];
                $categories = [];
                foreach ($period as $k => $slot) {
                    if ($isDaily) {
                        $data[] = $this->countHourlyPackageSales($package->id, $slot, $filter);
                        $categories[] = ($k + 1);
                    } elseif ($isWeekly) {
                        $data[] = $this->countPackageSales($package->id, $slot->startOfDay()->toDateTimeString(), $slot->endOfDay()->toDateTimeString());
                        $categories[] = $slot->format('D');
                    } elseif ($isYearly) {
                        $data[] = $this->countPackageSales($package->id, $slot->startOfMonth()->toDateTimeString(), $slot->endOfMonth()->toDateTimeString());
                        $categories[] = $slot->format('M');
                    } else {
                        $data[] = $this->countPackageSales($package->id, $slot->startOfDay()->toDateTimeString(), $slot->endOfDay()->toDateTimeString());
                        $categories[] = $slot->format('d');
                    }
                }
                return [
                    'name' => $package->name,
                    'data' => $data,
                    'categories' => $categories,
                    'showByDefault' => ($package->type == PackageType::REGULAR)
                ];
            })->toArray();
    }*/

    public function getAnalyticsChartData($from, $to, $filter)
    {
        $isDaily = in_array($filter, [AdminDateFilter::TODAY, AdminDateFilter::YESTERDAY]);
        $isWeekly = in_array($filter, [AdminDateFilter::THIS_WEEK, AdminDateFilter::LAST_WEEK]);
//        $isMonthly = in_array($filter, [AdminDateFilter::THIS_MONTH, AdminDateFilter::LAST_MONTH]);
        $isYearly = in_array($filter, [AdminDateFilter::THIS_YEAR, AdminDateFilter::LAST_YEAR, AdminDateFilter::ALL_TIME]);
        if ($isDaily) {

            if ($filter == AdminDateFilter::TODAY) {
                $toTime = Carbon::now($this->timezone)->subHour()->toTimeString();
            } else {
                $toTime = '23:00:00';
            }

            $period = createTimeRange('00:00:00', $toTime);
        } elseif ($isYearly) {
            $period = CarbonPeriod::create($from, '1 Month', $to);
        } else {
            $period = CarbonPeriod::create($from, $to);
        }

        $categories = [];

        $data = [
            [
                'id' => AdminDashboardAnalytics::CUSTOMER,
                'name' => 'Customers',
                'data' => [],
            ],
            [
                'id' => AdminDashboardAnalytics::COMPANY,
                'name' => 'Companies',
                'data' => [],
            ],
            [
                'id' => AdminDashboardAnalytics::JOB,
                'name' => 'Jobs',
                'data' => [],
            ],
            [
                'id' => AdminDashboardAnalytics::OPEN_JOB,
                'name' => 'Open Jobs',
                'data' => [],
            ],
            [
                'id' => AdminDashboardAnalytics::CANDIDATE,
                'name' => 'Candidates',
                'data' => [],
            ],
            [
                'id' => AdminDashboardAnalytics::HIRED,
                'name' => 'Hired Candidates',
                'data' => [],
            ],
        ];
        foreach ($period as $k => $slot) {
            if ($isDaily) {
                if ($filter == AdminDateFilter::YESTERDAY) {
                    $date = Carbon::yesterday()->toDateString()." ".$slot;
                    $dateTime = Carbon::parse($date);
                } else {
                    $dateTime = Carbon::parse($slot);
                }
                $from = $dateTime->startOfHour()->toDateTimeString();
                $to = $dateTime->endOfHour()->toDateTimeString();
                $categories[] = $dateTime->addMinute()->format('h A');
            } elseif ($isWeekly) {
                $from = $slot->startOfDay()->toDateTimeString();
                $to = $slot->endOfDay()->toDateTimeString();
                $categories[] = $slot->format('D');
            } elseif ($isYearly) {
                $from = $slot->startOfMonth()->toDateTimeString();
                $to = $slot->endOfMonth()->toDateTimeString();
                $categories[] = $slot->format('M, y');
            } else {
                $from = $slot->startOfDay()->toDateTimeString();
                $to = $slot->endOfDay()->toDateTimeString();
                $categories[] = $slot->format('d M');
            }

            $data[AdminDashboardAnalytics::CUSTOMER]['data'][] = $this->countUser(UserType::EMPLOYER, $from, $to);
            $data[AdminDashboardAnalytics::COMPANY]['data'][] = Company::whereBetween('created_at', [$from, $to])->count();
            $data[AdminDashboardAnalytics::JOB]['data'][] = JobPost::whereBetween('created_at', [$from, $to])->count();
            $data[AdminDashboardAnalytics::OPEN_JOB]['data'][] = JobPost::whereBetween('created_at', [$from, $to])
                ->where('expire_at', '>=', $to)
                ->where('status', JobStatus::PUBLISHED)
                ->count();
            $data[AdminDashboardAnalytics::CANDIDATE]['data'][] = $this->countUser(UserType::CANDIDATE, $from, $to);
            $data[AdminDashboardAnalytics::HIRED]['data'][] = JobApplicant::whereStatus(JobApplyStatus::SELECTED)->whereBetween('pipeline_updated_at', [$from, $to])->count();;
        }
        return [$data, $categories];
    }

    public function getRevenueChartData($from, $to, $filter)
    {
        $isDaily = in_array($filter, [AdminDateFilter::TODAY, AdminDateFilter::YESTERDAY]);
        $isWeekly = in_array($filter, [AdminDateFilter::THIS_WEEK, AdminDateFilter::LAST_WEEK]);
//        $isMonthly = in_array($filter, [AdminDateFilter::THIS_MONTH, AdminDateFilter::LAST_MONTH]);
        $isYearly = in_array($filter, [AdminDateFilter::THIS_YEAR, AdminDateFilter::LAST_YEAR, AdminDateFilter::ALL_TIME]);
        if ($isDaily) {

            if ($filter == AdminDateFilter::TODAY) {
                $toTime = Carbon::now($this->timezone)->subHour()->toTimeString();
            } else {
                $toTime = '23:00:00';
            }

            $period = createTimeRange('00:00:00', $toTime);
        } elseif ($isYearly) {
            $period = CarbonPeriod::create($from, '1 Month', $to);
        } else {
            $period = CarbonPeriod::create($from, $to);
        }

        $data = [
            [
                'name' => 'Free',
                'data' => [],
            ],
            [
                'name' => 'Pro',
                'data' => [],
            ],
        ];
        $categories = [];

        foreach ($period as $k => $slot) {

            if ($isDaily) {
                if ($filter == AdminDateFilter::YESTERDAY) {
                    $date = Carbon::yesterday()->toDateString()." ".$slot;
                    $dateTime = Carbon::parse($date);
                } else {
                    $dateTime = Carbon::parse($slot);
                }
                $users = $this->countFreeProCustomers($dateTime->startOfHour()->toDateTimeString(), $dateTime->endOfHour()->toDateTimeString());
                $categories[] = $dateTime->addHour()->format('h A');
            } elseif ($isWeekly) {
                $users = $this->countFreeProCustomers($slot->startOfDay()->toDateTimeString(), $slot->endOfDay()->toDateTimeString());
                $categories[] = $slot->format('D');
            } elseif ($isYearly) {
                $users = $this->countFreeProCustomers($slot->startOfMonth()->toDateTimeString(), $slot->endOfMonth()->toDateTimeString());
                $categories[] = $slot->format('M, y');
            } else {
                $users = $this->countFreeProCustomers($slot->startOfDay()->toDateTimeString(), $slot->endOfDay()->toDateTimeString());
                $categories[] = $slot->format('d M');
            }

            $data[0]['data'][] = data_get($users, 'free_count');
            $data[1]['data'][] = data_get($users, 'pro_count');
//            $data[0]['data'][] = rand(10,20);
//            $data[1]['data'][] = rand(5,15);
        }
        return [$data, $categories];
    }

    private function countFreeProCustomers($from, $to)
    {
        return User::whereBetween('users.created_at', [$from, $to])
            ->leftJoin('packages', 'packages.id', '=', 'users.package_id')
            ->where('users.type', UserType::EMPLOYER)
            ->select(
                \DB::raw('sum(IF(packages.discounted_price = 0, 1, 0)) as free_count'),
                \DB::raw('sum(IF(packages.discounted_price > 0, 1, 0)) as pro_count')
            )
            ->first();
    }

    public function totalPaidUsers($from, $to, $filter)
    {
        $count = Transaction::whereBetween('created_at', [$from, $to])->count();
        $customerData = [
            'title' => "Total Paid Users",
            'count' => $count,
        ];

        [$prevFrom, $prevTo] = $this->getFilterPrevDates($filter);
        $prevCount = Transaction::whereBetween('created_at', [$prevFrom, $prevTo])->count();;
        $customerData['previousCount'] = $prevCount;

        [$percent, $indexType] = $this->calculatePercent($count, $prevCount);

        $customerData['percent'] = $percent;
        $customerData['indexType'] = $indexType;
        return $customerData;
    }

    public function totalUpgradedUsers($from, $to, $filter)
    {
        $count = $this->countUpgradedUsers($from, $to);
        $customerData = [
            'title' => "Converted from Free to Paid",
            'count' => $count,
        ];

        [$prevFrom, $prevTo] = $this->getFilterPrevDates($filter);
        $prevCount = $this->countUpgradedUsers($prevFrom, $prevTo);
        $customerData['previousCount'] = $prevCount;

        [$percent, $indexType] = $this->calculatePercent($count, $prevCount);

        $customerData['percent'] = $percent;
        $customerData['indexType'] = $indexType;;
        return $customerData;
    }

    private function countUpgradedUsers($from, $to)
    {
        return Transaction::whereBetween('created_at', [$from, $to])
            ->where('type', '=', PaymentType::PACKAGE_UPGRADE)
            ->where('total', '>', 0)
            ->whereRaw('user_id in (select user_id from transactions where total = 0)')
            ->count();
    }

    public function getCustomerCounters($from, $to)
    {
        $customer = \DB::table('users as u')
            ->leftJoin('packages as p', 'p.id', '=', 'u.package_id')
            ->select([
                \DB::raw(
                    'COUNT(u.id) as total_customer,
                    SUM(IF(p.discounted_price = 0, 1, 0)) as free_customer,
                    SUM(IF(p.discounted_price > 0, 1, 0)) as pro_customer,
                    SUM(IF(u.provider = '.SocialAuthProvider::GOOGLE.', 1, 0)) as google_customer,
                    SUM(IF(u.provider = '.SocialAuthProvider::LINKEDIN.', 1, 0)) as linkedin_customer,
                    SUM(IF(ISNULL(u.provider), 1, 0)) as email_customer'
                ),
            ])
            ->where('u.status', UserStatus::ACTIVE)
            ->where('u.type', UserType::EMPLOYER)
            ->where('u.package_id', '!=', null)
            ->where('u.deleted_at', '=', null)
            ->whereBetween('u.created_at', [$from, $to])->first();


            $totalPaidUser = User::select('id')->whereHas('transactions', function ($query) {
                $query->where('status', 1)->where('price', '>', 0)->where('total', '>', 0);
            })
            ->where('status', UserStatus::ACTIVE)
            ->where('type', UserType::EMPLOYER)
            ->whereNotNull('package_id')
            ->whereNull('deleted_at')
            ->whereBetween('created_at', [$from, $to])
            ->count();

        return [
            [
                'title' => 'Customers',
                'count' => data_get($customer, 'total_customer', 0),
            ],
            [
                'title' => 'Free Customers',
                'count' => data_get($customer, 'free_customer', 0),
            ],
            [
                'title' => 'Pro Customers',
                'count' => data_get($customer, 'pro_customer', 0),
            ],
            [
                'title' => 'Signed up with Google',
                'count' => data_get($customer, 'google_customer', 0),
            ],
            [
                'title' => 'Signed up with LinkedIn',
                'count' => data_get($customer, 'linkedin_customer', 0),
            ],
            [
                'title' => 'Paid User',
                'count' => $totalPaidUser,
            ],
        ];
    }

    /**
     * Get Transaction Counter
     * @param $from
     * @param $to
     * @return array[]
     */
    public function getTransactionCounters($from, $to, $gatewayType): array
    {
        $transactionQuery = Transaction::has('user')
            ->where('price', '>', 0)
            ->whereBetween('created_at', [$from, $to]);

        if ($gatewayType == PaymentMethod::STRIPE) {
            $transactionQuery->whereNotNull('stripe');
        }

        if ($gatewayType == PaymentMethod::PAYPAL) {
            $transactionQuery->whereNotNull('paypal');
        }

        $transactions = $transactionQuery->selectRaw('
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as paid_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as failed_count,
            SUM(CASE WHEN total = 0 AND price > 0 AND price = discount THEN 1 ELSE 0 END) as coupon_count
        ')
        ->with('user.sourcePlatform', 'user.package')
        ->first();

        return [
            [
                'type' => 'paid',
                'title' => 'Paid Transactions',
                'count' => data_get($transactions, 'paid_count', 0)
            ],
            [
                'type' => 'failed',
                'title' => 'Failed Transactions',
                'count' => data_get($transactions, 'failed_count', 0)
            ],
            [
                'type' => 'coupon',
                'title' => '100% Coupon Transactions',
                'count' => data_get($transactions, 'coupon_count', 0)
            ]
        ];
    }

    public function getCandidateCounters($from, $to)
    {
        $totalCount = JobApplicant::where('status', '!=', JobApplyStatus::PENDING)
            ->whereBetween('created_at', [$from, $to])
            ->count();
        $activeCount = JobApplicant::where('status', JobApplyStatus::COMPLETE)
            ->whereBetween('created_at', [$from, $to])
            ->count();
        $hiredCount = JobApplicant::where('status', '=', JobApplyStatus::SELECTED)
            ->whereBetween('created_at', [$from, $to])
            ->count();

        return [
            [
                'title' => 'Candidates',
                'count' => $totalCount,
            ],
            [
                'title' => 'Active Candidates',
                'count' => $activeCount,
            ],
            [
                'title' => 'Successfully Hired',
                'count' => $hiredCount,
            ],
        ];
    }

    public function getJobsCounters($from, $to)
    {
        $totalJobs = JobPost::whereBetween('created_at', [$from, $to])
            ->where('status', '!=', JobStatus::DELETED)
            ->count();

        $activeJobs = JobPost::whereBetween('created_at', [$from, $to])
            ->where('status', JobStatus::PUBLISHED)
            ->where('expire_at', '>=', $to)
            ->count();

        $hiredJobs = JobPost::whereHas('applicants', function ($query) use ($from, $to) {
            $query->whereBetween('created_at', [$from, $to]);
            $query->where('status', JobApplyStatus::SELECTED);
        })->count();

        return [
            [
                'title' => 'Jobs',
                'count' => $totalJobs,
            ],
            [
                'title' => 'Active Jobs',
                'count' => $activeJobs,
            ],
            [
                'title' => 'Jobs (Hired)',
                'count' => $hiredJobs,
            ],
        ];
    }

    public function getJobBoardCounters($from, $to): array
    {
        $companyIds = CompanySetting::where('key', CompanySettingService::JOB_BOARD)
            ->where('value', true)
            ->pluck('company_id')
            ->toArray();

        $jobsQuery = JobPost::whereIn('company_id', $companyIds)
            ->whereBetween('created_at', [$from, $to])
            ->where(function ($query) {
                $query->whereNull('expire_at')
                    ->orWhere('expire_at', '>', \DB::raw('CURDATE()'));
            });

        $jobStatuses = [
            'Pending Jobs'   => JobBoardStatus::PENDING,
            'Published Jobs' => JobBoardStatus::PUBLISHED,
            'Rejected Jobs'  => JobBoardStatus::REJECTED,
        ];

        $jobCounts = [];

        foreach ($jobStatuses as $title => $status) {
            $jobCounts[] = [
                'title' => $title,
                'status_on_job_board' => $status,
                'count' => $jobsQuery->clone()
                    ->where('status', JobStatus::PUBLISHED)
                    ->where('status_on_job_board', $status)->count(),
            ];
        }

        return $jobCounts;

    }

    public function getCompanies($request, $from = null, $to = null, $report = false)
    {
        $search = $request->get('query');
        $companyType = $request->get('company_type');
        $companySize = $request->get('company_size');
        $package = $request->get('package');
        $other = $request->get('other');
        $jobPostRange = $request->get('job');

//        $companyQuery = \DB::table('companies')->when($search, function ($query) use ($search) {
//            $query->where('c.name', 'like', "%{$search}%");
//        });
//

        $companies = \DB::table('companies as c')
            ->leftJoin('users as u', 'c.created_by', '=', 'u.id')
            ->leftJoin('packages as p', 'p.id', '=', 'u.package_id')
            ->leftJoin('job_applicants as ja', function ($join) {
                $join->on('ja.company_id', '=', 'c.id')
                    ->where('ja.status', '!=', JobApplyStatus::PENDING)
                    ->whereNull('ja.deleted_at');
            })
            ->leftJoin(\DB::raw('(
        SELECT
            company_id,
            COUNT(id) AS total_jobs,
            SUM(CASE WHEN deleted_at IS NULL THEN 1 ELSE 0 END) AS active_jobs
        FROM job_posts
        GROUP BY company_id
    ) AS job_counts'), 'job_counts.company_id', '=', 'c.id')
            ->select([
                'c.id as id', 'c.is_verified', 'c.name as name', 'c.username', 'u.name as creator', 'c.company_type_id as company_type', 'c.company_size as company_size',
                'u.email as email','u.package_id as package_Id', 'p.name as package_name','p.slug as package_slug' ,'c.website', 'c.custom_domain', 'c.created_at',
                \DB::raw('concat(c.username, ".", "easy.jobs") domain'),
                \DB::raw('(CASE when p.slug="free" then 1 else 0 end) as isFree'),
                \DB::raw('COUNT(ja.id) as applicant_count'),
                \DB::raw('COUNT(CASE WHEN ja.status = 5 THEN 1 END) as selected_candidates'),
                \DB::raw('COALESCE(job_counts.total_jobs, 0) as total_jobs'),
                \DB::raw('COALESCE(job_counts.active_jobs, 0) as active_jobs')
            ])
            ->when($search, function ($query) use ($search) {
                $query->where(function ($subQuery) use ($search) {
                    $subQuery->where('c.name', 'like', "%{$search}%")
                        ->orWhere('c.username', 'like', "%{$search}%")
                        ->orWhere('u.name', 'like', "%{$search}%")
                        ->orWhere('u.email', 'like', "%{$search}%");
                });
            })
            ->when($companyType, function ($query) use ($companyType) {
                $query->where('c.company_type_id', $companyType);
            })
            ->when($companySize, function ($query) use ($companySize) {
                $query->where('c.company_size', $companySize);
            })
            ->when($package, function ($query) use ($package) {
                if ($package === 'pro') {
                    $query->having('isFree', '=', 0);
                } else {
                    $query->having('p.slug', $package);
                }
            })
            ->when($other, function ($query) use ($other) {
                if ($other == 1){
                    $query->orderBy('active_jobs', 'desc');
                } else if ($other == 2) {
                    $query->orderBy('selected_candidates', 'desc');
                } else if ($other == 3) {
                    $query->where('c.is_verified', 1);
                } else if ($other == 4) {
                    $query->where('c.is_verified', 0);
                } else if ($other == 5) {
                    $query->having('selected_candidates', 0);
                } else if ($other == 6) {
                    $query->orderBy('applicant_count', 'desc');
                }
            });

        if (isset($jobPostRange)) {
            $companies->when($jobPostRange == 0, fn($query) => $query->having('total_jobs', '=', 0))
                ->when($jobPostRange == 1, fn($query) => $query->havingBetween('total_jobs', [1, 10]))
                ->when($jobPostRange == 2, fn($query) => $query->havingBetween('total_jobs', [11, 30]))
                ->when($jobPostRange == 3, fn($query) => $query->havingBetween('total_jobs', [31, 50]))
                ->when($jobPostRange == 4, fn($query) => $query->havingBetween('total_jobs', [51, 100]))
                ->when($jobPostRange == 5, fn($query) => $query->having('total_jobs', '>=', 100));
        }

        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);

        if ($filter) {
            $companies->whereBetween('c.created_at', [$from, $to]);
        }

        if ($report) {
            return $companies->groupBy(['c.id'])->orderBy('c.created_at', 'desc')->get();
        } else {
            return $companies->groupBy(['c.id'])->orderBy('c.created_at', 'desc')->paginate(20)->toArray();
        }
    }

    public function getCompanyDetails($id)
    {
        return \DB::table('companies as c')
            ->leftJoin('users as u', 'c.created_by', '=', 'u.id')
            ->leftJoin('job_posts as jp', 'jp.company_id', '=', 'c.id')
            ->leftJoin('company_types as ct', 'c.company_type_id', '=', 'ct.id')
            ->leftJoin('job_applicants as ja', function ($join) {
                $join->on('ja.job_post_id', '=', 'jp.id')
                    ->where('ja.status', '=', JobApplyStatus::SELECTED);
            })
            ->select([
                'c.*',
                'u.name as creator',
                'u.email as email',
                'ct.name as company_type',
                \DB::raw('concat(c.username, ".", "easy.jobs") as domain'),
                \DB::raw("sum(IF(jp.status = ".JobStatus::PUBLISHED.", 1, 0)) as published_jobs, sum(IF(jp.status = ".JobStatus::DRAFT.", 1, 0)) as draft_jobs, sum(IF(jp.status = ".JobStatus::ARCHIVED.", 1, 0)) as archived_jobs"),
                \DB::raw('COUNT(ja.id) as selected_candidates'),
                \DB::raw("CASE
                    WHEN c.company_size = 1 THEN '1-30'
                    WHEN c.company_size = 2 THEN '31-60'
                    WHEN c.company_size = 3 THEN '61-90'
                    WHEN c.company_size = 4 THEN '100+'
                    ELSE 'Unknown'
                  END as company_size_label")
            ])
            ->where('u.status', UserStatus::ACTIVE)
            ->where('u.type', UserType::EMPLOYER)
            ->where('u.package_id', '!=', null)
            ->where('c.id', $id)
            ->first();
    }

    public function getCompanyCounters($from, $to)
    {
        $totalCompanies = Company::count();
        $newCompanies = Company::whereBetween('created_at', [$from, $to])->count();
        $activeJobsCompanies = Company::whereHas('jobs', function ($query) use ($to) {
            $query->where('status', JobStatus::PUBLISHED);
            $query->where('expire_at', '>=', $to);
        })->count();
        return [
            [
                'title' => 'Companies',
                'count' => $totalCompanies,
            ],
            [
                'title' => 'Companies',
                'count' => $newCompanies,
            ],
            [
                'title' => 'Active Companies',
                'count' => $activeJobsCompanies,
            ],
        ];
    }

    public function getCandidates($request)
    {
        $candidates = JobApplicant::where('job_applicants.status', '!=', JobApplyStatus::PENDING)
            ->with('job', 'pipeline')
            ->leftJoin('users', 'users.id', '=', 'job_applicants.user_id')
            ->leftJoin('job_posts', 'job_posts.id', '=', 'job_applicants.job_post_id')
            ->leftJoin('companies', 'companies.id', '=', 'job_applicants.company_id')
            ->leftJoin('employments', 'employments.user_id', '=', 'job_applicants.user_id')
            ->select([
                'job_applicants.id', 'job_applicants.job_post_id', 'job_applicants.user_id', 'job_applicants.company_id', 'job_applicants.status', 'job_applicants.job_pipeline_id',
                'users.name', 'users.email', 'job_posts.title as job_title', 'companies.name as company_name',
                'job_posts.meta->pipeline as pipelines', 'job_applicants.status as job_apply_status', 'job_applicants.created_at', 'job_applicants.total_experience as experience',
            ])
            ->where([
                'users.deleted_at' => null,
                'job_posts.deleted_at' => null,
                'companies.deleted_at' => null,
            ]);

        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);
        if ($filter) {
            if ($filter == AdminDateFilter::CUSTOM) {
                $from = Carbon::parse($request->get('from'))->startOfDay();
                $to = Carbon::parse($request->get('to'))->endOfDay();
            } else {
                [$from, $to] = $this->getFilterDates($filter);
            }
            $candidates->whereBetween('job_applicants.created_at', [$from, $to]);
        }

        $search = $request->get('search', null);
        if ($search) {
            $candidates->where(function ($query) use ($search) {
                $query->where('job_posts.title', 'like', "%$search%");
                $query->orWhere('companies.name', 'like', "%$search%");
                $query->orWhere('users.name', 'like', "%$search%");
                $query->orWhere('users.email', 'like', "%$search%");
            });
        }

        $candidates = $candidates
            ->groupBy('job_applicants.id', 'employments.user_id')
            ->orderBy('job_applicants.created_at', 'desc')
            ->paginate(20);

        $list = collect($candidates->items())->map(function ($candidate) {
            return $this->serializeCandidateData($candidate);
        })->toArray();

        $pagination = $candidates->toArray();
        unset($pagination['data']);

        return [$list, $pagination];
    }

    public function serializeCandidateData($candidate)
    {
        $jobPost = $candidate->job;

        $candidate->pipelines = $jobPost->pipelines;
        $candidate->pipeline = $candidate->pipeline;
        $candidate->job_link = jobDetailsRoute($jobPost);
        $candidate->job_preview_link = jobPreviewRoute($jobPost);
        $candidate->job_is_published = jobIsPublished($jobPost);
        $candidate->experience = round(data_get($candidate, 'experience', 0), 1);

        unset($candidate->job);

        return $candidate;
    }

    public function getJobs($request)
    {
        $jobs = JobPost::has('company')->where('job_posts.status', '!=', JobStatus::DELETED)
            ->leftJoin('companies', 'companies.id', '=', 'job_posts.company_id')
            ->leftJoin('categories', 'categories.id', '=', 'job_posts.category_id')
            ->select([
                'job_posts.id', 'job_posts.slug', 'job_posts.title', 'job_posts.company_id', 'job_posts.status', 'job_posts.vacancies', 'companies.name as company_name',
                'job_posts.category_id', 'categories.name as category_name', 'job_posts.created_at', 'job_posts.expire_at',
                \DB::raw('(job_posts.expire_at < curdate()) as is_expire'),
            ])
            ->where([
                'companies.deleted_at' => null,
            ]);;

        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);
        if ($filter) {
            if ($filter == AdminDateFilter::CUSTOM) {
                $from = Carbon::parse($request->get('from'))->startOfDay();
                $to = Carbon::parse($request->get('to'))->endOfDay();
            } else {
                [$from, $to] = $this->getFilterDates($filter);
            }
            $jobs->whereBetween('job_posts.created_at', [$from, $to]);
        }

        $search = $request->get('search', null);

        if ($search) {
            $jobs->where(function ($query) use ($search) {
                $query->where('job_posts.title', 'like', "%$search%");
                $query->orWhere('companies.name', 'like', "%$search%");
                $query->orWhere('categories.name', 'like', "%$search%");
            });
        }

        $companyName = $request->get('company', '');
        if ($companyName) {
            $jobs->whereHas('company', function ($query) use ($companyName) {
                $query->where('name', 'like', "%$companyName%");
            });
        }

        $status = $request->get('status', null);
        if ($status) {
            $jobs->where('status', $status);
        }

        $jobs = $jobs
            ->orderBy('job_posts.created_at', 'desc')
            ->paginate(20);

        $list = collect($jobs->items())->map(function ($job) {
            return $this->serializeJobData($job);
        })->toArray();

        $pagination = $jobs->toArray();
        unset($pagination['data']);

        return [$list, $pagination];
    }

    public function getJobBoardJobs($request)
    {
        $companyIds = CompanySetting::where('key', CompanySettingService::JOB_BOARD)
            ->where('value', true)
            ->select('company_id')
            ->pluck('company_id')
            ->toArray();

        $jobs = JobPost::whereIn('job_posts.company_id', $companyIds)
            ->where('job_posts.status', '!=', JobStatus::DELETED)
            ->leftJoin('companies', 'companies.id', '=', 'job_posts.company_id')
            ->leftJoin('categories', 'categories.id', '=', 'job_posts.category_id')
            ->select([
                'job_posts.id', 'job_posts.slug', 'job_posts.title', 'job_posts.company_id', 'job_posts.status', 'job_posts.vacancies',
                'companies.name as company_name', 'job_posts.category_id', 'categories.name as category_name',
                'job_posts.created_at', 'job_posts.expire_at', 'job_posts.status_on_job_board', \DB::raw('(job_posts.expire_at < curdate()) as is_expire'),
            ])
            ->where(function ($query) {
                $query->whereNull('job_posts.expire_at')
                    ->orWhere('job_posts.expire_at', '>', \DB::raw('CURDATE()'));
            })
            ->whereNull('companies.deleted_at');

        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);
        if ($filter) {
            if ($filter == AdminDateFilter::CUSTOM) {
                $from = Carbon::parse($request->get('from'))->startOfDay();
                $to = Carbon::parse($request->get('to'))->endOfDay();
            } else {
                [$from, $to] = $this->getFilterDates($filter);
            }
            $jobs->whereBetween('job_posts.created_at', [$from, $to]);
        }

        $search = $request->get('search', null);
        if ($search) {
            $jobs->where(function ($query) use ($search) {
                $query->where('job_posts.title', 'like', "%$search%")
                    ->orWhere('companies.name', 'like', "%$search%")
                    ->orWhere('categories.name', 'like', "%$search%");
            });
        }

        $status = $request->get('status', JobStatus::PUBLISHED);
        if ($status) {
            $jobs->where('status', $status);
        }

        $onBoardJobStatus = $request->get('status_on_job_board', null);
        if ($onBoardJobStatus) {
            $jobs->where('status_on_job_board', $onBoardJobStatus);
        }

        $jobs = $jobs->orderBy('job_posts.created_at', 'desc')->paginate(20);

        $list = collect($jobs->items())->map(function ($job) {
            return $this->serializeJobData($job);
        })->toArray();

        $pagination = $jobs->toArray();
        unset($pagination['data']);

        return [$list, $pagination];
    }

    public function serializeJobData($job)
    {
        $job->link = jobDetailsRoute($job);
        $job->preview_link = jobPreviewRoute($job);
        $job->published = jobIsPublished($job);
        unset($job->company);
        return $job;
    }

    public function processTransactions(Request $request): array
    {
        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);
        $gatewayType = $request->get('gatewayType');
        $dates = collect($request->only(['from', 'to']));
        [$from, $to] = $this->prepareFilterDates($filter, $dates);

        $customers = $this->getTransactions($request);
        $counters = $this->getTransactionCounters($from, $to, $gatewayType);
        $packages = DB::table('packages')->select(['id', 'name'])->orderBy('price', 'asc')->get();

        return [
            'currentFilter' => $filter,
            'prevFilter' => $this->getPrevFilter($filter),
            'from' => $from->format(config('easyjob.user.date_format')),
            'to' => $to->format(config('easyjob.user.date_format')),
            'counters' => $counters,
            'customers' => $customers,
            'pagination' => $customers,
            'subscriptionPackages' => $packages
        ];
    }

    public function getTotalTransactionChart(Request $request): array
    {
        $filter = $request->get('filter', AdminDateFilter::THIRTY_DAYS);
        [$startDate, $endDate] = $this->prepareFilterDates($filter, $request);

        $transactionData = [];
        $period = CarbonPeriod::create($startDate, $endDate);
        $allDates = $this->getDates($period);
        [$labels, $datesKey] = $allDates;

        $grouping = $filter == AdminDateFilter::ALL_TIME ? 'YEAR' : (count($period) > 365 ? 'YEAR' : (count($period) > 31 ? 'MONTH' : 'DAY'));
        $dateFormat = $grouping === 'YEAR' ? '%Y' : ($grouping === 'MONTH' ? '%b %Y' : '%b %d');

        $transactionQuery = Transaction::where('price', '>', 0)
            ->whereBetween('created_at', [$startDate, $endDate]);

        if ($request->gatewayType == PaymentMethod::STRIPE) {
            $transactionQuery->whereNotNull('stripe');
        }

        if ($request->gatewayType == PaymentMethod::PAYPAL) {
            $transactionQuery->whereNotNull('paypal');
        }

        $totalAmount = $transactionQuery->sum('total');

        if ($filter != AdminDateFilter::ALL_TIME) {
            $transactions = $transactionQuery->select(
                DB::raw("SUM(total) as totalTransactions"),
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as period")
            )
                ->groupBy('period')
                ->orderBy('period', 'asc')
                ->get()
                ->toArray();

            foreach ($transactions as $transaction) {
                $transactionData[data_get($transaction, 'period')] = data_get($transaction, 'totalTransactions');
            }

            $totalTransactionData = array_values(array_merge($datesKey, $transactionData));
        }

        if ($filter == AdminDateFilter::ALL_TIME) {
            $transactions = $transactionQuery->select(
                DB::raw("YEAR(created_at) as year"),
                DB::raw("SUM(total) as totalTransactions")
            )
                ->groupBy('year')
                ->orderBy('year', 'asc')
                ->get()
                ->toArray();

            $years = range($startDate->year, $endDate->year);

            foreach ($years as $year) {
                $transactionData[$year] = 0;
            }

            foreach ($transactions as $transaction) {
                $transactionData[$transaction['year']] = $transaction['totalTransactions'];
            }

            $labels = $years;
            $totalTransactionData = array_values(array_intersect_key($transactionData, array_flip($years)));

        }

        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        if ($today->isSameDay($startDate) || $yesterday->isSameDay($startDate) || $startDate->diffInDays($endDate) === 0) {
            [$totalTransactionData, $labels] = $this->getTodayYesterdayTransactions($startDate->toDateString());
        }

        return [
            'series' => [['name' => 'Transactions', 'data' => $totalTransactionData]],
            'labels' => $labels,
            'total_amount' => $totalAmount
        ];
    }

    public function getTodayYesterdayTransactions($date): array
    {
        $dates = [];
        $datesKey = [];
        $transactionData = [];

        $transactions = Transaction::where('price', '>', 0)
            ->whereDate('created_at', $date)
            ->select(
                DB::raw("SUM(total) as totalTransactions"),
                DB::raw("DATE_FORMAT(created_at, '%H:00') hour")
            )
            ->groupBy('hour')
            ->get()
            ->toArray();

        foreach (range(0, 23) as $i) {
            $currentHour = sprintf('%02d:00', $i);
            $dates[] = $currentHour;
            $datesKey[$currentHour] = 0;
        }

        foreach ($transactions as $transaction) {
            $transactionData[data_get($transaction, 'hour')] = data_get($transaction, 'totalTransactions');
        }

        $totalTransactionsData = array_values(array_merge($datesKey, $transactionData));

        return [$totalTransactionsData, $dates];
    }

    public function getDates($period): array
    {
        $dates = [];
        $datesKey = [];
        $dateFormat = count($period) > 31 ? 'M Y' : 'M d';

        foreach ($period as $value) {
            $formattedDate = $value->format($dateFormat);
            $dates[] = $formattedDate;
            $datesKey[$formattedDate] = 0;
        }

        return [$dates, $datesKey];
    }

    public function prepareFilterDates($filter, $date): array
    {
        if ($filter == AdminDateFilter::CUSTOM) {
            $startDate = Carbon::parse($date->get('from'))->startOfDay();
            $endDate   = Carbon::parse($date->get('to'))->endOfDay();
        } else {
            [$startDate, $endDate] = $this->getFilterDates($filter);
        }

        return [$startDate, $endDate];
    }
}
