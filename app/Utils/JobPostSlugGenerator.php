<?php

namespace App\Utils;

use App\Models\JobPost;
use Hashids\Hashids;

class JobPostSlugGenerator
{
    private Hashids $client;

    public function __construct()
    {
        $this->createHashIdObject();
    }

    public function createHashIdObject($salt = null)
    {
        $length = config('easyjob.job_post.id_length');
        $latterCase = 'abcdefghijklmnopqrstuvwxyz';

        if (blank($salt)) {
            $salt = config('easyjob.job_post.id_salt');
        }

        $this->client = new Hashids(
            $salt,
            $length,
            $latterCase
        );

        return $this;
    }

    public function encode(JobPost $jobPost)
    {
        return $this->client->encode($jobPost->company_id, $jobPost->id);
    }

    public function encodeAgain(JobPost $jobPost)
    {
        $this->createHashIdObject(uniqid());
        return $this->encode($jobPost);
    }

    public function decode($encodedId)
    {
        [$company] = $this->client->decode($encodedId);
        return compact('company');
    }

    public function generateRandomId(JobPost $jobPost)
    {
        $id = $this->encode($jobPost);

        if (!JobPost::findBySlug($id)) {
            return $id;
        }

        $id = $this->encodeAgain($jobPost);

        if (!JobPost::findBySlug($id)) {
            return $id;
        }

        return $this->generateRandomId($jobPost);
    }
}
