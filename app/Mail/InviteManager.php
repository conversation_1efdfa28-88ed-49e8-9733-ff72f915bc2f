<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InviteManager extends FlyMailable implements ShouldQueue
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    private $user;
    private $key;
    public $company;
    private $role;
    private $existingUser;
    private $jobPost;

    public function __construct(User $user, $key, $company, $role, $existingUser, $jobPost)
    {
        $this->user = $user;
        $this->key = $key;
        $this->company = $company;
        $this->role = $role;
        $this->existingUser = $existingUser;
        $this->jobPost = $jobPost;

        $lang = data_get($company, 'local', 'en');
        $subject = __("Invitation for", [], $lang);

        $this->subject = $subject.' ' . $this->company->name;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('email.company.invite-manager')->with([
            'subject' => $this->subject,
            'user' => $this->user,
            'key' => $this->key,
            'existingUser' => $this->existingUser,
            'company' => $this->company,
            'role'  => $this->role,
            'jobPost'  => $this->jobPost,
        ]);
    }
}
