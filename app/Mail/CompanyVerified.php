<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CompanyVerified extends FlyMailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;
    use InteractsWithQueue;

    /**
     * Create a new message instance.
     *
     * @return void
     */

    public $company;

    public function __construct($company)
    {
        $this->company = $company;
        $lang = data_get($company, 'local', 'en');
        $this->subject(__('company.verified.mail.subject', ['COMPANY_NAME' => $company->name], $lang));
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('email.company.company-verified')->with([
            'subject' => $this->subject,
            'company' => $this->company,
        ]);
    }
}
