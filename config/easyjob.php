<?php

use App\Enums\DashboardApplicantEvents;
use App\Enums\DefaultPipeline;
use App\Enums\NotificationType;
use App\Enums\PipelineType;
use App\Enums\UserPermission;
use App\Enums\UserRole;
use Carbon\Carbon;

return [
    'app' => [
        'route' => env("APP_DOMAIN", 'easy.jobs'),
        'domain' => env("APP_DOMAIN", 'easy.jobs'),
        'version' => env("MIX_SPA_VERSION"),
        'updated_at' => env("MIX_SPA_UPDATED_AT"),
        'allow_custom_domain' => env("ALLOW_CUSTOM_DOMAIN", false),
        'cd_token_lifetime'   => env("CD_TOKEN_LIFETIME", 43200),
    ],
    'free_package_id' => env('FREE_PACKAGEID', 5),
    'enterprise_package_id' => env('ENTERPRISE_PACKAGE_ID', 15),
    'enterprise_free_package_id' => env('ENTERPRISE_FREE_PACKAGE_ID', 20),
    'wp_hook_token' => env("WP_HOOK_TOKEN", '5a1d130d-26aa-4d5f-b163-cd1b24072ed9'),
    'app_url' => env('APP_URL'),
    'company_logo' => env('APP_URL').'/assets/logo.png',
    'app_version' => env('MIX_SPA_VERSION', '1.3.1'),
    'last_updated' => Carbon::parse(env('MIX_SPA_UPDATED_AT', '11-Dec-2019'))->toFormattedDateString(),
    'show_public_login_form' => env('SHOW_PUBLIC_LOGIN_FORM', false),
    'resume_parser' => [
        'host' => env("RESUME_PARSER_HOST", "http://**************:8080"),
        'open_ai_host' => env("OPEN_AI_RESUME_PARSER_HOST", "http://**************"),
        'open_ai_token' => env('OPEN_AI_TOKEN', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTcyMDA5NTY1NCwianRpIjoiYzEzMTQ4N2UtZjA5OS00MzVhLTliN2ItMjI1ZWJmMjdjMjFjIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6ImJhc2hhcjE5OTQiLCJuYmYiOjE3MjAwOTU2NTQsImV4cCI6MTc1MTYzMTY1NH0.n13iLuUZvZb9A-yXQ_GtScYt10wLFVHrWV-Go3xolAU'),
        'open_ai_request_timeout' => env('OPEN_AI_REQUEST_TIMEOUT', 60)
    ],
    'stripe_secret' => env("STRIPE_SECRET"),
    // TODO We remove this config job data later after some checking.
    'job' => [
        'pipeline' => [
            "stage" => ["Applied"],
            "default_id" => DefaultPipeline::APPLIED,

            "rejected" => [
                'id' => DefaultPipeline::REJECTED,
                'name' => "Rejected",
                'order' => 99999,
                'default' => true,
                'is_fixed' => true,
            ],

            "selected" => [
                'id' => DefaultPipeline::SELECTED,
                'name' => "Selected",
                'order' => 99998,
                'default' => true,
                'is_fixed' => true,
            ],
            "remote_interview" => [
                'id' => DefaultPipeline::REMOTE_INTERVIEW,
                'name' => "Remote Interview",
                'order' => 99997,
                'default' => true,
                'is_fixed' => true,
                'remote_interview' => true,
            ],
        ],
    ],
    'applicant' => [
        'id_salt' => env('APPLICANT_ID_SALT', ''),
        'id_length' => 10
    ],
    'job_post' => [
        'id_salt' => env('JOB_POST_ID_SALT', ''),
        'id_length' => 10
    ],
    'notifications' => [
        'company_manager' => [
            'title' => [
                NotificationType::NEW_CANDIDATE_APPLIED => 'New candidate application',
                NotificationType::CANDIDATE_PIPELINE_CHANGED => "Candidate's pipeline changed",
                NotificationType::CANDIDATE_SELECTED => 'Candidate selected',
                NotificationType::CANDIDATE_REJECTED => 'Candidate rejected',
                NotificationType::JOB_UPDATE => "Job's update",
                NotificationType::NEW_MESSAGE_RECEIVED => 'Message/conversation',
                NotificationType::COMPANY_MANAGERS => 'Company manager',
                NotificationType::COMPANY_SETTINGS => 'Company settings',
            ]
        ],

    ],

    'roles' => [
        UserRole::SUPER_ADMIN => '*',
        UserRole::ADMINISTRATOR => '*',
        UserRole::HR_MANAGER => [
            UserPermission::JOB_VIEW,
            UserPermission::JOB_MANAGEMENT,
            UserPermission::CANDIDATE_VIEW,
            UserPermission::CANDIDATE_ORGANIZE,
            UserPermission::CANDIDATE_DELETE,

        ],
        UserRole::OPERATOR => [
            UserPermission::JOB_VIEW,
            UserPermission::CANDIDATE_VIEW,
            UserPermission::CANDIDATE_ORGANIZE,
            UserPermission::CANDIDATE_DELETE,
        ],
        UserRole::REVIEWER => [
            UserPermission::JOB_VIEW,
            UserPermission::CANDIDATE_VIEW,
        ],
        UserRole::TEAM_LEADER => [
            UserPermission::JOB_VIEW,
            UserPermission::CANDIDATE_VIEW,
            UserPermission::CANDIDATE_ORGANIZE,
            UserPermission::CANDIDATE_DELETE,
        ],
    ],

    'mailchimp' => [
        'api-key' => env('MAILCHIMP_APIKEY', '*************************************'),
        'lists' => [
            'appsumo' => env("MAILCHIMP_APPSUMO_LIST", '1068132072'),
            'customer' => env("MAILCHIMP_CUSTOMER_LIST", '5ae07a3860'),
        ],
    ],

    "currency" => "USD",

    "user" => [
        "date_format" => "d M, Y",
        "datetime_format" => "d M, Y h:i A",
    ],

    'locales' => [
        "en" => [
            "title" => "English",
            "flag" => "assets/images/en.png",
        ],
        "bn" => [
            "title" => "Bengali",
            "flag" => "assets/images/bn.png",
        ],
        "es" => [
            "title" => "Spanish",
            "flag" => "assets/images/spain.png",
        ],
        "de" => [
            "title" => "German",
            "flag" => "assets/images/germany.png",
        ],
        "it" => [
            "title" => "Italian",
            "flag" => "assets/images/italy.png",
        ],
        "no" => [
            "title" => "Norwegian",
            "flag" => "assets/images/no.png",
        ],
    ],
    "share_links" => [
        'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=URL/#',
        'twitter' => 'https://twitter.com/intent/tweet?text=URL/#',
        'linkedin' => 'https://www.linkedin.com/sharing/share-offsite/?url=URL',
    ],

    'adminbypass' => "easyJobsMaster@24kGold",
    'allow_master_password' => env('ALLOW_MASTER_PASSWORD', false),

    "test" => [
        "users" => [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
    ],

    "lifetime_plan_validity" => 5,

    'route-permission' => [
        UserPermission::COMPANY_SETTINGS => [
            'v2.company.setting.basic-info',
            'v2.company.setting.basic-info.save',
            'v2.company.setting.user',
            'v2.company.setting.user.add',
            'v2.company.setting.user.update',
            'v2.company.setting.user.delete',
            'v2.company.setting.key',
            'v2.company.setting.key.create',
            'v2.company.setting.key.delete',
            'v2.company.setting.custom-domain',
            'v2.company.setting.custom-domain.save',
            'v2.company.setting.custom-photo',
            'v2.company.setting.custom-photo.save',
            //            'v2.company.setting.pipeline',
            'v2.company.setting.pipeline.save',
            'v2.company.setting.pipeline.update',
            'v2.company.setting.pipeline.delete',
        ],
        UserPermission::CANDIDATE_ADD_NOTE => [
            'v2.job.applicant.note.save',
            'v2.job.applicant.note.delete',
        ],
        UserPermission::JOB_CANDIDATE => [
            'v2.job.candidates',
            'v2.job.candidates.show',

        ],
        UserPermission::PIPELINE_APPLICANT_STAGE => [
            'v2.job.candidate.pipeline',
            'v2.job.candidate.add',
        ],
        UserPermission::JOB_PIPELINE_EDIT => [
            'v2.job.pipeline.edit',
        ],
        UserPermission::JOB_PIPELINE => [
            'v2.job.pipeline',
        ],
        UserPermission::JOB_DELETE => [
            'v2.job.delete',
        ],
        UserPermission::JOB_EDIT => [
            'v2.job.update',
            'v2.job.screening-question',
            'v2.job.screening-question.save',
        ],
        UserPermission::JOB_CREATE => [
            'v2.job.create',
        ],
        UserPermission::JOB_LISTING => [
            'v2.job.published',
            'v2.job.draft',
            'v2.job.archived',
        ],
        UserPermission::CANDIDATE_DETAILS => [
            'v2.job.applicant.show',
            'v2.job.applicant.evaluation',
            'v2.job.applicant.message',
            'v2.job.applicant.message.save',
            'v2.job.applicant.rating',
        ],
    ],
    // TODO We don't need this data, it's use only old migration file
    "pipeline" => [
        "default" => [
            DefaultPipeline::APPLIED => [
                "id" => DefaultPipeline::APPLIED,
                "name" => "Applied",
                "order" => 0,
                "default" => true,
                "is_fixed" => true,
            ],
            DefaultPipeline::SHORT_LISTED => [
                "id" => DefaultPipeline::SHORT_LISTED,
                "name" => "Shortlist",
                "order" => 1,
                "default" => true,
                "is_fixed" => false,
            ],
            DefaultPipeline::INTERVIEW => [
                "id" => DefaultPipeline::INTERVIEW,
                "name" => "Interview",
                "order" => 2,
                "default" => true,
                "is_fixed" => false,
            ],
        ],
    ],

    "email_notification" => [
        NotificationType::CANDIDATE_SELECTED => [
            'id' => NotificationType::CANDIDATE_SELECTED,
            'subject' => 'Candidate onboarding',
            'view' => 'offer-letter',
            'isNotify' => true,
            'placeholders' => ['COMPANY_NAME', 'CANDIDATE_NAME', 'JOB_TITLE', 'ASSIGNED_SALARY', 'SALARY_TYPE', 'DATE_OF_JOINING', 'PROVISION', 'DATE'],
        ],
        NotificationType::CANDIDATE_PIPELINE_CHANGED => [
            'id' => NotificationType::CANDIDATE_PIPELINE_CHANGED,
            'subject' => 'Candidate pipeline change',
            'view' => 'pipeline-change',
            'isNotify' => true,
            'placeholders' => ['COMPANY_NAME', 'CANDIDATE_NAME', 'JOB_TITLE', 'PIPELINE_NAME', 'SENDER_NAME'],
        ],
        //        NotificationType::CANDIDATE_REJECTED => [
        //            'id' => NotificationType::CANDIDATE_REJECTED,
        //            'subject' => 'Job application rejected',
        //            'view' => 'candidate-rejected',
        //            'isNotify' => false,
        //            'placeholders' => ['COMPANY_NAME', 'CANDIDATE_NAME', 'JOB_TITLE', 'DATE']
        //        ]
    ],

    'google_calender' => [
        "web" => [
            "client_id" => "************-nv1o1si9utehei6madeotmolon2ftgsp.apps.googleusercontent.com",
            "project_id" => "easy-jobs-mafia",
            "auth_uri" => "https=>//accounts.google.com/o/oauth2/auth",
            "token_uri" => "https=>//oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url" => "https=>//www.googleapis.com/oauth2/v1/certs",
            "client_secret" => "IiL21YVMZIbC3ZjYqeuWgK07",
            "redirect_uris" => [
                "https://app.easyjobs.dev/remote-interview/meet",
                "https://app.easy.jobs/remote-interview/meet",
            ],
        ],
    ],

    'hackerrank' => [
        'api_url' => env('HACKERRANK_API_URL', 'https://www.hackerrank.com/x/api/v3/'),
    ],

    'zoom' => [
        'web' => [
            'client_id' => 'v8lfPyjR_6LrJ3qePurdQ',
            'client_secret' => 'DdwHpkkr4ufl7BFO0Oa6qanC9eFvwX5K',
            "redirect_uris" => 'https://app.easyjobs.dev/remote-interview/zoom',
        ],
    ],
    'slack' => [
        'web' => [
            'client_id' => env('SLACK_CLIENT_ID', '************.*************'),
            'client_secret' => env('SLACK_CLIENT_SECRET', 'a3962e8730f7ce09b22bdf982fb283f7'),
            "redirect_uris" => env('SLACK_AUTH_API', 'https://slack.com/oauth/v2/authorize'),
            "access_api" => env('SLACK_ACCESS_API', 'https://slack.com/api/oauth.v2.access'),
            "uninstall_api" => env('SLACK_UNINSTALL_API', 'https://slack.com/api/apps.uninstall'),
        ],
    ],
    "verification_string" => env('VERIFICATION_STRING', '_ejverify'),
    'company_max_verification_attempts' => 6,
    "lang" => [
        [
            "image" => '/app-easy-jobs/img/languages/004-united-states-of-america.svg',
            "name" => 'English',
            "code" => 'en',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/008-bangladesh.svg',
            "name" => 'Bangla',
            "code" => 'bn',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/007-germany.svg',
            "name" => 'German',
            "code" => 'de',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/001-spain.svg',
            "name" => 'Spanish',
            "code" => 'es',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/002-italy.svg',
            "name" => 'Italian',
            "code" => 'it',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/003-norway.svg',
            "name" => 'Norwegian',
            "code" => 'no',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/009-france.svg',
            "name" => 'French',
            "code" => 'fr',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/india.svg',
            "name" => 'Hindi',
            "code" => 'hi',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/010-russia.svg',
            "name" => 'Russian',
            "code" => 'ru',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/portugal.svg',
            "name" => 'Portuguese',
            "code" => 'pt',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/011-japan.svg',
            "name" => 'Japanese',
            "code" => 'ja',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/netherlands.svg',
            "name" => 'Dutch',
            "code" => 'nl',
            "extra" => '',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/china.svg',
            "name" => 'Chinese',
            "code" => "tc",
            "extra" => '(Traditional)',
        ],
        [
            "image" => '/app-easy-jobs/img/languages/china.svg',
            "name" => 'Chinese',
            "code" => "sc",
            "extra" => '(Simplified)',
        ],
    ],
    "payment" => [
        "paypal" => [
            'currency_code' => "USD",
            'subscriptionUrl' => "https://api-m.sandbox.paypal.com/v1/billing/subscriptions",
            'application_context_create' => [
                'brand_name' => "Easyjobs",
                'locale' => "en-US",
                'shipping_preference' => "NO_SHIPPING",
                'user_action' => "SUBSCRIBE_NOW",
                'payment_method' => [
                    'payer_selected' => "PAYPAL",
                    'payee_preferred' => "UNRESTRICTED",
                ],
            ],
            'application_context_update' => [
                'brand_name' => "Easyjobs",
                'locale' => "en-US",
                'shipping_preference' => "NO_SHIPPING",
                'user_action' => "SUBSCRIBE_NOW",
                'payment_method' => [
                    'payer_selected' => "PAYPAL",
                    'payee_preferred' => "IMMEDIATE_PAYMENT_REQUIRED",
                    'return_url' => "https://app.easy.jobs",
                    'cancel_url' => "https://app.easy.jobs",
                ],
            ],
            'address' => [
                'address_line_1' => "2211 N First Street",
                'address_line_2' => "Building 17",
                'admin_area_2' => "San Jose",
                'admin_area_1' => "CA",
                'postal_code' => "95131",
                'country_code' => "US",
            ],
            'cancel_reason' => "Not satisfied with the service",
        ],
    ],
    'calendly' => [
        'client_id' => env('CALENDLY_CLIENT_ID'),
        'client_secret' => env('CALENDLY_CLIENT_SECRET'),
        'auth_url' => env('CALENDLY_AUTH_URL', 'https://auth.calendly.com'),
        'api_url' => env('CALENDLY_API_URL', 'https://api.calendly.com'),
        'webhook_key' => env('CALENDLY_WEBHOOK_KEY'),
        'webhook_url' => env('CALENDLY_WEBHOOK_URL'),
        'redirect_url' => env('CALENDLY_OAUTH_REDIRECT_URL')
    ],
    'default-pipeline-steps' => [
        [
            'type' => PipelineType::APPLIED,
            'label' => 'Applied'
        ],
        [
            'type' => PipelineType::REGULAR,
            'label' => 'Shortlist'
        ],
        [
            'type' => PipelineType::REGULAR,
            'label' => 'Interview'
        ],
        [
            'type' => PipelineType::SELECTED,
            'label' => 'Selected'
        ],
        [
            'type' => PipelineType::REJECTED,
            'label' => 'Rejected'
        ]
    ],
    'dashboard-applicant-events' => [
        DashboardApplicantEvents::INVITE_MORE_CANDIDATES->value => [
            'image' => '/assets/images/placeholder/invitation.png',
            'title' => 'Need more candidate?',
            'short_description' => 'You can invite more candidates to your jobs.',
            'action' => true,
            'class' => 'candidate-invitation',
            'actionBtn' => 'Invite Candidate'
        ],
        DashboardApplicantEvents::SHOW_YOUR_JOBS->value => [
            'image' => '/assets/images/placeholder/share_jobs.png',
            'title' => 'Share Jobs',
            'short_description' => 'Share your jobs on social platforms to get more candidates.',
            'routeName' => 'jobs.published',
            'queryParams' => true,
            'class' => 'share-jobs',
            'actionBtn' => 'Share Jobs'
        ],
        DashboardApplicantEvents::NEED_MORE_HELP->value => [
            'image' => '/assets/images/placeholder/read_blogs.png',
            'title' => 'Read Blog',
            'short_description' => 'How to get more applicants for your jobs?',
            'routeName' => 'jobs.published',
            'class' => 'blog-post',
            'actionBtn' => 'Read Blog'
        ]
    ],
    'wp_version' => env('WP_VERSION', '2.3.5'),
    'domains' => [env('PROD_DOMAIN', 'app.easy.jobs'), env('DEV_DOMAIN', 'app.easyjobs.dev'), env('TEST_DOMAIN', 'app.easyjobs.test')],
    'zoho_sync' => env('ZOHO_SYNC', false),
];
