!function(n){"use strict";function t(){}t.prototype.initTooltipPlugin=function(){n.fn.tooltip&&n('[data-toggle="tooltip"]').tooltip()},t.prototype.initPopoverPlugin=function(){n.fn.popover&&n('[data-toggle="popover"]').popover()},t.prototype.initSlimScrollPlugin=function(){n.fn.slimScroll&&n(".slimscroll").slimScroll({height:"auto",position:"right",size:"8px",touchScrollStep:20,color:"#fff"})},t.prototype.initFormValidation=function(){n(".needs-validation").on("submit",function(t){return n(this).addClass("was-validated"),!1!==n(this)[0].checkValidity()||(t.preventDefault(),t.stopPropagation(),!1)})},t.prototype.initCustomModalPlugin=function(){n('[data-plugin="custommodal"]').on("click",function(t){t.preventDefault(),new Custombox.modal({content:{target:n(this).attr("href"),effect:n(this).attr("data-animation")},overlay:{color:n(this).attr("data-overlayColor")}}).open()})},t.prototype.initCounterUp=function(){n(this).attr("data-delay")&&n(this).attr("data-delay"),n(this).attr("data-time")&&n(this).attr("data-time");n('[data-plugin="counterup"]').each(function(t,i){n(this).counterUp({delay:100,time:1200})})},t.prototype.initPeityCharts=function(){n('[data-plugin="peity-pie"]').each(function(t,i){var o=n(this).attr("data-colors")?n(this).attr("data-colors").split(","):[],e=n(this).attr("data-width")?n(this).attr("data-width"):20,a=n(this).attr("data-height")?n(this).attr("data-height"):20;n(this).peity("pie",{fill:o,width:e,height:a})}),n('[data-plugin="peity-donut"]').each(function(t,i){var o=n(this).attr("data-colors")?n(this).attr("data-colors").split(","):[],e=n(this).attr("data-width")?n(this).attr("data-width"):20,a=n(this).attr("data-height")?n(this).attr("data-height"):20;n(this).peity("donut",{fill:o,width:e,height:a})}),n('[data-plugin="peity-donut-alt"]').each(function(t,i){n(this).peity("donut")}),n('[data-plugin="peity-line"]').each(function(t,i){n(this).peity("line",n(this).data())}),n('[data-plugin="peity-bar"]').each(function(t,i){var o=n(this).attr("data-colors")?n(this).attr("data-colors").split(","):[],e=n(this).attr("data-width")?n(this).attr("data-width"):20,a=n(this).attr("data-height")?n(this).attr("data-height"):20;n(this).peity("bar",{fill:o,width:e,height:a})})},t.prototype.initKnob=function(){n('[data-plugin="knob"]').each(function(t,i){n(this).knob()})},t.prototype.initTippyTooltips=function(){0<n('[data-plugin="tippy"]').length&&tippy('[data-plugin="tippy"]')},t.prototype.init=function(){this.initTooltipPlugin(),this.initPopoverPlugin(),this.initSlimScrollPlugin(),this.initFormValidation(),this.initCustomModalPlugin(),this.initCounterUp(),this.initPeityCharts(),this.initKnob(),this.initTippyTooltips()},n.Components=new t,n.Components.Constructor=t}(window.jQuery),function(a){"use strict";function t(){this.$body=a("body"),this.$portletIdentifier=".card",this.$portletCloser='.card a[data-toggle="remove"]',this.$portletRefresher='.card a[data-toggle="reload"]'}t.prototype.init=function(){var e=this;a(document).on("click",this.$portletCloser,function(t){t.preventDefault();var i=a(this).closest(e.$portletIdentifier),o=i.parent();i.remove(),0==o.children().length&&o.remove()}),a(document).on("click",this.$portletRefresher,function(t){t.preventDefault();var i=a(this).closest(e.$portletIdentifier);i.append('<div class="card-disabled"><div class="card-portlets-loader"></div></div>');var o=i.find(".card-disabled");setTimeout(function(){o.fadeOut("fast",function(){o.remove()})},500+5*Math.random()*300)})},a.Portlet=new t,a.Portlet.Constructor=t}(window.jQuery),function(o){"use strict";function t(){this.$body=o("body"),this.$window=o(window)}t.prototype._resetSidebarScroll=function(){o(".slimscroll-menu").slimscroll({height:"auto",position:"right",size:"8px",color:"#fff",wheelStep:5,touchScrollStep:20})},t.prototype.initMenu=function(){var i=this;o(".button-menu-mobile").on("click",function(t){t.preventDefault(),i.$body.toggleClass("sidebar-enable"),768<=i.$window.width()?i.$body.toggleClass("enlarged"):i.$body.removeClass("enlarged"),i._resetSidebarScroll()}),o("#side-menu").metisMenu(),i._resetSidebarScroll(),o("#side-menu a").each(function(){var t=window.location.href.split(/[?#]/)[0];this.href==t&&(o(this).addClass("active"),o(this).parent().addClass("active"),o(this).parent().parent().addClass("in"),o(this).parent().parent().prev().addClass("active"),o(this).parent().parent().parent().addClass("active"),o(this).parent().parent().parent().parent().addClass("in"),o(this).parent().parent().parent().parent().parent().addClass("active"))}),o(".navbar-toggle").on("click",function(t){o(this).toggleClass("open"),o("#navigation").slideToggle(400)}),o(window).on("load",function(){o("#status").fadeOut(),o("#preloader").delay(350).fadeOut("slow")})},t.prototype.initLayout=function(){768<=this.$window.width()&&this.$window.width()<=1028?this.$body.addClass("enlarged"):1!=this.$body.data("keep-enlarged")&&this.$body.removeClass("enlarged")},t.prototype.init=function(){var i=this;this.initLayout(),o.Portlet.init(),this.initMenu(),o.Components.init(),i.$window.on("resize",function(t){t.preventDefault(),i.initLayout(),i._resetSidebarScroll()})},o.App=new t,o.App.Constructor=t}(window.jQuery),function(){"use strict";window.jQuery.App.init()}(),Waves.init();
//# sourceMappingURL=app.min.js.map
