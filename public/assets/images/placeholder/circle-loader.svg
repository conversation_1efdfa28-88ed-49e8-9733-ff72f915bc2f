<?xml version="1.0" encoding="UTF-8"?>
<svg  aria-labelledby="loading-aria" preserveAspectRatio="none" role="img" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" clip-path="url(#b)" fill="url(#a)"/>
    <defs>
        <clipPath id="b">
            <rect x="0" y="0" width="100" height="100" rx="0" ry="0"/>
        </clipPath>
        <linearGradient id="a">
            <stop stop-color="#ccd8ff" offset="-1.1594">
                <animate attributeName="offset" dur="2s" keyTimes="0; 0.25; 1" repeatCount="indefinite" values="-2; -2; 1"/>
            </stop>
            <stop stop-color="#e7ecff" offset="-.15941">
                <animate attributeName="offset" dur="2s" keyTimes="0; 0.25; 1" repeatCount="indefinite" values="-1; -1; 2"/>
            </stop>
            <stop stop-color="#ccd8ff" offset=".84059">
                <animate attributeName="offset" dur="2s" keyTimes="0; 0.25; 1" repeatCount="indefinite" values="0; 0; 3"/>
            </stop>
        </linearGradient>
    </defs>
</svg>
