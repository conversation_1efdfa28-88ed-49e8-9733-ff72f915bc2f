$(document).ready(function() {
    $('.office__gallery__slider').owlCarousel({
        center: true,
        // items: 4,
        autoplay: true,
        loop: true,
        margin: 30,
        dots: false,
        responsive:{
            0: {
                items: 1.8,
            },
            575: {
                items: 2,
            },
            767: {
                items: 2.5
            },
            991: {
                items: 4,
            }
        }
    });
})

let url = new URL(window.location.href);
if (url.searchParams.get('title') || url.searchParams.get('location') || url.searchParams.get('category')) {
    let openJobPosition = document.getElementById("open_job_position");
    openJobPosition.scrollIntoView();
}

window.onload = (event) => {
    $('.office__gallery__slider .item').removeClass('skeleton')
    $('.office__gallery__slider .item img').css( "opacity", "1" );
}
