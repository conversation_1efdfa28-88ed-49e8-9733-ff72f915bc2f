let AskConfirm = (title, msg, callback) => {
    var button = $('<button>').prop({
        'class': 'btn btn-success',
    }).html("Yes").click(callback);

    let templete = `<div class="modal fade" id="askConfirm" tabindex="-1" role="dialog" aria-labelledby="askConfirmLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <h4 class="mb-3">${title}</h4>
                    <p class="mb-3">${msg}</p>
                    <hr>
                    <div class="">
                        <button type="button" class="btn btn-warning mr-4" data-dismiss="modal">No</button>
                        <span id="tokotokoid"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>`;

    $('#CustomModal').html(templete);
    $('#tokotokoid').html(button);
    $('#askConfirm').modal('show');
};

let HideAskConfirm = () => {
    $('#askConfirm').modal('hide');
};
