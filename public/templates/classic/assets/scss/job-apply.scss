
.button {
	padding: 15px 23px;
    border-radius: 25px;
	font-size: 14px;
	line-height: 1.3;
	font-weight: 500;
	text-align: center;
	border: 1px solid #00CC99;
	color: #00CC99;
	transition: background .3s ease, color .3s ease, border .3s ease, box-shadow .3s ease;
	&:hover {
		color: #fff;
		background: #00CC99;
	}
	&.button__xl {
		padding: 18px 30px;
		font-size: 18px;
	}
	&.button__radius {
		border-radius: 60px;
	}
	&.radius-5 {
		border-radius: 5px
	}
	&.radius-10 {
		border-radius: 10px
	}
	&.radius-15 {
		border-radius: 15px
	}
	&.radius-8 {
		border-radius: 8px
	}
}
.info-button,
.button__success {
    background: #00CC99;
    color: #fff;
}
.semi-button-info,
.hover__highlight {
    background: rgba(0, 204, 153, .1);
    border-color: transparent;
    color: #00CC99;
    &:hover {
        background: #00CC99;
        color: #fff;
    }
}


.fixed__nav {
    top: 0;
    left: 0;
    right: 0;
    position: absolute;
    z-index: 999;
    transition: padding .3s ease, background .3s ease;
    .ej-header {
        transition: padding .3s ease;
    }
    &.fixed {
        background: #fff;
        box-shadow: 0 3px 5px 0 rgba(0, 0, 0, .10);
        .ej-header {
            padding-top: 20px;
            padding-bottom: 20px;
        }
    }
}

.ej-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 50px;
    max-width: 1600px;
    margin: 0 auto;
    @media all and (max-width: 1600px) {
        padding-left: 40px;
        padding-right: 40px;
    }
    @media all and (max-width: 767px) {
        padding: 20px 0;
        padding-left: 30px;
        padding-right: 30px;
    }
    @media all and (max-width: 499px) {
        padding-left: 20px;
        padding-right: 20px;
    }
    .site__logo {
        img {
            max-height: 50px;
            @media all and (max-width: 767px) {
                max-height: 40px;
            }
        }
    }
    .button {
        @media all and (max-width: 767px) {
            padding: 10px 23px;
        }
    }
}
.ej-breadcrumb {
    margin-top: 111px;
    border-bottom: 1px solid #CFDCEB;
    border-top: 1px solid #CFDCEB;
    @media all and (max-width: 767px) {
        margin-top: 80px;
    }
}
.ej-breadcrumb .breadcrumb {
    margin-bottom: 0;
    background: transparent;
    padding: 14.5px 0;
    max-width: 1600px;
    padding-left: 50px;
    padding-right: 50px;
    margin: 0 auto;
    @media all and (max-width: 1600px) {
        padding-left: 40px;
        padding-right: 40px;
    }
    @media all and (max-width: 767px) {
        padding-left: 30px;
        padding-right: 30px;
    }
    @media all and (max-width: 499px) {
        padding-left: 20px;
        padding-right: 20px;
    }
    ul {
        padding: 0;
        margin: 0;
        list-style: none;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        li {
            font-size: 14px;
            color: #6E748A;
            font-weight: 400;
            &:not(:last-child) {
                margin-right: 10px;
            }
            &:last-child {
                span {
                    color: #597dfc;
                }
            }
            &:not(:first-child) {
                &::before {
                    content: '\e903';
                    font-family: 'jobs';
                    font-size: 12px;
                    padding-right: 10px;
                }
            }
            a:hover {
                color: #597dfc;
            }
        }
    }
}

footer {
    border-top: 1px solid #CFDCEB;
} 

.footer__wrap {
    padding: 50px;
    padding: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    max-width: 1600px;
    margin: 0 auto;
    @media all and (max-width: 1600px) {
        padding-left: 40px;
        padding-right: 40px;
    }
    @media all and (max-width: 767px) {
        padding-left: 30px;
        padding-right: 30px;
    }
    @media all and (max-width: 499px) {
        padding-left: 20px;
        padding-right: 20px;
    }
    @media all and (max-width: 1199px) {
        flex-direction: column;
        justify-content: center;
        padding-top: 30px;
        padding-bottom: 30px;
        gap: 10px;
    }
    .copyright {
        color: #1A1751;
        font-size: 16px;
        font-weight: 400;
        flex: 1 1 30%;
        a:hover {
            color: #00CC99;
        }
        @media all and (max-width: 1199px) {
            order: 3;
        }
    }
    .courtesy {
        display: flex;
        align-items: center;
        flex: 0 0 auto;
        @media all and (max-width: 1199px) {
            order: 1;
        }
        span {
            font-size: 16px;
            color: #1A1751;
            margin-right: 10px;
        }
    }
    .footer__nav {
        flex: 1 1 30%;
        @media all and (max-width: 1199px) {
            order: 2;
        }
        ul {
            padding: 0;
            margin: 0;
            list-style: none;
            li {
                font-size: 16px;
                font-weight: 400;
                color: #1A1751;
                &:hover {
                    color: #00CC99;
                }
                &:not(:last-child) {
                    margin-right: 30px;
                }
            }
        }
    }
}

.footer__nav {
    display: flex;
    justify-content: flex-end;
    .language-control {
        display: flex;
        margin-left: auto;
        .dropdown-toggle {
            display: flex;
            align-items: center;
            // background: rgba(89, 125, 252, .1);
            color: #1A1751;
            padding: 5px 45px 5px 15px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 5px;
            position: relative;
            &:after {
                position: absolute;
                content: '\e804';
                font-family: 'eicon';
                top: 50%;
                transform: translateY(-50%);
                right: 15px;
                // background: rgba(89,125,252,0.1);
                color: #1A1751;
                pointer-events: none;
                height: 20px;
                width: 20px;
                font-size: 20px;
                line-height: 20px;
                border: 0;
                text-align: center;
                border-radius: 3px;
            }
            .language-flag {
                width: 20px;
                margin-right: 10px;
                border-radius: 3px;
                overflow: hidden;
                .lang-flag {
                    width: 100%;
                }
            }
        }
        .dropdown-menu {
            li {
                a {
                    display: flex;
                    align-items: center;
                    padding: 5px 20px;
                    font-size: 16px;
                    font-weight: 500;
                    color: #1A1751;
                    .language-flag {
                        width: 20px;
                        margin-right: 10px;
                        border-radius: 3px;
                        overflow: hidden;
                    }
                    &:hover {
                        color: rgb(89, 125, 252);
                    }
                }
            }
        }
    }
}
