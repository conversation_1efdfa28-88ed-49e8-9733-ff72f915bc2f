<!DOCTYPE html>
<html lang="en">
<head>
    <title>easy.jobs - Easy Solution For The Job Recruitment</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Try FREE Easy Solution For The Job Recruitment To Attract, Manage &amp;amp; Hire Right Talent Faster Get Started Why Does Your Company Need easy.jobs? Advanced"/>
    <meta name="robots" content="nofollow,noindex,max-snippet:-1,max-video-preview:-1,max-image-preview:large"/>
    <link rel="canonical" href="https://easy.jobs/" />
    <meta property="og:locale" content="en_US">
    <meta property="og:type" content="website">
    <meta property="og:title" content="easy.jobs">
    <meta property="og:description" content="Try FREE Easy Solution For The Job Recruitment To Attract, Manage &amp;amp; Hire Right Talent Faster Get Started Why Does Your Company Need easy.jobs? Advanced">
    <meta property="og:url" content="https://easy.jobs/">
    <meta property="og:site_name" content="easy.jobs">
    <meta property="og:updated_time" content="2019-11-25T08:11:54+00:00">
    <meta property="og:image" content="https://easy.jobs/wp-content/uploads/2019/11/Tab-Screen3.png">
    <meta property="og:image:secure_url" content="https://easy.jobs/wp-content/uploads/2019/11/Tab-Screen3.png">
    <meta property="og:image:width" content="521">
    <meta property="og:image:height" content="310">
    <meta property="og:image:alt" content="easy.jobs">
    <meta property="og:image:type" content="image/png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="easy.jobs">
    <meta name="twitter:description" content="Try FREE Easy Solution For The Job Recruitment To Attract, Manage &amp;amp; Hire Right Talent Faster Get Started Why Does Your Company Need easy.jobs? Advanced">
    <meta name="twitter:creator" content="@easyjobs_">
    <meta name="twitter:image" content="https://easy.jobs/wp-content/uploads/2019/11/Tab-Screen3.png">

    <meta name="csrf-token" content="5nT6gLzyADgjtD5hrKgqX8nz1hyCfFOClVcC7FMd">

    <!-- App favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="http://app.easyjobs.test/favicon.png">



    <link rel="icon" href="{{asset('favicon.png')}}">
    <link rel="stylesheet" href="{{asset('app-easy-jobs/css/bootstrap/bootstrap.min.css')}}"/>
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap" rel="stylesheet"/>
    <link rel="stylesheet" href="{{asset('app-easy-jobs/css/eicon/style.css')}}"/>
    <link rel="stylesheet" href="{{asset('app-easy-jobs/css/style.min.css')}}"/>


    <!-- Global site tag (gtag.js) - Google Analytics -->
</head>
<body id="app">
<div class="page-body min-vh-100">
    <div class="col-md-4 error-left">
        @if(isEasyJobsDomain())
            <img src="{{asset('app-easy-jobs/img/Group 2.svg')}}" alt="" class="img-fluid mw-100">
        @endif
    </div>
    <div class="col-md-4 error-middle">
        <img src="{{asset('app-easy-jobs/img/Repeat Grid 2.svg')}}" alt="" class="img-fluid m-100">
        <h1>Oops!</h1>
        <p>The page you requested could not be found</p>
        @if(Request::route() && (\Request::route()->getName()=='company.domain.job.open' || \Request::route()->getName()=='company.custom-domain.job.apply'))
            <a href="{{url('/')}}" class="semi-button info-button">Back To Home</a>
        @endif
    </div>
    <div class="col-md-4 error-right">
        @if(isEasyJobsDomain())
            <img src="{{asset('/app-easy-jobs/img/Group 338.svg')}}" alt="" class="img-fluid mw-100">
        @endif
    </div>
</div>

<script src="{{asset('app-easy-jobs/js/custom.js')}}"></script>
</body>
</html>
