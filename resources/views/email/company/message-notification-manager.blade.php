@extends('email.layout')

@section('content')
    <table
            cellpadding="0"
            cellspacing="0"
            width="100%"
            role="presentation"
            style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse: collapse; border-spacing: 0px;"
    >
        <tr style="border-collapse: collapse;">
            <td align="left" style="padding: 0; margin: 0;">
                <p>
                    {{__("Hi")}},
                </p>
                <p>
                    <b>{{data_get($messageData, 'manager.name')}}</b> {{ __("sent you a new message") }} <b>{{data_get($messageData, 'jobApplicant.user.name')}}</b> {{__("who applied for")}} <b>{{data_get($messageData, 'jobApplicant.jobPost.title')}}</b>,<br>

                    <span>"</span>{!! $messageData->message !!} <span>"</span> <br>

                    <div align="center">
                        <a class="button" style="color: #fff" target="_blank" href="{{$appUrl.'/applicant/'.data_get($messageData, 'jobApplicant.generated_id').'?tab=conversation'}}">{{__("Reply")}}</a>
                    </div>
                </p>
                <p>
                    {{__("Kind Regards")}},<br>

                    {{data_get($messageData, 'jobApplicant.company.name')}}

                </p>
            </td>
        </tr>
    </table>
@endsection
