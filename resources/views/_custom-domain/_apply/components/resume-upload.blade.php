@php
    $resumeFileName = data_get($resume,'file_name');
    $resumeFileUrl = !blank($resume) ? $resume->getFullUrl() : null;
    $resumeLinkUrl = oldOrElse('resume_link', $tabData);
@endphp

@push('styles')
    <style>
        .upload-button-area .button-separator-or {
            border-top: 1px solid #d6dffc;
            margin: 15px 15px;
            position: relative;
            max-width: 177px;
        }

        .form-wrapper .attachment-card .CV_preview-group .cv_preview .cv__check {
            display: inline-flex;
            width: auto;
            cursor: inherit;
        }

        .form-wrapper .attachment-card .CV_preview-group .cv_preview .cv__check .cv_preview__item__icon:hover {
            color: #fff !important;
        }

        .form-wrapper .attachment-card .CV_preview-group .cv_preview {
            justify-content: flex-start;
        }
        .upload-button-area .button-separator-or:after {
            content: "OR";
            font-size: 14px;
            color: #7d8091;
            font-weight: 500;
            top: calc(50% - 12.5px);
            left: calc(50% - 12.5px);
            position: absolute;
            height: 25px;
            width: 25px;
            background: #f5f7fd;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .error.text-danger {
            font-size: 14px;
        }

        @media (max-width: 1599px) {
            .upload-button-area .candidate-resume-button label,
            .upload-button-area .candidate-resume-button .button {
                width: 206px !important;
                text-align: center;
                padding: 15px 26px !important;
                font-size: 14px !important;
            }
            .form-wrapper .attachment-card .resume__block .remove-button {
                top: 7px!important;
            }
        }

        #resume_link {
            padding-right: 50px;
        }

    </style>
@endpush

<div class="col-md-8">
    <div class="attachment-card">
        <h4>{{ __('Provide Resume') }}</h4>
        <div class="attachment-notes">
            <ul>
                <li class="attachment-note">{{__('You can upload only PDF of size upto 5MB.')}}</li>
                <li class="attachment-note">{{__("Or you can provide resume link.")}}</li>
            </ul>
        </div>
        <div class="mb-4">

            <div class="upload-button-area" id="resume_button_area">
                <div class="resume-error-block mb-1"></div>
                <div>
                    <div class="file-browse-button candidate-resume-button" id="file-browse-button">
                        <label for="file_attach">{{__("Upload New Resume")}}</label>
                        <input id="file_attach" class="uploader__input" type="file" accept=".pdf" name="resume">
                    </div>
                    <div class="button-separator-or"></div>
                    <div class="submit-resume-link-button candidate-resume-button">
                        <a href="javascript:void(0)" class="button semi-button-info resume__button" id="resume_link_button">{{ __('Submit resume link') }}</a>
                    </div>
                </div>
            </div>

            <div class="upload-preview-area" id="upload-preview-area">
                <div class="CV_preview-group" id="resume_attach_block">
                    <div class="cv_preview">
                        <label class="cv__check">
                            <a target="_blank" href="{{ $resumeFileUrl }}" id="resumeFileUrl" class="cv_preview__item__icon"><i class="eicon e-pdf"></i></a>
                            <div class="cv_preview__item__name" id="show_feed_back">
                                {{ substr($resumeFileName, -10) }}
                            </div>
                            <a href="javascript:void(0)" class="remove__resume" id="remove__resume_attach"><i class="eicon e-close"></i></a>
                        </label>
                    </div>
                </div>
                <div class="resume__block" id="resume_link_block">
                    <input type="url" id="resume_link" name="resume_link" value="{{ $resumeLinkUrl }}" placeholder="{{__('Resume link')}}">
                    <a href="javascript:void(0)" id="resume_link_close" class="remove-button"><i class="eicon e-close"></i></a>
                    <div class="resume-link-error-block mb-1"></div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        $(function () {
            let hasResume = false;

            @if(!blank($resume))
                hasResume = true;
            @endif

            @if(!blank($resumeFileName) || !blank($resumeLinkUrl))
                $('#resume_button_area').hide();
            @endif

            @if(blank($resumeFileName))
                $('#resume_attach_block').hide();
            @endif

            @if(blank($resumeLinkUrl))
                $('#resume_link_block').hide();
            @endif

            @if(!blank($resumeFileName))
                $('#resume_link_block').hide();
            @elseif(!blank($resumeLinkUrl))
                $('#resume_attach_block').hide();
            @endif

            $('#file_attach').unbind().change(function (e) {
                e.preventDefault();
                const file = $(this)[0].files[0];
                if (file) {
                    const fileSize = file.size / 1024;
                    if (fileSize > 5120) {
                        $('.resume-error-block').html(`<div class="error text-danger">File size should be under 5 mb!</div>`);
                        $(this).val('');
                        return false;
                    }

                    let allowFileExtension = ['pdf'];
                    let extension = file.name.split('.').pop();

                    if (!allowFileExtension.includes(extension)) {
                        $('.resume-error-block').html(`<div class="error text-danger">Please upload a valid file.</div>`);
                        $(this).val('');
                        return false;
                    }

                    const requestData = new FormData();
                    requestData.append('resume', file);
                    requestData.append('candidate_id', "{{ $user->id }}");

                    $.ajax({
                        url: "{{ route('save.candidate.resume')}}",
                        type: 'POST',
                        data: requestData,
                        contentType: false,
                        processData: false,
                        enctype: 'multipart/form-data',
                        success: (res) => {
                            if (res.success) {
                                hasResume = true;

                                $('#resume_button_area').hide();
                                $('#upload-preview-area').show();
                                $('#resume_attach_block').show();
                                $('#resume_link_block').hide();

                                toastr.success(res.message);

                                $('#show_feed_back').html(stringLimit(file.name));
                                $('.resume-error-block').html(``);

                                $('#resumeFileUrl').attr('href', res.resumeFileUrl);
                            } else {
                                toastr.error(res.message);
                            }
                        },
                        error: (err) => {
                            toastr.error("{{ __("Something went wrong")}}");
                        }
                    });

                    $('.resume-error-block').html(``);
                } else {
                    $('.resume-error-block').html(``);
                }
            });

            $('#resume_link_close').click(function() {
                $('#resume_link').val("");
                $('#resume_button_area').show();
                $('#upload-preview-area').hide();
            });

            $('#remove__resume_attach').click(function (e) {
                e.preventDefault();

                $.confirm({
                    scrollToPreviousElement: false,
                    scrollToPreviousElementAnimate: false,
                    title: '{{ __('Confirmation') }}',
                    content: '{{ __('Are you sure, you want to remove this attachment?') }}',
                    buttons: {
                        confirm: () => {
                            const requestData = new FormData();
                            requestData.append('candidate_id', "{{ $user->id }}");

                            $.ajax({
                                url: "{{ route('remove.candidate.resume')}}",
                                type: 'POST',
                                data: requestData,
                                contentType: false,
                                processData: false,
                                enctype: 'multipart/form-data',
                                success: (res) => {
                                    $('#resume_button_area').show();
                                    $('#upload-preview-area').hide();
                                    $('#file_attach').val('');
                                    hasResume = false;
                                    toastr.success("{{ __("Resume removed.")}}");
                                },
                                error: (err) => {
                                    toastr.error("{{ __("Something went wrong")}}");
                                }
                            });
                        },
                        cancel: function () {

                        }
                    }
                });
            });

            $('#resume_link_button').click(function() {
                $('#resume_button_area').hide();
                $('#resume_attach_block').hide();
                $('#upload-preview-area').show();
                $('#resume_link_block').show();
            });

            function stringLimit(value, size = 10) {
                if (!value) return '';

                value = value.toString();
                if (value.length <= size) {
                    return value;
                }

                return String(value).slice(-10);
            }

        });
    </script>
@endpush
