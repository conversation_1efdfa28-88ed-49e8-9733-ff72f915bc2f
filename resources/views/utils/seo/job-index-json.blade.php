@php
    $jobPosting = [
        '@context' => 'https://schema.org/',
        '@type' => 'JobPosting',
        'title' => data_get($post, 'title', ''),
        'description' => strip_tags(data_get($post, 'requirements', '')),
        'identifier' => [
            '@type' => 'PropertyValue',
            'name' => data_get($post, 'company.name', ''),
            'value' => data_get($post, 'id')
        ],
        'datePosted' => data_get($post, 'posted_at')->format('Y-m-d'),
        'validThrough' => data_get($post, 'expire_at')->endOfDay()->format('Y-m-d\TH:i:s'),
        'employmentType' => data_get($post, 'employment_type_label', 'OTHER'),
        'hiringOrganization' => [
            '@type' => 'Organization',
            'name' => data_get($post, 'company.name', ''),
            'sameAs' => data_get($post, 'company.website', ''),
            'logo' => url($post->company->logo),
        ]
    ];

    if (isRemoteJob($post)) {
        $jobPosting['jobLocationType'] = 'TELECOMMUTE';

        $jobPosting['applicantLocationRequirements'] = [
            '@type' => 'Country',
            'name' => data_get($post, 'address.country.name') ?? 'Anywhere'
    ];
    } elseif (
        $post->address?->address_line_1 ||
        $post->address?->address_line_2 ||
        $post?->address?->country ||
        $post?->address?->city ||
        $post?->address?->state ||
        $post?->address?->postal_code
    ) {
        $address = [];

        $streetAddress = trim(data_get($post, 'address.address_line_1', '') . ' ' . data_get($post, 'address.address_line_2', ''));

        $address['streetAddress'] = !blank($streetAddress) ? $streetAddress : 'UNAVAILABLE';
        $address['addressLocality'] = data_get($post, 'address.city.name') ?? 'UNAVAILABLE';
        $address['addressRegion'] = data_get($post, 'address.state.name') ?? 'UNAVAILABLE';
        $address['postalCode'] = data_get($post, 'address.postal_code') ?? 'UNAVAILABLE';
        $address['addressCountry'] = data_get($post, 'address.country.sort_name') ?? 'UNAVAILABLE';

        $jobPosting['jobLocation'] = [
            '@type' => 'Place',
            'address' => array_merge(['@type' => 'PostalAddress'], $address)
        ];
    } else {
        $jobPosting['jobLocation'] = [
        '@type' => 'Place',
        'address' => [
            '@type' => 'PostalAddress',
            'streetAddress' => 'UNAVAILABLE',
            'addressLocality' => 'UNAVAILABLE',
            'addressRegion' => 'UNAVAILABLE',
            'postalCode' => 'UNAVAILABLE',
            'addressCountry' => 'UNAVAILABLE',
            ]
        ];
    }

    $jobPosting['baseSalary'] = [
            '@type' => 'MonetaryAmount',
            'currency' => data_get($post, 'salary_range.currency', $post->salary_currency) ?? 'UNAVAILABLE',
            'value' => [
                '@type' => 'QuantitativeValue',
                'value' => $post->getSalary() ?? 0,
                'unitText' => data_get(trans("constants.salary_type"), $post->salary_type, '')
            ]
        ];
@endphp

<script type="application/ld+json">
    {!! json_encode($jobPosting, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
</script>
