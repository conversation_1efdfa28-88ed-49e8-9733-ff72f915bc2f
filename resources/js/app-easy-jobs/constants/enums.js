export const STATUS = {
    JOB_APPLY_STATUS : {
        PENDING : 0,
        COMPLETE : 1,
        SELECT : 5,
        REJECT : 9
    },
    JOB_PIPELINE_STATUS : {
        NOT_REQUIRED : 0,
        REQUIRED : 1,
    },
    USER_STATUS : {
        ACTIVE : 'Active',
        INACTIVE : 'Inactive',
        INVITED : 'Invited',
        BLOCKED : 'Blocked',
    }
};

export const PLATFORM_NAME = {
    EASY_JOBS: 'EasyJobs',
    HACKER_RANK: 'HackerRank',
    TESTLIFY: 'Testlify',
};

export const PLATFORM_TYPE = {
    EASY_JOBS: 5,
    HACKER_RANK: 10,
    TEST_DOME: 15,
    TESTLIFY: 16,
};

export const EASYJOBS_HOST_NAME = {
    EASY_JOBS_LOCAL: 'app.easyjobs.test',
    EASY_JOBS_DEV: 'app.easyjobs.dev',
    EASY_JOBS_PROD: 'app.easy.jobs',
};

export const PLATFORMS = [
    { name: PLATFORM_NAME.EASY_JOBS },
    { name: PLATFORM_NAME.HACKER_RANK, $isDisabled: false },
    { name: PLATFORM_NAME.TESTLIFY, $isDisabled: false }
];

export const FILTER = {
    JOB_CANDIDATES : {
        NEW : 1,
        RATED : 2,
        NO_RATED : 3,
        ASSIGNED_ASSESSMENT: 4,
        UN_ASSIGNED_ASSESSMENT: 5,
    }
};

export const PERMISSIONS = {
    JOB_VIEW: "job.view",
    JOB_MANAGEMENT: "job.management",
    JOB_PUBLISH: "job.publish",
    CANDIDATE_VIEW: "candidate.view",
    CANDIDATE_DELETE: "candidate.delete",
    CANDIDATE_ORGANIZE: "candidate.organize",
    SETTINGS_COMPANY: "settings.company",
    SETTINGS_TEAM: "settings.team",
    SETTINGS_PIPELINE: "settings.pipeline",
    SETTINGS_EMAIL: "settings.email",
    SETTINGS_CANDIDATE_APPLY: "settings.candidate.apply",
    SETTINGS_OTHER: "settings.other",
    VIEW_EVALUATIONS: 'evaluation.view',
    MANAGE_OWN_EVALUATIONS: 'evaluation.manage.own',
    MANAGE_ALL_EVALUATIONS: 'evaluation.manage.all'
};

export const PIPELINE_TYPE = {
    APPLIED: 1,
    REGULAR: 10,
    REMOTE_INTERVIEW: 50,
    SELECTED: 99,
    REJECTED: 100,
};

export const RESPONSE_CODE = {
    SUCCESS: 200,
    FAILED: 400,
    UNAUTHORISED: 403,
    PAYMENT_REQUIRED: 402,
    PRECONDITION_FAILED: 412,
    UNPROCESSABLE_ENTITY: 422,
    UNAUTHORISED_ACCESS: 480,
    LOGIN_REQUIRED: 401,
    APPLICANT_DELETED: 470,
    MANAGER_DELETED: 471,
    INVITATION_REJECT: 472,
    CONFLICT_RELOAD: 499,
    PACKAGE_LIMIT_EXCEEDED: 490
};

export const INTENDANT_ROUTE_NAME = {
    CANDIDATE_DELETE: 'CANDIDATE_DELETE_UNAUTHORISED_INTENDANT',
    EVALUATION: {
        QUESTION_SET: 'company.question.group',
        ASSESSMENT: 'company.assessments',
    }
};

export const PIPELINE_TABS = {
    THUMB: 1,
    BOARD: 2
};

export const USER_TYPE = {
    EMPLOYER: 'employer',
    CANDIDATE: 'candidate'
};

export const GENDER = {
    MALE: 1,
    FEMALE: 2,
    OTHER: 3
};

export const COOKIES = {
    AFFILIATE: 'ej_fp_affiliate',
};

export const CANDIDATE_DETAILS_TABS = {
    APPLICATION: 'application',
    RESUME: 'resume',
    EVALUATION: 'evaluation',
    EVALUATION_QUIZ: 'evaluation-quiz',
    EVALUATION_SCREENING: 'evaluation-screening',
    EVALUATION_ASSESSMENT: 'evaluation-assessment',
    EVALUATION_ATTACHMENT: 'evaluation-attachment',
    CONVERSATION: 'conversation',
    VIDEO: 'video',
};


export const PACKAGE_RULES = {
    ACTIVE_JOBS: 'jobs',
    TEAM_ACCOUNTS: 'managers',
    COMPANIES: 'companies',
    ACTIVE_CANDIDATES: 'applications',
    QUIZ: 'quizzes',
    IN_APP_MESSAGING: 'messaging',
    DETAILS_ANALYTICS: 'analytics',
    CUSTOM_DOMAIN: 'custom_domain',
    SMART_WORKFLOW: 'job_pipeline',
    // PERSONALITY_TEST: 'screening',
    IQ_TEST: 'screening',
    SUPPORT: 'support',
    REMOTE_INTERVIEW: 'remote_interview',
    EASYJOBS_AI: 'easyjobs_ai'
}

export const COMPANY_API_KEY_TYPE = {
    WORDPRESS: 1,
    ZAPIER: 2
}

export const MESSAGE_TYPE = {
    REGULAR: 1,
    NOTE: 2,
    PIPELINE_CHANGE: 3,
    REMOVE_NOTE: 4
};

export const CANDIDATE_RATING = [
    { id: 5, label: '5 star' },
    { id: 2, label: '2 star' },
    { id: 4, label: '4 star' },
    { id: 1, label: '1 star' },
    { id: 3, label: '3 star' },
    { id: 0, label: '0 star' },
];

export const NOTIFICATION_CATEGORY = [
    { id: 0, label: 'All' },
    { id: 1, label: 'New Application' },
    { id: 2, label: 'Pipeline Changed' },
    { id: 3, label: 'Note for Applicant' },
    { id: 4, label: 'Mention in Note' },
    { id: 5, label: 'New Message' },
    { id: 6, label: 'Attachment for Applicant' },
    { id: 7, label: 'Rating for Applicant' },
    { id: 8, label: 'New Manager Joined' },
    { id: 9, label: 'New Collaborator Joined' },
    { id: 10, label: 'Job Updated' },
    { id: 11, label: 'Company Setting Updated' },
    { id: 12, label: 'Assessment Completed' },
    { id: 13, label: 'Received Invitation' }
];

export const HOST_NAME = {
    EASY_JOBS_LOCAL: 'app.easyjobs.test',
    EASY_JOBS_DEV: 'app.easyjobs.dev',
    EASY_JOBS_PROD: 'app.easy.jobs',
};

export const COMPANY_SIZES = [
    {
        id: 1,
        name: '1-30 Employees',
    },
    {
        id: 2,
        name: '31-60 Employees',
    },
    {
        id: 3,
        name: '61-99 Employees',
    },
    {
        id: 4,
        name: '100-200 Employees',
    },
    {
        id: 5,
        name: '201-500 Employees',
    },
    {
        id: 6,
        name: '501-1,000 Employees',
    },
    {
        id: 7,
        name: '1,001–5,000 Employees',
    },
    {
        id: 8,
        name: '5,001–10,000 Employees',
    },
    {
        id: 9,
        name: '10,000+',
    }
]

export const PackageTypes = {
    FREE: Number(process.env.MIX_FREE_PACKAGEID),
};

