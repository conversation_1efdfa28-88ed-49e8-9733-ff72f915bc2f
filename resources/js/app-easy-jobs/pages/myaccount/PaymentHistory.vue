<template>
    <div class="account-content mb-0 position-relative">
        <div class="section-title-wrap d-flex justify-content-start align-items-center">
            <my-account-hamburger-button></my-account-hamburger-button>
            <div class="section-title">{{ $t("Payment History")}}</div>
        </div>

        <PaymentHistoryLoader v-if="isContentLoading && isCompanyOwner"></PaymentHistoryLoader>

        <template v-else>
            <h4 class="empty-message" v-if="isEmpty && isCompanyOwner">{{ $t("no payment history found")}}</h4>
            <!-- data table -->
            <div class="data-table history-table" v-if="!isEmpty && isCompanyOwner">
                <div class="table-wrap">
                    <div class="table">
                        <div class="table__row table__head">
                            <div class="table-cell text-center">{{ $t("No")}}</div>
                            <div class="table-cell">{{ $t("Trans ID")}}</div>
                            <div class="table-cell">{{ $t("Details")}}</div>
                            <div class="table-cell">{{ $t("Type")}}</div>
                            <div class="table-cell">{{ $t("Coupon")}}</div>
                            <div class="table-cell">{{ $t("Total")}}</div>
                            <div class="table-cell">{{ $t("Date")}}</div>
                            <div class="table-cell text-center">{{ $t("Status")}}</div>
                            <div class="table-cell text-center">{{ $t("Actions")}}</div>
                        </div>
                        <div class="table__row" v-for="(transaction, key) in transactions">
                            <div class="table-cell pay-counter">{{(key+1)}}</div>
                            <div class="table-cell trans-ID">{{transaction.trans_id}}</div>
                            <div class="table-cell pay-Details">{{transaction.details}}</div>
                            <div class="table-cell text-center pay-type">{{transaction.type}}</div>
                            <div class="table-cell text-center pay-type">
                                <span v-if="transaction.coupon && transaction.hasOwnProperty('coupon') && transaction.coupon.hasOwnProperty('code') && transaction.coupon.code.length>0" class="color-warning" style="font-size: 12px">{{transaction.coupon.name}} ({{transaction.coupon.code}})</span>
                            </div>
                            <div class="table-cell text-right pay-amount text-nowrap">
                                <p>{{transaction.total}}</p>
                            </div>
                            <div class="table-cell pay-time">{{transaction.date}}</div>
                            <div class="table-cell pay-status">
                                <div class="semi-button h-modified semi-button-success b-radius-50" v-if="(transaction.isPayable && transaction.paid)">{{$t('Paid')}}</div>
                                <div class="semi-button h-modified semi-button-warning b-radius-50" v-if="(transaction.isPayable && !transaction.paid)">{{$t('Unpaid')}}</div>
                            </div>
                            <div class="table-cell pay-action" v-if="transaction.payment_method == 2">
                                <router-link :to="transaction.isLifetime ? {name: 'paypal-invoice', params: {id: transaction.id}} : {name: 'paypal-pay', params: {id: transaction.id}}" class="semi-button h-modified info-button b-radius-50" target="_blank">Paypal</router-link>
                            </div>
                            <div class="table-cell pay-action" v-else>
                                <button v-if="(transaction.isPayable && transaction.action.link)" @click="getActionLink(transaction.id)" class="semi-button h-modified info-button b-radius-50">{{transaction.action.title}}</button>
                            </div>
                        </div>
                    </div>

                    <pagination :pagination="paginationData" @paginate="getPaymentHistory"></pagination>
                </div>
            </div>
        </template>

        <div v-if="!isCompanyOwner">
            <PaymentHistorySkeleton/>
            <SubscriptionPaymentModel image="eicon e-owner" content="Only company owner can view history" />
        </div>
    </div>
</template>

<script>
    import client from "../../app/api/Client";
    import SubscriptionPaymentModel from "../../components/common/SubscriptionPaymentModel.vue";
    import {mapState} from "vuex";
    import PaymentHistorySkeleton from "../../components/common/PaymentHistorySkeleton.vue";
    const PaymentHistoryLoader = () => import("../../components/_loaders/PaymentHistoryLoader.vue");
    const Pagination = () => import("../../components/pagination/Pagination.vue");
    const MyAccountHamburgerButton = () => import("../../components/buttons/MyAccountHamburgerButton");

    export default {
        components: {
            PaymentHistorySkeleton,
            SubscriptionPaymentModel,
            MyAccountHamburgerButton,
            Pagination,
            PaymentHistoryLoader
        },
        data() {
            return {
                transactions: [],
                isContentLoading: false,
                paginationData: {},
            }
        },

        computed: {
            ...mapState(['company']),

            isEmpty() {
                return !this.transactions.length;
            },

            isCompanyOwner() {
                return this.company.logged_in_by_company_owner;
            }
        },

        methods: {
            async getPaymentHistory(page) {
                this.isContentLoading = true;
                try {
                    if(typeof page === 'undefined') page = 1;
                    let {data : {data}} = await client().get(`/my-account/payment-history?page=${page}`);
                    this.transactions = data.data;
                    _.unset(data, 'data');
                    this.paginationData = data;
                } catch (e) {
                }
                this.isContentLoading = false;
            },

            async getActionLink(transactionId) {
                try {
                    let {data : {data}} = await client().get(`/my-account/payment-action/${transactionId}`);
                    if (data.url !== '#') {
                        window.open(data.url, '_blank');
                    }
                } catch (e) {
                    console.log(e)
                }
            }
        },

        async mounted() {
            await this.getPaymentHistory();
        }
    }
</script>

<style scoped>
.history-table .table .table-cell.pay-status *,
    .history-table .table .table-cell.pay-action a {
        word-break: unset !important;
    }
    @media only screen and (max-width: 991.98px) {
        .pay-counter,
        .pay-type,
        .pay-time {
            text-align: left!important;
        }
    }

.pay-action {
    /*display: flex!important;*/
}
.pay-action button {
    word-break: initial !important;
}
</style>
