<template>
    <div class="page-body wrapper-fixed align-items-center justify-content-center py50">
            <div class="paypal-pay-wrapper">
                <div class="payment-main">
                    <div class="header">
                        <div class="shade"></div>
                        <div class="icon mb-3">
                            <svg viewBox="0 0 39 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M34.4 33.6C35.4 33.6 36.2 32.8 36.2 31.8C36.2 30.8 35.4 30 34.4 30C33.4 30 32.6 30.8 32.6 31.8C32.6 32.8 33.4 33.6 34.4 33.6Z" fill="#597DFC"/>
                                <path d="M28.7 30.1C27.9 30.1 27.2 30.8 27.2 31.6C27.2 32.4 27.9 33.1 28.7 33.1C29.5 33.1 30.2 32.4 30.2 31.6C30.1 30.8 29.5 30.1 28.7 30.1Z" fill="#597DFC"/>
                                <path d="M38.3 34.2C37.9 34.2 37.6 34.5 37.6 34.9C37.6 35.3 37.9 35.6 38.3 35.6C38.7 35.6 39 35.3 39 34.9C39 34.5 38.7 34.2 38.3 34.2Z" fill="#597DFC"/>
                                <path d="M31.6 36.4C32.1 36.4 32.6 36 32.6 35.4C32.6 34.9 32.2 34.4 31.6 34.4C31.1 34.4 30.6 34.8 30.6 35.4C30.7 36 31.1 36.4 31.6 36.4Z" fill="#597DFC"/>
                                <path d="M14.5 9.99999C14.7 9.99999 14.9 10.1 15 10.1C16.1 10.1 16.9 9.19999 16.9 8.19999C16.9 7.09999 16 6.29999 15 6.29999C13.9 6.29999 13.1 7.19999 13.1 8.19999C13.1 8.89999 13.5 9.59999 14.2 9.89999C14.3 9.89999 14.4 9.99999 14.5 9.99999Z" fill="#597DFC"/>
                                <path d="M27.6 22.7C29 20.5 29.8 17.8 29.8 15C29.8 7.99999 24.8 2.09999 18.1 0.699993C17.1 0.499993 16.1 0.399994 15 0.399994C7 0.399994 0.5 6.89999 0.5 15C0.5 21.8 5.1 27.6 11.4 29.2C10.4 28.7 3.4 23.9 3.5 14.6C3.5 8.59999 8.6 3.69999 14.6 3.49999C21.1 3.29999 26.5 8.49999 26.5 15C26.5 19.8 23.6 23.9 19.4 25.6C18.5 25.9 15.5 26.6 12.9 23.1C11.2 20.6 11.4 17.9 11.5 17.1C11.6 15.6 12.8 14.5 14.2 14.5C14.3 14.5 14.4 14.5 14.5 14.5C16.2 14.6 17.3 15.8 17.2 17.4C17.1 18.7 16.8 20.1 16.2 21.3L16.1 21.4C16 21.6 15.9 21.8 15.8 22C15.7 22.2 15.7 22.4 15.7 22.6C15.8 23.1 16.3 23.4 16.8 23.3C17.1 23.3 17.3 23.1 17.4 22.9C18.7 21 19.6 19.1 19.6 16.8C19.6 15.4 19.1 14.1 18.2 13.2C17.2 12.2 15.9 11.7 14.4 11.7C14.3 11.7 14.2 11.7 14.1 11.7C13.3 11.7 12.6 11.9 12 12.2C12 12.2 8.8 13.2 8.5 16.9C8.2 19 8.4 22.4 11.5 25.8C11.9 26.2 12.2 26.6 12.7 27C13.2 27.4 13.7 27.7 14.3 28.1C14.6 28.3 14.9 28.4 15.1 28.5C15.6 28.7 16.2 28.9 16.8 29C18.1 29.2 19.4 29 20.6 28.5C21.6 28.1 22.5 27.6 23.3 27L25.7 29.2C26.3 28.6 27.2 28.3 28.1 28.3C29.5 28.3 30.8 29.2 31.3 30.4C31.6 29.6 32.3 29 33.1 28.7L27.6 22.7Z" fill="#597DFC"/>
                            </svg>
                        </div>
                    </div>
                    <div class="payment-body px-4 pt-2 pb-4">
                        <p class="title">Receipt from Easy.Jobs</p>
                        <p class="receipt">Receipt {{invoice.trans_id}}</p>

                        <div class="payment-info mt-4 mb-4">
                            <div class="info">
                                <p>AMOUNT PAID</p>
                                <p>${{invoice.total}}</p>
                            </div>
                            <div class="info">
                                <p>DATE PAID</p>
                                <p>{{invoice.date_paid}}</p>
                            </div>
                            <div class="info">
                                <p>PAYMENT METHOD</p>
                                <p>Visa - 4242</p>
                            </div>
                        </div>
                        <p class="summary-title">Summery</p>
                        <div class="summary mb-5">
                            <div class="item">
                                <p>EasyJobs × 1</p>
                                <p>${{invoice.price}}</p>
                            </div>
                            <div class="hr"></div>
                            <div class="item">
                                <p>Subtotal</p>
                                <p>${{invoice.price}}</p>
                            </div>
                            <div class="item" v-if="invoice.discount_percentage > 0">
                                <p>{{invoice.discount_percentage }}% Discount ({{invoice.discount_percentage }}% off)</p>
                                <p>${{invoice.discount}}</p>
                            </div>
                            <div class="item">
                                <p>Amount charged</p>
                                <p>${{invoice.total}}</p>
                            </div>
                        </div>

                        <p class="query">If you have any questions, contact <NAME_EMAIL></p>

                        <div class="note">
                            <p>Something wrong with the email? <a href="#">View it in your browser</a>.</p>
                            <p>You're receiving this email because you made a purchase at Easy.Jobs, which partners with
                                <a href="#">Paypal</a> to provide invoicing and payment processing.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</template>
<script>

import client from "../app/api/Client";

export default {
    data() {
        return {
            invoice: {},
        }
    },
    methods: {
        async getPaypalInvoice(transactionId) {
            try {
                let {data:{data}} = await client().get(`/my-account/paypal/payment-action/${transactionId}`);
                this.invoice = data;
            } catch (e) {
            }
        }
    },
    mounted() {
        const transactionId = this.$route.params?.id || null;
        this.getPaypalInvoice(transactionId);
    }
}
</script>


<style scoped>
.py50 {
    padding-top: 50px;
    padding-bottom: 50px;
}
.paypal-pay-wrapper {
    width: 100%;
    max-width: 600px;
    background: #fff;
    border-radius: 10px;
}
.paypal-pay-wrapper .header {
    padding-top: 70px;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
}
.paypal-pay-wrapper .header .shade {
    position: absolute;
    bottom: 60px;
    left: 0;
    height: 100%;
    width: 100%;
    background: #597dfc;
    content: '';
    transform: skewY(-11deg);
}
.paypal-pay-wrapper .header .icon {
    height: 60px;
    width: 60px;
    border-radius: 50%;
    box-shadow: 0 0 5px 3px rgba(0, 0, 0, .1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    background: #fff;
    z-index: 2;
}
.paypal-pay-wrapper .header svg {
    height: 40px;
}
.paypal-pay-wrapper .payment-body .title {
    font-size: 24px;
    font-weight: 400;
    text-align: center;
}
.paypal-pay-wrapper .payment-body .receipt {
    font-size: 16px;
    font-weight: 400;
    color: #8898aa;
    text-align: center;
    margin-top: 10px;
}
.payment-info {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}
@media all and (max-width: 575px) {
    .payment-info {
        flex-direction: column;
    }
}
.payment-info .info p {
    font-size: 14px;
    color: #8898aa;
}
.payment-info .info p:last-child {
    font-weight: 400;
    color: #525f7f;
}
.summary-title {
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    color: #8898aa;
    margin-bottom: 10px;
}
.summary {
    background: #f7f9fc;
    padding: 10px 20px;
    border-radius: 10px;
}
.summary .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
}
.summary .item p {
    color: #525f7f;
    font-weight: 400;
    font-size: 16px;
}
@media all and (max-width: 575px) {
    .summary {
        padding: 10px;
    }
    .summary .item {
        gap: 5px;
    }
    .summary .item p {
        font-size: 14px;
    }
}
.summary .item:last-child p {
    font-weight: 600;
}
.summary .hr {
    border-bottom: 1px solid #e6ebf1;
    margin: 10px 0;
}
.query {
    font-size: 14px;
    font-weight: 400;
    color: #525f7f;
    padding: 20px 0;
    border-top: 1px solid #e6ebf1;
    border-bottom: 1px solid #e6ebf1;
}
.note {
    padding-top: 20px;
}
.note p {
    font-size: 12px;
    color: #8898aa;
}
.note p + p {
    margin-top: 15px;
}
.note p a {
    color: #597dfc;
}
</style>
