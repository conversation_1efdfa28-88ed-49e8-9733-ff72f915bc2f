<template>
    <div class="notification-setup-wrapper">

        <div class="form-group--wrapper p20-30">
            <div class="setup-title basis">
                <h3>{{ $t(`Email Alerts`) }}
                    <v-popover offset="10" :disabled="false" style="display:initial;">
                        <button class="ej__tooltip__button ml-1"><i class="eicon e-question"></i></button>
                        <template slot="popover">
                            <a class="tooltip-close" v-close-popover><i class="eicon e-close"></i></a>
                            <div style="text-align:left">
                                <a v-close-popover
                                   href="https://easy.jobs/docs/how-to-configure-email-setup-settings"
                                   target="_blank">
                                    {{ $t(`How to configure email setup?`) }}
                                </a>
                            </div>
                        </template>
                    </v-popover>
                </h3>
                <p>{{ $t(`Manage your email settings to ensure that you never miss an important notification whenever there’s a new update in your recruitment process.`) }}</p>
            </div>
        </div>

        <SMTPSetup />

        <div v-if="isContentLoading">
            <SettingItemsSkeleton></SettingItemsSkeleton>
        </div>

        <div v-else>
            <div class="setup-wrapper flex items-center space-between gap-8 p20-30 transition" v-for="(setting, index) in settings">

                <h4 v-if="isEmpty" class="empty-message">{{ $t(`No emails found.`) }}</h4>

                <div class="setup-info flex items-center gap-16">
                    <span class="recruitment-icon">
                        <i :class="`eicon ${setting.icon}`"></i>
                    </span>
                    <div>
                        <h4>{{ setting.name }}</h4>
                        <p class="m-width-530">{{ setting.description }}</p>
                    </div>
                </div>

                <div class="action-item flex items-center gap-16">
                    <span @click.prevent="showUpdateModal(setting)" class="update-content">{{ $t(`update`) }}</span>

                    <label class="toggle-wrap">
                        <input type="checkbox"
                               v-model="setting.isNotify"
                               value="true"
                               @change="toggleNotify(setting)"
                        >
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>

        <Animation :show="updateSetting">
            <modal-update-email
                v-if="updateSetting"
                @closeUpdateModal="hideUpdateEmailModal"
                @setupUpdated="setupUpdated"
                :data='updateData'
            ></modal-update-email>
        </Animation>
    </div>
</template>

<script>
import Animation from "../../../../components/modal/Animation.vue";
const DashboardLayout = () => import("../../../../layouts/DashboardLayout.vue");
const SettingMenu = () => import( "../../../../components/company/SettingSidebar.vue");
const ModalUpdateEmail = () => import("../../../../components/modal/company/UpdateEmail.vue");
const TableLoader = () => import("../../../../components/_loaders/_TableLoader.vue");
const SettingsHamburgerButton = () => import("../../../../components/buttons/SettingsHamburgerButton.vue");

import { mapState} from 'vuex';

import client from "../../../../app/api/Client";
import SettingItemsSkeleton from "../../../../components/_loaders/SettingItemsSkeleton.vue";
import SMTPSetup from "./SMTPSetup.vue";
import {RESPONSE_CODE} from "../../../../constants/enums";

export default {
    components: {
        SMTPSetup,
        SettingItemsSkeleton,
        Animation,
        SettingsHamburgerButton,
        DashboardLayout,
        SettingMenu,
        ModalUpdateEmail,
    },

    data() {
        return {
            isContentLoading: true,
            settings: [],
            updateData: null,
            updateSetting: false,
        }
    },

    computed: {
        ...mapState(['company', 'user']),

        isEmpty() {
            return this.settings.length === 0;
        },

        hasPackagePermissionRemoteInterview() {
            return this.user.package_rules.remote_interview;
        }
    },
    methods: {
        async getEmailSettings() {
            this.isContentLoading = true;
            try {
                let {data: {data}} = await client().get('/company/setting/email');
                this.settings = data;
            } catch (e) {
            }
            this.isContentLoading = false;
        },

        async showUpdateModal(setting) {
            if (!this.company.is_verified){
                this.$toast.error(this.$t("Your company has not been verified. Please verify it to proceed."));
                return;
            }
            let {data: {data}} = await client().get(`/company/setting/email/type/${setting.type}`);
            this.updateData = data;
            this.updateSetting = true;
            const el = document.body;
            el.classList.add('modal-open');
        },

        async hideUpdateEmailModal(isCandidateSelectModal = false) {
            this.updateSetting = false;
            const el = document.body;
            el.classList.remove('modal-open');
            if (isCandidateSelectModal.reload) {
                await this.getEmailSettings();
            }
        },

        async toggleNotify(setting) {
            if (!this.company.is_verified){
                this.$toast.error(this.$t("Your company has not been verified. Please verify it to proceed."));
                return;
            }

            try {
                let {data: { message}} = await client().put(`/company/setting/email/${setting.id}/notify`, {
                    notify: setting.isNotify
                });
                this.$toast.success(this.$t(message));
            } catch ({response}) {
                if (response?.status === RESPONSE_CODE.PACKAGE_LIMIT_EXCEEDED) {
                    this.closeModal();
                    return;
                }

                let message =  response.data.message;

                if (message){
                    this.$toast.error(message);
                }
            }
        },

        async setupUpdated() {
            await this.getEmailSettings();
        }
    },
    mounted() {
        this.getEmailSettings();
    }
}
</script>
