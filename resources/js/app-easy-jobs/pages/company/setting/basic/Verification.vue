<template>
    <div class="supported-language-wrapper">
        <div class="add-item--wrapper p20-30 flex space-between items-center gap-16">
            <div class="add-item--title supported-language-title">
                <h3>{{ $t(`Verify Company`) }}
                    <v-popover offset="10" :disabled="false" style="display:initial;">
                        <button class="ej__tooltip__button ml-1"><i class="eicon e-question"></i></button>
                        <template slot="popover">
                            <a class="tooltip-close" v-close-popover><i class="eicon e-close"></i></a>
                            <div style="text-align:left">
                                <a v-close-popover href="https://easy.jobs/docs/verify-your-company-profile/" target="_blank" style="margin-bottom: 5px;">
                                    {{ $t(`How to verify?`) }}
                                </a>
                            </div>
                        </template>
                    </v-popover>
                </h3>
                <p>{{ $t(`After you verify that you own your domain, you can start using your Easy.Jobs service.`) }}</p>
            </div>
        </div>
        <div class="p20-30 mt--20">
            <div class="overflow-auto">
                <div class="custom-domain--wrapper">
                    <div class="why-verify">
                        <h3>{{ $t(`Add a TXT verification record`) }}</h3>
                        <p class="mb-3">{{ $t(`To add a TXT Record, follow these steps:`) }}</p>
                    </div>
                    <div class="domain--settings">
                        <div class="domain--settings-item">
                            <span class="number"> 1 </span>
                            <div class="settings--content">
                                <p>Log in to your domain name provider ( eg:
                                    <a href="#" class="custom-link">godaddy.com</a>
                                    or
                                    <a href="#" class="custom-link">namecheap.com</a>
                                    )
                                </p>
                            </div>
                        </div>
                        <div class="domain--settings-item">
                            <span class="number"> 2 </span>
                            <div class="settings--content">
                                <p>Copy the TXT record below into the DNS configuration of
                                    <a href="#" class="custom-link">
                                        {{webUrl}}
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="domain--settings-item">
                            <span class="number"> 3 </span>
                            <div class="settings--content">
                                <p>Enter or copy+paste it in the Host field.
                                    <span class="action-company">
                                        _ejverify
                                    </span>
                                    <a href="#" class="custom-link">
                                        {{webUrl}}
                                    </a>
                                </p>
                                <i class="eicon e-duplicate" @click="copyToClipboard(`_ejverify`)"></i>
                            </div>
                        </div>

                        <div class="domain--settings-item">
                            <span class="number"> 4 </span>
                            <div class="settings--content">
                                <p>Enter or copy+paste the value (string) into the Answer field.
                                    <span class="action-company">
                                        {{ company.verification_code }}
                                    </span>
                                </p>
                                <i class="eicon e-duplicate" @click="copyToClipboard(company.verification_code)"></i>
                            </div>
                        </div>
                        <div class="domain--settings-item">
                            <span class="number"> 5 </span>
                            <div class="settings--content">
                                <p>Press the Verify button below</p>
                            </div>
                        </div>
                    </div>
                    <div class="button-wrapper pt-24">
                        <submit-button :click="verifyCompany" :loading="isContentLoading">{{ $t("Verify Company") }}</submit-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import client from "../../../../app/api/Client";
import {mapActions, mapState} from 'vuex';
import {UPDATE_COMPANY_DATA} from "../../../../constants/action-type";

export default {
    data() {
        return {
            isContentLoading: false,
        }
    },

    computed: {
        ...mapState(['user', 'company']),
        isVerified() {
            return this.company.is_verified;
        },
        hasWebsite() {
            return !_.isEmpty(this.company.website);
        },
        webUrl() {
            if (!this.company.website || this.company.website.lenth < 1) {
                return '';
            }

            let data = this.company.website.replace(/^https?:\/\//, '');
            return  data.replace(/^www./, '');
        }
    },
    methods: {
        ...mapActions([UPDATE_COMPANY_DATA]),

        async verifyCompany() {
            if (!this.hasWebsite) {
                this.$toast.error(this.$t('Please input website URL first.'));
                return await this.$router.push({name: 'company.setting.basic'});
            }

            try {
                let {data: {data, message}} = await client().post(`/company/verify`, {
                    is_verified: this.isVerified
                });

                this.$toast.success(this.$t(message));

                if (data.hasOwnProperty('name') && data.hasOwnProperty('id')) {
                    this[UPDATE_COMPANY_DATA](data);
                    await this.$router.push({name: 'dashboard'});
                }
            } catch (error) {
                this.$toast.error(this.$t(error.response.data.message));
            }
        }
    },
    async mounted() {
        if (this.isVerified) {
            await this.$router.push({name: 'dashboard'});
        }

        if (!this.hasWebsite) {
            this.$toast.error(this.$t('Please input website URL first.'));
            return await this.$router.push({name: 'company.setting.basic', query: { focus: 'website_url' }});
        }
    }
}
</script>

<style>
.button-wrapper {
    display: flex;
    justify-content: flex-end;
}
</style>
