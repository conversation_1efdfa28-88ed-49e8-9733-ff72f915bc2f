<template>
    <dashboard-layout>
        <div class="content-area__body">
            <!-- applicant details -->
            <section class="applicant-details">
                <div class="d-flex flex-wrap justify-content-between align-items-center">
                    <div class="back-button">
                        <a href="javascript:void(0)" @click.prevent="closeCandidateDetailsWindow()" class="back-button__icon">
                            <i class="eicon e-back"></i>
                        </a>
                        <div class="back-button__text">{{ $t("Back") }}</div>
                    </div>
                    <div class="back-button ml-auto mr-4" style="gap: 10px;">
                        <button :disabled="current === 0" @click.prevent="prev()" class="back-button__icon">
                            <i class="eicon e-arrow-left"></i>
                        </button>
                        <p>{{ current + 1 }}/<span class="candidate__count">{{ totalCandidates }}</span></p>
                        <button :disabled="current === totalCandidates - 1" @click.prevent="next()" class="back-button__icon">
                            <i class="eicon e-arrow-right"></i>
                        </button>
                    </div>

                    <div class="back-button dropdown" v-click-outside="hideDropdown" v-if="canOrganizeCandidate || canDeleteCandidate">
                        <button class="back-button__icon" @click="dropOpen = !dropOpen">
                            <i class="eicon e-more-horiz"></i>
                        </button>
                        <div class="dropdown-menu applicant-dropdown" :class="{'show': dropOpen}">
                            <a href="javascript:void(0)" class="assign"
                                v-if="showAssignAssessmentOption && canOrganizeCandidate" @click="showAssessmentModal()"
                            >
                                {{ $t(`Assign assessment`) }}
                            </a>
                            <a href="javascript:void(0)" v-if="hasIntegrationSettingPermission" @click="showCalendlyEventList=true">
                                {{ $t(`Assign Calendly Event`) }}
                            </a>
                            <a href="javascript:void(0)" class="remove" v-if="canDeleteCandidate" @click="removeCandidate">
                                {{ $t(`Remove`) }}
                            </a>
                        </div>
                    </div>
                </div>

                <transition name="slide-fade" mode="out-in">
                    <template v-if="candidate">
                        <div class="row" :key="renderComponent">
                            <candidate-info-card :candidate="candidate" @emailCandidate="sendEmail()" @load-pipeline-messages="handleConversation"></candidate-info-card>
                            <div class="col-xl-6 pr-0 order-2">
                                <div class="tab__card tab__card--primary gutter-10">
                                    <a href="javascript:void(0)" class="tab__control" v-for="(tab, index) in tabs"
                                       @click.prevent="changeTab(index)" :class="{'active' : tab.is_active}">
                                        <div class="tab__control__icon"><i :class="tab.icon"></i></div>
                                        <div class="tab__control__text">{{ $t(tab.title) }}</div>
                                    </a>
                                </div>

                                <component :is="component" :candidate="candidate" :tabData="tabData" ref="conversaionChat" :key="dynamicKey" @open-note="handleNoteClicked"></component>
                            </div>
                            <candidate-notes :candidate="candidate" @load-messages="handleLoadMessages" :noteData.sync="noteData"></candidate-notes>
                        </div>
                    </template>
                </transition>
            </section>

            <Animation :show="manageAssessment">
                <manage-assessment
                    v-if="manageAssessment"
                    @showAssignedAssignment="showAssignedAssignment"
                    @closeModal="manageAssessment = false"
                    @assessmentUpdated="assessmentAdded"
                    :selectedApplicants="selectedApplicants"
                    :job="candidate.job_slug">
                </manage-assessment>
            </Animation>

            <Animation :show="showCalendlyEventList">
                <calendly-event-list
                    v-if="showCalendlyEventList"
                    @closeModal="showCalendlyEventList = false"
                    :job="candidate.job_slug">
                </calendly-event-list>
            </Animation>
        </div>
    </dashboard-layout>
</template>
<script>
import {SWITCH_TO_ACCOUNT} from "../../constants/action-type";

const TabApplication = () => import("../../components/candidate/details/TabApplication.vue");
const TabVideo = () => import("../../components/candidate/details/TabVideo.vue");
const TabConversation = () => import('../../components/candidate/details/TabConversation.vue');
const TabEvaluation = () => import('../../components/candidate/details/TabEvaluation.vue');
const TabResume = () => import('../../components/candidate/details/TabResume.vue');
const CandidateInfoCard = () => import('../../components/candidate/details/CandidateInfoCard.vue');
const CandidateNotes = () => import('../../components/candidate/details/CandidateNotes.vue');
const ManageAssessment = () => import("../../components/modal/job/ManageAssessment");
const CalendlyEventList = () => import("../../components/modal/job/CalendlyEventList");
const DashboardLayout = () => import('../../layouts/DashboardLayout');


import {mapActions, mapState} from 'vuex';
import {CANDIDATE_DETAILS_TABS, MESSAGE_TYPE, PERMISSIONS, PIPELINE_TYPE} from "../../constants/enums";
import client from "../../app/api/Client";
import {
    ASSESSMENT_INFO_VIEW_DETAILS,
    RELOAD_CANDIDATE_PROFILE,
    SWITCHED_TO_ANOTHER_ACCOUNT
} from "../../constants/events";
import {EventBus} from "../../extra/event-bus";
import {getApplicantExists} from "../../app/api/CommonRequest";
import {canOrganizeCandidate, canDeleteCandidate, canOtherSettings, canViewCandidate} from "../../config/permission"
import Animation from "../../components/modal/Animation";
import {RESPONSE_CODE, INTENDANT_ROUTE_NAME} from "../../constants/enums";

import axios from 'axios'

export default {
    components: {
        Animation,
        TabApplication,
        TabConversation,
        TabResume,
        TabEvaluation,
        CandidateInfoCard,
        CandidateNotes,
        DashboardLayout,
        ManageAssessment,
        CalendlyEventList
    },
    data() {
        return {
            back: '',
            tabs: [
                {
                    title: "Application",
                    is_active: true,
                    icon: 'eicon e-paper',
                    tab: CANDIDATE_DETAILS_TABS.APPLICATION,
                    component: TabApplication
                },
                {
                    title: "Resume",
                    is_active: false,
                    icon: 'eicon e-cv',
                    tab: CANDIDATE_DETAILS_TABS.RESUME,
                    component: TabResume
                },
                {
                    title: "Evaluation",
                    is_active: false,
                    icon: 'eicon e-contract',
                    tab: CANDIDATE_DETAILS_TABS.EVALUATION,
                    component: TabEvaluation
                },
                {
                    title: "Conversation",
                    is_active: false,
                    icon: 'eicon e-chat',
                    tab: CANDIDATE_DETAILS_TABS.CONVERSATION,
                    component: TabConversation
                },
            ],
            component: TabApplication,
            candidate: null,
            candidates: [],
            current: 0,
            tabData: null,
            manageAssessment: false,
            reloadCount: 0,
            logs: [],
            paginateData: [],
            canOrganizeCandidate: false,
            canDeleteCandidate: false,
            renderComponent: 0,
            dropOpen:false,
            showCalendlyEventList:false,
            noteData: null,
            dynamicKey: +new Date()
        }
    },

    computed: {
        ...mapState(['company', 'user']),

        selectedApplicants() {
            return [this.candidate.id];
        },

        jobId() {
            return this.candidate.job_id;
        },

        showAssignAssessmentOption() {
            const type = this.candidate?.pipeline?.type || null;
            return ![PIPELINE_TYPE.SELECTED, PIPELINE_TYPE.REJECTED].includes(type);
        },

        totalCandidates(){
            return this.candidates.length
        },

        hasIntegrationSettingPermission() {
            return canOtherSettings();
        },

        canViewCandidate() {
            return canViewCandidate();
        }
    },

    methods: {
        ...mapActions([SWITCH_TO_ACCOUNT]),

        closeModal(){
            this.manageAssessment = false;
        },
        async removeCandidate() {
            this.dropOpen = false;
            let dialogConfig = {
                okText: this.$t('yes'),
                cancelText: this.$t('no'),
            };

            let confirm = await this.$dialog.confirm({
                title: this.$t(`Confirmation`),
                body: this.$t(`
                    <p>Are you sure, you want to remove this candidate's job application?</p>
                    <p class="mt-3">Note: You cannot undo it.</p>
                `)
            }, dialogConfig);
            if (confirm) {
                try {
                    await client().post(`/job/${this.candidate.job_slug}/candidate/delete`, {candidates: [this.$route.params.id]}, {headers: {intendant: INTENDANT_ROUTE_NAME.CANDIDATE_DELETE}});

                    this.closeCandidateDetailsWindow()
                } catch (err) {
                    if(err.response.status === RESPONSE_CODE.UNAUTHORISED_ACCESS) {
                        if (this.canViewCandidate) {
                            this.$toast.error(this.$t("Sorry you don't have permission for this action."));
                            window.location.reload();
                        }
                    }
                }
            }
        },
        showAssignedAssignment() {
            this.manageAssessment = false;
            this.reloadCount += 1;
            this.showEvaluation();
        },

        assessmentAdded() {
            this.$toast.success(this.$t(`Assessment assigned.`));
            this.manageAssessment = false;
            this.reloadCount += 1;
            this.showEvaluation();
        },
        showAssessmentModal() {
            getApplicantExists(this.candidate.id).then(() => {
            }).catch(() => {
            });
            this.manageAssessment = true;
            this.dropOpen = false;
        },
        changeTab(tabIndex) {
            this.tabs = this.tabs.map((tab, index) => {
                tab.is_active = false;
                if (index === tabIndex) {
                    tab.is_active = true;
                    this.component = tab.component;
                }
                return tab;
            });

            let activeTab = this.tabs.find((tab, index) => {
                return index === tabIndex;
            });

            if(activeTab) {
                this.$router.push({path: this.$route.fullPath, query: {tab: activeTab.tab} });
            }
        },
        hasPermission(tab) {
            if (tab.title === 'Conversation') {
                return this.company.package_rule.messaging;
            }
            if (tab.title === 'Evaluation') {
                if (this.company.package_rule.quiz || this.company.package_rule.screening) {
                    return true;
                }
                return false;
            }
            return true;
        },
        sendEmail() {
            this.changeTab(this.tabs.findIndex((t) => t.title === 'Conversation'));
            window.scrollTo(0, 0);
        },
        showEvaluation(activeTab = 3) {
            const evaluationIndex = this.tabs.findIndex((t) => t.title === 'Evaluation');
            this.renderComponent++;
            this.changeTab(evaluationIndex);
            this.tabData = activeTab;
            window.scrollTo(0, 0);
        },
        showResume() {
            this.changeTab(this.tabs.findIndex((t) => t.title === 'Resume'));
            this.tabData = 4;
            window.scrollTo(0, 0);
        },

        async showCandidateDetails(applicantId = null) {
            try {
                let {data: {data}} = await client().get(`/job/applicant/${applicantId ?? this.$route.params.id}`);

                this.switchCompany(data.company_id);

                if (!this.company.permission[PERMISSIONS.CANDIDATE_VIEW]) {
                    this.$toast.error(this.$t('Sorry you do not have permission for this action.'));
                    await this.$router.push({name: 'dashboard'});
                }

                this.candidate = data;
                this.canOrganizeCandidate = canOrganizeCandidate(this.candidate.job_id);
                this.canDeleteCandidate = canDeleteCandidate(this.candidate.job_id);

                let activeTab = this.$route.query.tab;
                if (activeTab === CANDIDATE_DETAILS_TABS.RESUME) {
                    this.showResume();
                } else if (activeTab === CANDIDATE_DETAILS_TABS.EVALUATION_QUIZ) {
                    this.showEvaluation(1);
                } else if (activeTab === CANDIDATE_DETAILS_TABS.EVALUATION_SCREENING) {
                    this.showEvaluation(2);
                } else if (activeTab === CANDIDATE_DETAILS_TABS.EVALUATION_ASSESSMENT) {
                    this.showEvaluation(3);
                } else if (activeTab === CANDIDATE_DETAILS_TABS.EVALUATION_ATTACHMENT) {
                    this.showEvaluation(4);
                } else if (activeTab === CANDIDATE_DETAILS_TABS.CONVERSATION) {
                    this.sendEmail();
                }
            } catch (error) {
                if (!axios.isCancel(error)) {
                    let errorMessage = error.response?.data?.message;
                    if(errorMessage) {
                        this.$toast.error(this.$t(errorMessage));
                    }
                    await this.$router.push({name: 'dashboard'});
                }
            }
        },

        async candidateNavigation() {
            try {
                const { pipeline } = this.$route.query;

                let { data: { data } } = await client().get(`/job/applicant/navigation/${this.candidate.job_id}`, {
                    params: {
                        pipeline: pipeline
                    }
                });
                this.candidates = data;
            } catch (error) {
            }
        },

        async next(){
            this.current++;
            await this.showCandidateDetails(this.candidates[this.current]);
            this.changeTab(0);
            this.renderComponent++;

            return this.$router.push({name : 'applicant.details', params: {id :this.candidates[this.current]}});
        },
        async prev(){
            this.current--;
            await this.showCandidateDetails(this.candidates[this.current]);
            this.changeTab(0);
            this.renderComponent++;

            return this.$router.push({name : 'applicant.details', params: {id :this.candidates[this.current]}});
        },
        hideDropdown(){
            this.dropOpen = false;
        },
        switchCompany(companyId) {
            if (companyId !== this.company.id) {
                let company = _.find(this.user.companies, {id: companyId});
                this[SWITCH_TO_ACCOUNT](company);
                EventBus.$emit(SWITCHED_TO_ANOTHER_ACCOUNT, 'company');
            }
        },

        closeCandidateDetailsWindow() {
            if (this.back.name && this.back.name.length > 0) {
                return this.$router.push({name: this.back.name, params: this.back.params, query: this.back.query});
            } else {
                return this.$router.push({name: 'jobs.published'});
            }
        },

        handleLoadMessages() {
            let activeTab = this.$route.query.tab;
            if (activeTab === CANDIDATE_DETAILS_TABS.CONVERSATION) {
                this.dynamicKey = +new Date();
            }
        },

        handleConversation() {
            let activeTab = this.$route.query.tab;
            if (activeTab === CANDIDATE_DETAILS_TABS.CONVERSATION) {
                this.dynamicKey = +new Date();
            }
        },

        handleNoteClicked(note) {
            this.noteData = note;
        },
    },
    beforeMount() {
        EventBus.$off(RELOAD_CANDIDATE_PROFILE);
    },

    async mounted() {
        window.scrollTo(0, 0);

        await this.showCandidateDetails();
        await this.candidateNavigation();

        EventBus.$on(RELOAD_CANDIDATE_PROFILE, async () => {
            await this.showCandidateDetails();
        });

        EventBus.$on(ASSESSMENT_INFO_VIEW_DETAILS, async () => {
            this.changeTab(2);
        });

        this.current = this.candidates.findIndex(el => el === this.$route.params.id)
        this.tabs = this.tabs.filter((tab) => {
            return !(!this.canOrganizeCandidate && tab.title === 'Conversation');
        })

        if (this.candidate?.video_link || this.candidate?.job_video_required){
            this.tabs.push({
                title: "Video",
                is_active: false,
                icon: 'eicon e-video',
                tab: CANDIDATE_DETAILS_TABS.VIDEO,
                component: TabVideo
            });
        }
    },
    beforeRouteEnter(to, from, next) {
        next((vm) => {
            vm.back = from;
        });
    }
}
</script>

<style scoped>
@media all and (max-width: 767px) {
    .applicant-details .back-button .back-button__text {
        display: none;
    }
}
.page-body .content-area__body .tab__card .tab__control.active {
    background: rgba(89,125,252,0.15) !important;
}
.back-button__icon[disabled] {
    background: #c7c7c7;
    color: #fff;
}
.candidate__count {
    color: #7d8091;
    font-size: 15px;
}
.back-button .dropdown-menu {
    right: 0;
    left: auto;
    padding: 7px;
    z-index: 9;
}
.back-button .dropdown-menu a {
    padding: 11.5px;
    line-height: 1;
    font-size: 14px;
    color: #7d8091;
    transition: all .3s ease;
    display: block;
    width: 100%;
}
.back-button .dropdown-menu .remove:hover {
    color: #dc3545;
}
.applicant-dropdown {
    width: 227px;
}
.slide-fade-enter-active {
    transition: all .3s ease;
}
.slide-fade-leave-active {
    transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}
.slide-fade-enter, .slide-fade-leave-to {
    transform: translateX(10px);
    opacity: 0;
}
@media all and (max-width: 1300px) {
    .tab__control .tab__control__icon {
        display: none;
    }
}
</style>
