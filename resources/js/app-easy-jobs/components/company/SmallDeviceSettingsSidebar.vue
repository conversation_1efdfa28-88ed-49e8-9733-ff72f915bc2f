<template>
    <div class="dropdown">
        <button class="btn btn-secondary dropdown-toggle" type="button" @click="toggleDropdown">
            {{ getMenuTitleForRoute(this.$route.name) }}
        </button>
        <ul :class="{'dropdown-menu': true, 'show': isDropdownOpen}">
            <li v-for="(menu, index) in menus" :key="index" v-if="menu.permission && menu.params.module">
                <router-link :to="{name: menu.routeName, params: menu.params}" class="sidebar-wrapper-item" :class="{'sidebar-wrapper-item--active': isSidebarMenuActive(menu)}">
                    <span class="sidebar-icon"><i class="eicon" :class="menu.icon"></i></span>
                    <div class="sidebar-content">
                        <h5>{{ menu.title }}</h5>
                    </div>
                </router-link>
            </li>
        </ul>
    </div>
</template>
<script>
import {mapState} from "vuex";
import {canOtherSettings} from "../../config/permission";
import {menus} from "../../config/settingSidebar";

export default  {
    name: 'SettingSidebar',

    data() {
        return {
            isDropdownOpen: false,
        }
    },

    computed: {
        ...mapState(['company']),

        canOtherSettings() {
            return canOtherSettings();
        },

        menus() {
            return menus();
        },

        getMenuTitleForRoute() {
            return (routeName) => {
                const matchingMenu = this.menus.find((menu) => menu.routeName === routeName);
                return matchingMenu ? matchingMenu.title : "";
            };
        },
    },

    methods: {
        isSidebarMenuActive(menu) {
            return this.$route.name === menu.routeName || this.$route.name === menu.extraRouteName;
        },

        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;
        }
    }
}
</script>
