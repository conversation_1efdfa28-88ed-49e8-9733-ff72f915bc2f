<template>
    <div class="skeleton-wrapper">
        <div class="skeleton-box" :style="skeletonStyle"></div>
    </div>
</template>

<script>
export default {
    props: {
        skeletonStyle: {
            type: Object,
        }
    }
}
</script>

<style scoped>
.skeleton-wrapper {
    font-size: 0;
}
.skeleton-wrapper:not(:last-child) {
    margin-bottom: 10px;
}
.skeleton-box {
    display: inline-block;
    position: relative;
    overflow: hidden;
    background: #dde0e4;
    border-radius: 5px;
}
.skeleton-box:after {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: translateX(-100%);
    background: #dde0e4;
    content: '';
    background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0));
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}
</style>
