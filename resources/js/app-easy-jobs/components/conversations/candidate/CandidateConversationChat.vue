<template>
    <div class="chat__box__content__area">
        <div class="chat__box__content" id="containerBox">

            <infinite-loading :key="identifierId" :identifier="identifierId" spinner="waveDots" direction="top" @infinite="getApplicantMessages">
                <div slot="no-more"></div>
                <div slot="no-results"></div>
            </infinite-loading>

            <div class="conversation" v-for="(message, index) in candidateMessages" v-bind:key="index" :class="message.id === lastMessageId ? 'active' : (message.pipeline || message.note ? 'border-bottom-0' : 'border__top')">
                <div class="d-flex gap-10 flex-1">
                    <div class="conversation__thumb">
                        <message-user-picture :src="message.image" :alt="message.image"></message-user-picture>
                    </div>
                    <div class="conversation__text__group">
                        <div class="conversation__header">
                            <div class="title">
                                <p class="name">
                                    <span :title="message.name" v-html="message.name"></span>
                                </p>
                            </div>
                            <span class="time">{{ message.created_at }}</span>
                        </div>
                        <div class="d-flex gap-40 justify-content-between align-items-start">
                            <div class="text ej-ck-editor flex-1" style="white-space: pre-line" v-if="message.message" v-html="message.message"></div>
                        </div>
                        <div class="attachment__wrap" v-if="message.attachment">
                            <div class="attachments">
                                <a :href="message.attachment" target="_blank" class="attached__item">
                                    <div class="icon">
                                        <i :class="message.media_icon"></i>
                                    </div>
                                    <div class="content">
                                        <p class="name">{{ message.media_name }}</p>
                                        <span class="size">{{ message.media_size }}</span>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="message__box" :class="{'message__box__sm': isOpenNoteAndAttachment}">
            <div class="editor__wrapper ej-ck-editor" id="conversation-editor">
                <CkeditorVue
                    @input="draftMessages"
                    @editorInstance="onConversationReady"
                    editorHtmlId="conversation-editor"
                    :config="conversationEditorConfig"
                    :value="form.message"
                    :onEditorValueChanged="((value) => form.message = value)"
                ></CkeditorVue>
            </div>


            <div class="message__bottom">
                <div class="attachment__wrap" v-if="filename">
                    <p class="attachment">
                        {{ shortAttachmentFileName }}
                        <a href="javascript:void(0)" @click="removeFile"><i class="eicon e-delete"></i></a>
                    </p>
                </div>
                <div class="button__group">
                    <label class="attachment__input">
                        <input type="file" ref="file" name="attach" :key="fileKey" @change="attachFile" :accept="allowFileExtension">
                        <span class="social-button semi-button-primary"><i class="eicon e-attachment"></i></span>
                    </label>

                    <conversation-submit-button :click="sendMessage" :loading="isLoading">{{ $t("Send") }}</conversation-submit-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ConversationSubmitButton from "../../buttons/ConversationSubmitButton";
import client from "../../../app/api/Client";
import InfiniteLoading from "vue-infinite-loading";
import {mapActions, mapState} from "vuex";
import {debounce} from "lodash"
import Vue from "vue";
import MessageUserPicture from "../MessageUserPicture";
import {allowUploadFileExtension, apiCallScheduler, apiCallTimer} from "../../../extra/messageConversationHelper"
import {ckEditorConversationOptions} from "../../../config/options";
import {LOAD_CONVERSATION_CANDIDATES, LOAD_MESSAGE_NOTIFICATION, UPDATE_PAGE_STATE} from "../../../constants/action-type";
import CkeditorVue from '../../../components/questions/Ckeditor.vue';

import Multiselect from "vue-multiselect";

export default {
    name: "ConversationChat",

    props: ['isOpenNoteAndAttachment'],

    data() {
        return {
            candidateMessages: [],
            errors: {
                message: '',
                file: ''
            },
            form: {
                message: '',
                file: ''
            },
            filename: '',
            isLoading: false,
            messagePage: 1,
            fileKey: null,
            showFirstMessage: false,
            firstMessageDate: null,
            lastMessageId: null,
            ckeditorInstance: null,
            identifierId: +new Date(),
            errorMessage: null
        }
    },

    components: {
        CkeditorVue,
        InfiniteLoading,
        ConversationSubmitButton,
        MessageUserPicture,
        Multiselect
    },

    computed: {
        ...mapState(['pageState']),

        shortAttachmentFileName() {
            let val = this.filename;
            if (val.length > 10) {
                return '...' + val.substring(val.length - 10, val.length);
            }
            return val;
        },

        conversationEditorConfig() {
            let placeholderConfig = {'placeholder': this.$t(`Type your message here`)};
            return {...ckEditorConversationOptions, ...placeholderConfig};
        },

        applicantId() {
            return this.pageState.applicantId;
        },

        allowFileExtension() {
            return allowUploadFileExtension.map((extension) => {
                return '.' + extension;
            }).join(', ');
        }
    },

    methods: {

        ...mapActions([LOAD_MESSAGE_NOTIFICATION, LOAD_CONVERSATION_CANDIDATES, UPDATE_PAGE_STATE]),

        async getApplicantMessages($state) {

            try {

                let {headers} = this.intendantRouteHeader();
                let queryParams = {params: {page: this.messagePage}, headers};
                let {data: {data: {applicant}}} = await this.getCandidateApplicantMessages(queryParams);

                await this.getConversationNotification();

                await this.setUserInfo(applicant);

                this.form.message = applicant.draft_message;

                if(applicant.messages.data.length > 0) {
                    this.candidateMessages.unshift(...applicant.messages.data);
                    this.firstMessageDate = applicant.messages.data[0].created_at;

                    $state.loaded();

                    if (this.messagePage < applicant.messages.last_page) {
                        this.messagePage = applicant.messages.current_page + 1;
                    } else {
                        this.showFirstMessage = true;
                        $state.complete();
                    }
                } else {
                    $state.complete();
                }
            } catch (error) {
                console.log(error);
            }
        },

        async getCandidateApplicantMessages(params) {
            return await client().get(`candidate/conversation/` + this.applicantId, params);
        },

        async sendMessage() {
            if (this.checkValidMessage()) {

                apiCallScheduler.clearFn();

                this.isLoading = true;

                await this.saveMessage();

                this.ckeditorInstance?.focus();

                apiCallScheduler.setFn(setTimeout(async () => {
                    await this.getHasConversationApplicantList();
                    await this.getConversationNotification();
                }, apiCallTimer));

                this.isLoading = false;
            }
        },

        checkValidMessage() {
            return this.form.message !=='' || this.form.file !== '';
        },

        async saveMessage() {
            const formData = new FormData();
            formData.append('file', this.form.file);
            formData.append('message', this.form.message);

            try {
                let {data : {data: { applicant, lastMessage } = {}} = {}} = await this.saveCandidateMessage(formData);

                if(applicant?.id) {
                    await this.setUserInfo(applicant);

                    this.identifierId += 1;
                    this.messagePage = 2;
                    this.showFirstMessage = false;
                    this.candidateMessages = applicant?.messages?.data || [];

                    this.lastMessageId = lastMessage?.id;
                    this.clearMessage();

                    Vue.nextTick(() => {
                        const lastMessageElement = this.$refs.lastMessage;
                        if (lastMessageElement) {
                            lastMessageElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
                        }
                    });

                    setTimeout(() => {
                        this.lastMessageId = null
                    }, 2000);
                } else {
                    await this.$router.push({name: 'applicantConversations', replace: true});
                }
            } catch (error) {
                let {response: {data: {status, message}}} = error || {};
                if (status === 'FAILED') {
                    this.setErrorMessage(message);
                }
            } finally {
                Vue.nextTick(function () {
                    const div = document.getElementById('containerBox');
                    if(div?.scrollHeight) {
                        div.scrollTop = div.scrollHeight;
                    }
                });

                this.isLoading = false;
            }
        },

        setErrorMessage(message) {
            let messages = Object.values(message);
            if(messages.length > 0) {
                this.errorMessage = messages.flat()[0] ?? '';
            }
        },

        async saveCandidateMessage(formData) {
            let {headers} = this.intendantRouteHeader();
            return await client().post(`candidate/conversation/` + this.applicantId + `/message`, formData, {headers});
        },

        async setUserInfo(data) {
            this.$emit('applicantData', {
                name: data.company?.name,
                pipeline: data.pipeline,
                image: data.company?.logo,
                applicantid: data.id,
                title: data.job?.title,
                url: data.company?.url,
                show_button: data.show_button,
                read: data.read
            });

            this[UPDATE_PAGE_STATE]({attachments: data.attachments});
        },

        clearMessage() {
            this.form.message = '';
            this.errors.message = '';
            this.form.file = '';
            this.errors.file = '';
            this.filename = ''
        },

        removeFile() {
            this.form.file = '';
            this.errors.file = '';
            this.filename = '';
        },

        attachFile() {
            this.fileKey = Math.random();
            this.errors.file = '';

            let fileSize = this.$refs.file.files[0].size / 1024 / 1024; // in MB
            if (fileSize > 2) {
                this.removeFile();
                this.errors.file = this.$t(`File size should be under 2 mb!`);
                this.$toast.error(this.$t(`File size should be under 2 mb!`));
                return false;
            }
            let extension = this.$refs.file.files[0].name.split('.').pop();
            if (!allowUploadFileExtension.includes(extension)) {
                this.removeFile();
                this.errors.file = this.$t(`Please upload a valid file.`);
                this.$toast.error(this.$t(`Please upload a valid file.`));
                return false;
            }

            this.form.file = this.$refs.file.files[0];
            this.filename = this.form.file.name;
        },

        draftMessages: debounce(function (message) {
            let {headers} = this.intendantRouteHeader();
            client().post(`job/applicant/${this.applicantId}/draft-message`, {message}, {headers});
        }, 1000),

        intendantRouteHeader() {
            return { headers: {intendant: 'applicantConversations'} }
        },

        async getHasConversationApplicantList() {
            this[LOAD_CONVERSATION_CANDIDATES]({params: {page: 1, 'has-conversation': true}});
        },

        async getConversationNotification() {
            this[LOAD_MESSAGE_NOTIFICATION]();
        },

        onConversationReady(editor) {
            editor?.focus();

            this.ckeditorInstance = editor;
        },

        triggerConversationAttachment(attachment) {
            this.$emit('open-attachment', attachment);
        },
    },
}
</script>

<style src="../../../css/conversation-chat.css" scoped></style>

<style scoped>
    .conversation {
        flex-direction: column;
    }
    .time {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    .time .seen {
        font-size: 14px;
        color: #597dfc;
    }
    .quick__templates__toggler {
        height: 35px;
        width: 35px;
        line-height: 38px;
        margin-right: 0;
    }
    @media all and (max-width: 767px) {
        .quick__templates__toggler {
            font-size: 13px;
            height: 30px;
            width: 30px;
            line-height: 33px;
        }
    }
    .quick__templates {
        position: relative;
    }
    .quick__template__dropdown {
        position: absolute;
        bottom: 110%;
        right: 0;
        width: 250px;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 5px 10px rgba(125,128,145,.3);
        background: #fff;
    }
    @media all and (max-width: 575px) {
        .quick__template__dropdown {
            right: -85px;
        }
    }
    .gap-40 {
        gap: 40px;
    }
    .flex-1 {
        flex: 1;
    }
</style>

<style>
.message__box .ej-ck-editor .ck-content {
    height: inherit;
    min-height: 85px;
    max-height: 250px;
    resize: vertical;
    border-radius: 2px 2px 0 0;
    word-break: break-word;
}

.conversation .alert.fade.show.text-center.conversation__first__message.attachment, .note {
    cursor: pointer;
    border-radius: 0.25rem !important;
}

.conversation__start .conversation__first__message {
    margin: 0px 90px;
}
.conversation.border__top {
    border-top: 1px solid #f5f7fd !important;
}

.message__wrapper .chat__box .chat__box__body .chat__box__content__area .chat__box__content .conversation {
    padding: 0px 20px !important;
}

.alert.fade.show.text-center.my-1.p-2.conversation__first__message {
    padding: 0.1rem !important;
}

.alert.fade.show.text-center.my-1.p-2.conversation__first__message.note.info-label {
    color: #004085;
}

.message__wrapper .chat__box .chat__box__body .chat__box__content__area .chat__box__content .conversation .conversation__text__group .text {
    font-size: 13px !important;
}

.content-area__body p {
    font-size: 13px;
}

@media all and (max-width: 1199.98px){
    .page-body .content-area__body .details__card .note-info .erase-button {
        visibility: visible;
        opacity: 0;
    }
}
@media (max-width: 991px){
    .conversation__start .conversation__first__message {
        margin: 0px 70px;
    }
}
</style>
