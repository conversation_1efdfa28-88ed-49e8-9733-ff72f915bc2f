<template>
    <div class="modal custom-fields show" id="new-user-modal" style="display: block">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <form action="">
                <div class="modal-content" v-click-outside="closeModal">
                    <div class="modal-header">
                        <h4 class="modal-title text-uppercase">{{ $t("invite new candidate") }}</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close" @click.prevent="closeModal()">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                      <div class="form-group">
                        <label>{{ $t("Job") }}*</label>
                        <multiselect
                            id="experience_level"
                            v-model="form.job"
                            :options="jobs"
                            openDirection="bottom"
                            :placeholder="$t('select job')"
                            track-by="id"
                            label="name"
                            selectLabel=""
                            deselectLabel=""
                            @input="jobSelectionChange"
                        ></multiselect>
                        <div class="error-message mb-3" v-text="errors.job"></div>
                      </div>


                        <div class="form-group">
                            <label>{{ $t("Email") }}*</label>
                            <div class="input-group mb-2">
                                <input type="email" class="form-control" v-bind:class="{ 'has-error': errors.email.length }" name="email" v-model.trim="form.email" placeholder="<EMAIL>">
                                <submit-button :click="addCandidate" type="button info-button text-capitalize" :loading="isLoading" loaderText="Wait">{{ $t("invite")}}</submit-button>
                            </div>
                            <div class="error-message mb-3" v-text="errors.email"></div>
                        </div>

                        <div class="content-area__body" style="padding: 0; margin-top: 10px;">
                            <div class="data-table user-table">

                                <TableLoader v-if="isLoading"></TableLoader>

                                <template v-else>
                                    <div class="table-wrap" v-if="hasInvitedCandidates">
                                        <div class="table table-modal">
                                            <div class="table__row table__head">
                                                <div class="table-cell">{{ $t(`Name`) }}</div>
                                                <div class="table-cell">{{ $t(`Email`) }}</div>
                                                <div class="table-cell" style="width: 110px"><span class="d-flex justify-content-end">{{ $t(`Actions`) }}</span></div>
                                            </div>
                                            <div class="table__row" v-for="invite in invitations">
                                                <div class="table-cell">{{ invite.name }}</div>
                                                <div class="table-cell"><span class="text-break">{{ invite.email }}</span></div>
                                                <div class="table-cell" style="width: 110px">
                                                    <div class="d-flex justify-content-end">
                                                        <a href="javascript:void(0)" class="control-button control-button--warning has_bg_color" @click="reInvite(invite.email)">
                                                            <div class="control-button__icon">
                                                                <i class="eicon e-send"></i>
                                                            </div>
                                                        </a>
                                                        <a href="javascript:void(0)" class="control-button control-button--danger has_bg_color" @click="deleteInvite(invite.email)">
                                                            <div class="control-button__icon">
                                                                <i class="eicon e-delete"></i>
                                                            </div>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- <h6 v-else style="color: #ff5f74;" class="mt-2">{{ $t('No invitations found.') }}</h6> -->
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</template>

<script>
import client from "../../../app/api/Client";
import Multiselect from "vue-multiselect";
import {mapState} from "vuex";
import {PackageTypes} from "../../../constants/enums";
const TableLoader = ()=> import('../../../components/_loaders/_TableLoader');

export default {
    props: ['jobId'],
    components: {
      Multiselect,
      TableLoader
    },

    data() {
        return {
            form: {
                email: '',
                job: ''
            },
            errors: {
                email: '',
                job: '',
            },
            invitations: [],
            jobs: [],
            isLoading: false,
        }
    },

    computed: {
        ...mapState(['user']),

        hasInvitedCandidates() {
            return this.invitations?.length;
        }
    },

    methods: {
        closeModal() {
            this.$emit('closeModal');
        },

        async getInvitations() {
            this.form.email = '';
            this.isLoading = true;

            if (this.form.job) {
              try {
                let {data: {data}} = await client().get(`/job/${this.getJobId()}/invitations`)
                this.invitations = data;
              } catch ({response}) {
                this.$toast.error(response.data.message);
                if (response.status === 480 || response.status === 490) {
                  await this.$router.push({name: 'dashboard'});
                }
              } finally {
                this.isLoading = false;
              }
            }
        },

        addCandidate() {
            if (this.user.package_rules.name === 'Free') {
                this.$toast.error(this.$t(`This is a pro feature, please upgrade to suitable package.`));
                this.closeModal();
                return;
            }

            this.isLoading = true;

            if (!this.isValid()) {
                this.isLoading = false;
                return;
            }

            this.addCandidateToJob().then(({data: {message}}) => {
                this.$toast.success(message);
                this.form.email = ''
                this.getInvitations();
            }).catch(({response}) => {
                if (response.status === 422) {
                    this.errors = _helper.serializeValidationMessage(response.data.message);
                    this.$toast.error(this.$t('Oops! Please check your input'));
                } else {
                    this.$toast.error(response.data.message);
                }
            }).finally(() => {
                this.isLoading = false;
                this.form.email = '';
            });
        },

        reInvite(email) {
            if(this.isLoading) {
                return;
            }

            this.form.email = email;

            if (!this.isValid()) {
                this.isLoading = false;
                return;
            }

            this.addCandidateToJob().then(({data: {message}}) => {
                this.$toast.success(message);
            }).catch(({response}) => {
                if (response.status === 422) {
                    this.errors = _helper.serializeValidationMessage(response.data.message);
                    this.$toast.error(this.$t('Oops! Please check your input'));
                } else {
                    this.$toast.error(response.data.message);
                }
            }).finally(res => {
                this.form.email = '';
            });
        },

        deleteInvite(email) {
            client().post(`job/${this.getJobId()}/invitation/delete`, {email}).then(({data: {message}}) => {
                this.$toast.success(message);
            }).finally(() => {
                this.invitations = this.invitations.filter((invitation) => {
                    return invitation.email !== email;
                });
            });
        },

        isValid() {
            this.errors.email = "";
            this.errors.job = "";

            if (this.form.job === null || this.form.job.length < 1) {
                this.errors.job = this.$t("Please select a job");
                return false;
            }

            if (this.form.email.length < 1) {
                this.errors.email = this.$t("Email field is required");
                return false;
            }

            if (!_helper.validateEmail(this.form.email)) {
                this.errors.email = this.$t("Please type a valid email address");
                return false;
            }

            return true;
        },

        addCandidateToJob() {
            return client().post(`job/${this.getJobId()}/candidate/add`, this.form);
        },

        getJobId() {
          let jobDetails = this.jobs.find(job => job.id === this.form.job.id);
          return jobDetails.slug;
        },

        jobSelectionChange() {
          if(this.form.job !== null) {
            this.getInvitations();
          } else {
            this.invitations = [];
          }
        },

        async loadPublishedJobs() {
          try {
            let {data: {data}} = await client().get('/job/active');
            this.jobs = data;
          } catch (e) {

          }
        },
    },
    async created() {
      await this.loadPublishedJobs();
    },
};
</script>

<style scoped>
.table-modal .table-cell {
    padding: 10px !important;
    background: #f8f8fd !important;
}

@media (max-width: 767.98px) {
  .custom-fields .modal-body .button {
    height: 40px !important;
  }
}
@media (max-width: 445px) {
    .page-body .content-area__body .data-table .table-wrap .table .table__row {
        display: flex;
        flex-direction: column;
    }
    .page-body .content-area__body .data-table .table-wrap .table .table__row .table-cell:first-child {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        border-bottom-left-radius: 0;
    }
    .page-body .content-area__body .data-table .table-wrap .table .table__row .table-cell:last-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 10px;
        border-bottom-left-radius: 10px;
        width: 100% !important;
        display: flex;
        justify-content: flex-start !important;
    }
}


</style>
