<template>
    <div>
        <ResumeParser
            :key="showResumeParser"
            :showDefaultResumeModal="showResumeModal"
            @resumeParser="candidateResumeParser"
        >
        </ResumeParser>

        <!--Basic information-->
        <section class="content-card section-gap" v-if="tabs[tabBasicInformation]">
            <div class="content-card__title-wrapper">
                <div class="content-card__title">
                    <div class="content-card__title__logo">
                        <i :class="tabs[tabBasicInformation]['icon']"></i>
                    </div>
                    <p class="content-card__title__text">{{ $t(tabs[tabBasicInformation]['title']) }} </p>
                </div>

                <div class="connect-resume-btn" @click="connectWithResume">
                    <span>Connect Resume</span>
                </div>
            </div>

            <div class="content-card__text">
                <div class="form-wrapper">
                    <div class="row">
                        <div class="col-md-6"
                             v-if="reqFirstName = isRequired(applyRules[tabBasicInformation],'first_name', true)">
                            <div class="form-card">
                                <div class="label-wrap">
                                    <label for="first_name">{{ $t("First Name") }}{{ reqFirstName ? '*' : '' }}</label>
                                </div>
                                <input type="text"
                                       v-model.trim="form.first_name"
                                       id="first_name"
                                       :placeholder="$t('First Name')"
                                       maxlength="50">
                                <div class="error text-danger" v-if="errors['first_name']"
                                     v-text="errors['first_name']"></div>
                            </div>
                        </div>
                        <div class="col-md-6"
                             v-if="reqLastName = isRequired(applyRules[tabBasicInformation],'last_name', true)">
                            <div class="form-card">
                                <div class="label-wrap">
                                    <label for="last_name">{{ $t("Last Name") }}{{ reqLastName ? '*' : '' }}</label>
                                </div>
                                <input type="text"
                                       v-model.trim="form.last_name"
                                       id="last_name"
                                       :placeholder="$t('Last Name')"
                                       maxlength="50">
                                <div class="error text-danger" v-if="errors['last_name']"
                                     v-text="errors['last_name']"></div>
                            </div>
                        </div>
            <div class="col-md-6" v-if="(applyRules[tabBasicInformation]['email'])">
              <div class="form-card mb-3">
                <div class="label-wrap">
                  <label for="email">{{ $t("email address") }}{{ isRequired(applyRules[tabBasicInformation],'email') ? '*' : '' }}</label>
                </div>
                <input type="text" id="email"
                       v-model="form.email"
                       @keyup="validateEmail"
                       :placeholder="$t('email address')"
                       maxlength="100"/>
                <div class="error text-danger" v-if="errors['email']" v-text="errors['email']"></div>
                <div class="error text-danger redirect__url" v-html="authRedirectUrl"></div>
              </div>
            </div>

            <div class="col-md-6" v-if="(applyRules[tabBasicInformation]['phone_number'])">
              <div class="form-card mb-3">
                <div class="label-wrap">
                  <label for="phone_number">{{ $t("Phone number") }}{{ isRequired(applyRules[tabBasicInformation],'phone_number') ? '*' : '' }}</label>
                </div>
                <input type="text" id="phone_number"
                       class="numberOnlyInput"
                       v-model.trim="form.phone_number"
                       :placeholder="$t('Phone number')"
                       maxlength="20"/>
                <div class="error text-danger" v-if="errors['phone_number']"
                     v-text="errors['phone_number']"></div>
              </div>
            </div>

            <div class="col-md-6" v-if="(applyRules[tabBasicInformation]['nationality'])">
              <div class="form-card mb-3">
                <div class="label-wrap">
                  <label for="nationality">{{ $t("Nationality") }} {{ isRequired(applyRules[tabBasicInformation],'nationality') ? '*' : '' }}</label>
                </div>
                <multiselect
                    v-model="form.nationality"
                    id="nationality"
                    :options="nationalities"
                    :placeholder="$t('Select Nationality')"
                    track-by="value"
                    label="name"
                    selectLabel=""
                    deselectLabel=""
                ></multiselect>
                <div class="error text-danger" v-if="errors['nationality']"
                     v-text="errors['nationality']"></div>
              </div>
            </div>

            <div class="col-md-6" v-if="(applyRules[tabBasicInformation]['national_id'])">
              <div class="form-card mb-3">
                <div class="label-wrap">
                  <label for="national_id">{{ $t("National ID") }} {{ isRequired(applyRules[tabBasicInformation],'national_id') ? '*' : '' }}</label>
                </div>
                <input type="text" id="national_id"
                       v-model="form.national_id"
                       :placeholder="$t('National ID')"
                       maxlength="100">
                <div class="error text-danger" v-if="errors['national_id']"
                     v-text="errors['national_id']"></div>
              </div>
            </div>

            <div class="col-md-6" v-if="(applyRules[tabBasicInformation]['dob'])">
              <div class="form-card">
                <div class="label-wrap">
                  <label for="dob_date_picker">{{ $t("Date of Birth") }}{{ isRequired(applyRules[tabBasicInformation],'dob') ? '*' : ''}}</label>
                </div>
                <div class="date-picker">
                  <date-picker
                      id="dob_date_picker"
                      v-model="form.dob"
                      :placeholder="$t('mm/dd/yyyy')"
                      autocomplete="off"
                      :config="datePickerOptions"
                  ></date-picker>
                </div>
              </div>
              <div class="error text-danger" v-if="errors['dob']" v-text="errors['dob']"></div>
            </div>

            <div class="col-md-6" v-if="(applyRules[tabBasicInformation]['gender'])">
              <div class="form-card">
                <div class="label-wrap">
                  <label for="gender">{{ $t("Gender") }}{{ isRequired(applyRules[tabBasicInformation],'gender') ? '*' : '' }}</label>
                </div>

                <multiselect
                    v-model="form.gender"
                    id="gender"
                    :options="genders"
                    :placeholder="$t('select gender')"
                    track-by="value"
                    :allow-empty="false"
                    label="name"
                    selectLabel=""
                    deselectLabel=""
                ></multiselect>
                <div class="error text-danger" v-if="errors['gender']" v-text="errors['gender']"></div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6" v-for="field in applyCustomFields">
              <div class="form-card">
                <div class="label-wrap">
                  <label :for="field.field_name">{{ field.title }} <span v-if="field.required">*</span></label>
                </div>
                <input type="text"
                       v-model.trim="field.value"
                       :id="field.field_name"
                       :placeholder="field.title"
                       maxlength="250">
                <div class="error text-danger" v-if="field.error" v-text="field.error"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
        </section>

        <!--Education-->
        <section class="content-card section-gap" v-if="tabs[tabEducation]">
          <div class="content-card__title">
            <div class="content-card__title__logo">
              <i :class="tabs[tabEducation]['icon']"></i>
            </div>
            <p class="content-card__title__text">{{ $t(tabs[tabEducation]['title']) }}</p>
          </div>
          <div class="content-card__text">
            <div class="form-wrapper">
              <div class="form__header" v-if="allEducations.length === 0">
                <p class="form__header-subtitle">{{ $t("No education qualification available") }}</p>
              </div>
              <div class="error text-danger mb-3" v-if="errors['education']" v-text="errors['education']"></div>
              <div class="row mb-3" v-if="allEducations.length > 0" v-for="(education, key) in allEducations"
                   :key="key">
                <div class="col-md-12">
                  <div class="form-title">
                    <h4 class="form-card__title">{{ $t("Academic") }} {{ key + 1 }}</h4>
                    <a href="#" class="form-card__erase-button" @click.prevent="removeEducation(key)"><i
                        class="eicon e-close"></i></a>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label :for="`level_id_${key}`">
                        {{ $t("Level of Education") }} {{ isRequired(applyRules[tabEducation],'education.*.level_id') ? '*' : '' }}
                      </label>
                    </div>
                    <vue-autosuggest
                        ref="autosuggest"
                        v-model="education.level"
                        @input="levelInputHandler"
                        @click="levelClickHandler(key)"
                        @selected="levelSelect"
                        :suggestions="filteredLevelSuggestions"
                        :get-suggestion-value="getLevelSuggestionValue"
                        :inputProps="{id: `level_${key}`, placeholder: $t('Level of Education'), class: 'form-control' }">
                      <template slot="before-suggestions">
                        <div class="vue__suggestion__label">
                          {{ $t('You can select from following suggestion list or type your own:') }}
                        </div>
                      </template>
                      <template slot-scope="{ suggestion }">
                        <div>
                          <b>{{ suggestion.item.name }}</b>
                        </div>
                      </template>
                    </vue-autosuggest>

                    <div class="error text-danger" v-if="errors[`education.${key}.level`]"
                         v-text="errors[`education.${key}.level`]"></div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label :for="`degree_id_${key}}`">
                        {{ $t("Exam or Degree Title") }} {{ isRequired(applyRules[tabEducation],'education.*.degree_id') ? '*' : '' }}
                      </label>
                    </div>
                    <vue-autosuggest
                        ref="autosuggest"
                        v-model="education.degree"
                        @input="degreeInputHandler"
                        @click="degreeClickHandler(key)"
                        @selected="degreeSelect"
                        :suggestions="filteredDegreeSuggestions"
                        :get-suggestion-value="getDegreeSuggestionValue"
                        :inputProps="{id: `degree_${key}`, placeholder: $t('Exam or Degree Title'), class: 'form-control' }">
                      <template slot="before-suggestions">
                        <div class="vue__suggestion__label">
                          {{ $t('You can select from following suggestion list or type your own:') }}
                        </div>
                      </template>
                      <template slot-scope="{ suggestion }">
                        <div>
                          <b>{{ suggestion.item.name }}</b>
                        </div>
                      </template>
                    </vue-autosuggest>
                    <div class="error text-danger" v-if="errors[`education.${key}.degree`]"
                         v-text="errors[`education.${key}.degree`]"></div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label :for="`passing_year_${key}`">
                        {{ $t("Year of passing") }} {{ isRequired(applyRules[tabEducation],'education.*.passing_year') ? '*' : '' }}
                      </label>
                    </div>
                    <input type="text"
                           class="passing_year"
                           :id="`passing_year_${key}`"
                           v-model="education.passing_year"
                           :name="`education[${key}][passing_year]`"
                           :placeholder="$t('Year of passing')"
                           maxlength="4"
                           @keypress="isNumber($event)"
                    />
                    <div class="error text-danger" v-if="errors[`education.${key}.passing_year`]"
                         v-text="errors[`education.${key}.passing_year`]"></div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label :for="`institute_name_${key}`">
                        {{ $t("Institute name") }} {{ isRequired(applyRules[tabEducation],'education.*.academy_name') ? '*' : '' }}
                                        </label>
                    </div>
                    <input type="text"
                           class="institute_name"
                           :id="`institute_name_${key}`"
                           :name="`education[${key}][academy_name]`"
                           v-model="education.academy_name"
                           :placeholder="$t('Institute name')"
                           maxlength="100"
                    >
                    <div class="error text-danger" v-if="errors[`education.${key}.academy_name`]"
                         v-text="errors[`education.${key}.academy_name`]"></div>
                  </div>
                </div>
              </div>

              <button type="button" @click="addNewEducation" class="button primary-button"><i class="ti-plus"></i>
                {{ $t("Add Academy") }}
              </button>
            </div>
          </div>
        </section>

        <!--Employment-->
        <section class="content-card section-gap" v-if="employmentRequiredFields(this.applyRules[tabEmployment])">
          <div class="employment-title-wrap d-flex align-items-center mb-4 justify-content-between">
            <div class="content-card__title mb-0">
              <div class="content-card__title__logo">
                <i :class="tabs[tabEmployment]['icon']"></i>
              </div>
              <p class="content-card__title__text">{{ $t(tabs[tabEmployment]['title']) }}</p>
            </div>

          <label class="checkbox no-employment mb-0" v-if="allEmployments.length == 0 && this.applyRules[tabEmployment]['employment.*.allow_no_experience'] == 'nullable'">
            <input type="checkbox"
              class="no-employment"
              v-model="noEmploymentRecord"
              @change="hasNoEmploymentRecord()">
            <span class="no-employment">{{ $t('I have no employment record') }}</span>
          </label>
          </div>
          <div class="content-card__text">
            <div class="form-wrapper">
              <div class="form__header" v-if="allEmployments.length == 0">
                <p class="form__header-subtitle">{{ $t('No Employment Available') }}</p>
              </div>

              <div class="row mb-3" v-if="allEmployments.length > 0" v-for="(employment, key) in allEmployments">
                <div class="col-md-12">
                  <div class="form-title">
                    <h4 class="form-card__title">{{ $t("Company") }} {{ key + 1 }}</h4>
                    <a href="#" class="form-card__erase-button" @click.prevent="removeCompany(key)"><i
                        class="eicon e-close"></i></a>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label :for="`company_${key}`">{{ $t("Company Name") }}{{
                          isRequired(applyRules[tabEmployment],'employment.*.company_id') ? '*' : '' }}
                        </label>
                    </div>
                    <vue-autosuggest
                        ref="autosuggest"
                        v-model="employment.company_name"
                        @selected="companyNameSelect"
                        @input="companyNameInputHandler"
                        @click="companyNameClickHandler(key)"
                        :suggestions="filteredCompanyNameSuggestions"
                        :get-suggestion-value="getCompanyNameSuggestionValue"
                        :inputProps="{id: `company_name_${key}`, placeholder: $t('Company Name'), class: 'form-control' }">
                      <template slot="before-suggestions">
                        <div class="vue__suggestion__label">
                          {{ $t('You can select from following suggestion list or type your own:') }}
                        </div>
                      </template>
                      <template slot-scope="{ suggestion }">
                        <div>
                          <b>{{ suggestion.item.name }}</b>
                        </div>
                      </template>
                    </vue-autosuggest>

                    <div class="error text-danger" v-if="errors[`employment.${key}.company_name`]"
                         v-text="errors[`employment.${key}.company_name`]"></div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label :for="`designation_${key}`">{{ $t("Designation") }}{{
                          isRequired(applyRules[tabEmployment],'employment.*.designation') ? '*' : '' }}
                        </label>
                    </div>
                    <vue-autosuggest
                        ref="autosuggest"
                        v-model="employment.designation"
                        @input="designationInputHandler"
                        @click="designationClickHandler(key)"
                        @selected="designationSelect"
                        :suggestions="filteredDesignationSuggestions"
                        :get-suggestion-value="getDesignationSuggestionValue"
                        :inputProps="{id: `designation_${key}`, placeholder: $t('Designation'), class: 'form-control' }">
                      <template slot="before-suggestions">
                        <div class="vue__suggestion__label">
                          {{ $t('You can select from following suggestion list or type your own:') }}
                        </div>
                      </template>
                      <template slot-scope="{ suggestion }">
                        <div>
                          <b>{{ suggestion.item }}</b>
                        </div>
                      </template>
                    </vue-autosuggest>

                    <div class="error text-danger" v-if="errors[`employment.${key}.designation`]"
                         v-text="errors[`employment.${key}.designation`]"></div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label :for="`department_${key}`">{{ $t("Department") }}{{
                          isRequired(applyRules[tabEmployment],'employment.*.department_id') ? '*' : '' }}
                        </label>
                    </div>
                    <vue-autosuggest
                        ref="autosuggest"
                        v-model="employment.department"
                        @input="departmentInputHandler"
                        @click="departmentClickHandler(key)"
                        @selected="departmentSelect"
                        :suggestions="filteredDepartmentSuggestions"
                        :get-suggestion-value="getDepartmentSuggestionValue"
                        :inputProps="{id: `department_${key}`, placeholder: $t('Enter department'), class: 'form-control' }">
                      <template slot="before-suggestions">
                        <div class="vue__suggestion__label">
                          {{ $t('You can select from following suggestion list or type your own:') }}
                        </div>
                      </template>
                      <template slot-scope="{ suggestion }">
                        <div>
                          <b>{{ suggestion.item.name }}</b>
                        </div>
                      </template>
                    </vue-autosuggest>
                    <div class="error text-danger" v-if="errors[`employment.${key}.department`]"
                         v-text="errors[`employment.${key}.department`]"></div>
                  </div>
                </div>

                <div class="col-md-6">
                  <div class="form-card">
                    <div class="label-wrap d-flex justify-content-between">
                      <label :for="`employment_start_${key}`">
                          {{$t("Employment Period")
                        }}{{ isRequired(applyRules[tabEmployment],'employment.*.from') ? '*' : '' }}</label>
                      <label class="checkbox-wrap" style="display: flex; justify-content: flex-end; text-align: right">
                        <input type="checkbox" v-model="employment.is_currently_working"/>
                        <span class="checkbox-text">{{ $t('Currently Working') }}</span>
                      </label>
                    </div>
                    <div class="form-group__date">
                      <div class="date-picker">
                        <date-picker
                            :id="`employment_start_${key}`"
                            v-model="employment.from"
                            :placeholder="$t('mm/dd/yyyy')"
                            autocomplete="off"
                            :config="datePickerOptions"
                        ></date-picker>
                      </div>
                      <h4 class="px-2">{{ $t("to") }}</h4>
                      <div class="date-picker">
                        <date-picker
                            :id="`employment_end_${key}`"
                            v-model="employment.to"
                            :placeholder="$t('mm/dd/yyyy')"
                            autocomplete="off"
                            :config="datePickerOptions"
                            :disabled="employment.is_currently_working"
                        ></date-picker>
                      </div>
                    </div>
                    <div class="error text-danger" v-if="errors[`employment.${key}.from`]"
                         v-text="errors[`employment.${key}.from`]"></div>
                    <div class="error text-danger" v-if="errors[`employment.${key}.to`]"
                         v-text="errors[`employment.${key}.to`]"></div>
                  </div>
                </div>


                <div class="col-md-12">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label :for="`responsibilities_${key}`">{{ $t("Responsibilities") }}</label>
                    </div>
                    <textarea
                        class="responsibilities"
                        :id="`responsibilities_${key}`"
                        v-model="employment.responsibilities"
                        :placeholder="$t('Write Job Responsibilities')"
                        rows="4"
                    ></textarea>
                    <div class="error text-danger" v-if="errors[`employment.${key}.responsibilities`]"
                         v-text="errors[`employment.${key}.responsibilities`]"></div>
                  </div>
                </div>
              </div>

              <div class="error text-danger mb-3" v-if="errors['employment']">{{ errors['employment'] }}</div>

              <button type="button" @click="addNewEmployment" class="button primary-button"><i
                  class="ti-plus"></i> {{ $t("Add Company") }}
              </button>
            </div>
          </div>
        </section>

        <!--Others-->
        <section class="content-card section-gap" v-if="tabs[tabOthers]">
          <div class="content-card__title">
            <div class="content-card__title__logo">
              <i :class="tabs[tabOthers]['icon']"></i>
            </div>
            <p class="content-card__title__text">{{ $t(tabs[tabOthers]['title']) }}</p>
          </div>
          <div class="content-card__text">
            <div class="form-wrapper">
              <div class="row">
                <div class="col-md-2" v-if="(applyRules[tabOthers]['expected_salary'])">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label for="expected_salary">{{ $t("Expected Salary") }}{{
                          isRequired(applyRules[tabOthers],'expected_salary') ? '*' : '' }}
                        </label>
                    </div>
                    <div class="currency-icon currency-in-input">
                      <input type="text"
                             v-model.trim="form.expected_salary"
                             id="expected_salary"
                             :placeholder="$t('Expected Salary')"
                             autocomplete="off"
                             maxlength="20"
                      />
                    </div>
                    <div class="error text-danger" v-if="errors['expected_salary']"
                         v-text="errors['expected_salary']"></div>
                  </div>
                </div>
                <div class="col-md-5" v-if="(applyRules[tabOthers]['linkedin'])">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label for="expected_salary">{{ $t("LinkedIn profile link") }}{{
                          isRequired(applyRules[tabOthers],'linkedin') ? '*' : '' }}
                      </label>
                    </div>
                    <input type="url"
                           id="linkedIn"
                           v-model="form.linkedin"
                           placeholder="https://www.linkedin.com/in/yourprofilename"
                           maxlength="250"
                    >
                    <div class="error text-danger" v-if="errors['linkedin']"
                         v-text="errors['linkedin']"></div>
                  </div>
                </div>
                <div class="col-md-5" v-if="(applyRules[tabOthers]['facebook'])">
                  <div class="form-card">
                    <div class="label-wrap">
                      <label for="facebook">{{ $t("Facebook profile link") }}{{
                          isRequired(applyRules[tabOthers],'facebook') ? '*' : '' }}
                      </label>
                    </div>
                    <input type="url"
                           id="facebook"
                           v-model="form.facebook"
                           placeholder="https://www.facebook.com/yourprofilename"
                           maxlength="250"
                    >
                    <div class="error text-danger" v-if="errors['facebook']"
                         v-text="errors['facebook']"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>


        <section class="content-card section-gap" v-if="candidateConsent">
            <div class="row mt-5">
                <div class="col-md-12">
                    <label class="checkbox-wrap">
                        <input
                            type="checkbox"
                            id="consent"
                            v-model="consentChecked"
                        />
                        <span class="checkbox-text"></span>
                        <div class="" v-html="candidateConsent"></div>
                    </label>
                </div>
            </div>
        </section>

        <section class="content-card section-gap">
          <div class="row">
            <div class="col-md-12">
              <div class="content-card__text d-flex justify-content-between">
                <div></div>
                <button class="button primary-button" :disabled="isLoading" @click.prevent="submitForm">
                  {{ $t("Save and Continue") }}
                </button>
              </div>
            </div>
          </div>
        </section>
    </div>
</template>

<script>
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
import {JOB} from "../../../utils/constants";
import {forEach, map, keys, find, merge, debounce} from "lodash";
import {VueAutosuggest} from 'vue-autosuggest';
import ResumeParser from "./ResumeParser.vue";

export default {
    name: "PersonalInformation",
    props: ['tabs', 'data', 'applyRules', 'nationalities', 'genders', 'customFields', 'candidateConsent'],

    components: {
        ResumeParser,
        Multiselect,
        VueAutosuggest
    },

    data() {
        return {
            form: {
                step: JOB.APPLY.STEPS.PERSONAL_INFORMATIONS,
                nationality: null,
                first_name: "",
                last_name: "",
                email: "",
                phone_number: "",
                national_id: "",
                country_id: null,
                dob: "",
                gender: null,
                expected_salary: null,
                linkedin: "",
                facebook: "",
                education: [],
                employment: [],
                custom_fields: [],
                resume_file: '',
                user_id: _USER_ID
            },
            errors: {},
            allEducations: [],
            educationLevelOptions: [],
            educationDegreeOptions: [],
            hasErrorInEducation: false,
            allEmployments: [],
            hasErrorInEmployment: false,
            datePickerOptions: {
                format: 'MM/DD/YYYY',
                useCurrent: false,
                maxDate: new Date().setHours(23, 59, 59, 999)
            },
            isLoading: false,
            noEmploymentRecord: false,
            currentYear: new Date().getFullYear(),
            applyCustomFields: [],
            selectedDepartment: '',
            selectedLevel: '',
            selectedDegree: '',
            selectedCompany: '',
            selectedDesignation: '',
            selectedIndex: null,
            companies: [],
            designations: [],
            departments: [],
            levels: [],
            degrees: [],
            popupSearchCharLimit: 2,
            resumeFileName: '',
            showResumeModal: false,
            showResumeParser: 0,
            authRedirectUrl: '',
            consentChecked: false,
        }
    },

    computed: {
        filteredCompanyNameSuggestions() {
            if (this.companies.length > 0) {
                return [{
                    data: this.companies
                }];
            }
            return [];
        },
        filteredDegreeSuggestions() {
            if (this.degrees.length > 0) {
                return [{
                    data: this.degrees
                }];
            }
            return [];
        },
        filteredDepartmentSuggestions() {
            if (this.departments.length > 0) {
                return [{
                    data: this.departments
                }];
            }
            return [];
        },
        filteredLevelSuggestions() {
            if (this.levels.length > 0) {
                return [{
                    data: this.levels
                }];
            }
            return [];
        },
        filteredDesignationSuggestions() {
            if (this.designations.length > 0) {
                return [{
                    data: this.designations.filter(item => {
                        return (item.toLowerCase().indexOf(this.selectedDesignation.toLowerCase()) > -1);
                    })
                }];
            }
            return [];
        },
        tabBasicInformation() {
            return JOB.APPLY.TABS.BASIC_INFORMATION;
        },
        tabEducation() {
            return JOB.APPLY.TABS.EDUCATIONAL_QUALIFICATION;
        },
        tabEmployment() {
            return JOB.APPLY.TABS.EMPLOYMENT_HISTORY;
        },
        tabOthers() {
            return JOB.APPLY.TABS.OTHERS;
        }
    },

    methods: {
        companyNameSelect(item) {
            this.allEmployments[this.selectedIndex]['company_name'] = item.item.name;
        },
        designationSelect(item) {
            this.allEmployments[this.selectedIndex]['designation'] = item.item;
        },
        departmentSelect(item) {
            this.allEmployments[this.selectedIndex]['department'] = item.item.name;
        },
        levelSelect(item) {
            this.allEducations[this.selectedIndex]['level'] = item.item.name;
        },
        degreeSelect(item) {
            this.allEducations[this.selectedIndex]['degree'] = item.item.name;
        },
        designationInputHandler(item) {
            this.selectedDesignation = '';
            if (item) {
                this.selectedDesignation = item ?? '';
            }
            this.getDesignations(this.selectedDesignation);
        },
        designationClickHandler(item) {
            this.selectedIndex = item;
            this.selectedDesignation = event.target.value;
            this.getDesignations(this.selectedDesignation);
        },
        getDesignationSuggestionValue(suggestion) {
            return suggestion.item;
        },
        companyNameInputHandler(item) {
            this.selectedCompany = '';
            if (item) {
                this.selectedCompany = item ?? '';
            }
            this.getCompanies(this.selectedCompany);
        },
        companyNameClickHandler(item) {
            this.selectedIndex = item;
            this.selectedCompany = event.target.value;
            this.getCompanies(this.selectedCompany);
        },
        getCompanyNameSuggestionValue(suggestion) {
            return suggestion.item.name;
        },
        departmentInputHandler(item) {
            this.selectedDepartment = '';
            if (item) {
                this.selectedDepartment = item ?? '';
            }
            this.getDepartments(this.selectedDepartment);
        },
        departmentClickHandler(item) {
            this.selectedIndex = item;
            this.selectedDepartment = event.target.value;
            this.getDepartments(this.selectedDepartment);
        },
        getDepartmentSuggestionValue(suggestion) {
            return suggestion.item.name;
        },
        levelInputHandler(item) {
            this.selectedLevel = '';
            if (item) {
                this.selectedLevel = item ?? '';
            }
            this.getLevels(this.selectedLevel);
        },
        levelClickHandler(item) {
            this.selectedIndex = item;
            this.selectedLevel = event.target.value;
            this.getLevels(this.selectedLevel);
        },
        getLevelSuggestionValue(suggestion) {
            return suggestion.item.name;
        },
        degreeInputHandler(item) {
            this.selectedDegree = '';
            if (item) {
                this.selectedDegree = item ?? '';
            }
            this.getDegrees(this.selectedDegree);
        },
        degreeClickHandler(item) {
            this.selectedIndex = item;
            this.selectedDegree = event.target.value;
            this.getDegrees(this.selectedDegree);
        },
        getDegreeSuggestionValue(suggestion) {
            return suggestion.item.name;
        },

        getCompanies: debounce(function(val = '') {
          const keyword = val.trim();
          if(keyword.length === 0) {
            this.companies = [];
          }
          if(keyword.length <= this.popupSearchCharLimit) {
            return false;
          }

          let route = _ROUTES._COMPANY + '?keyword=' + keyword;
          axios.get(route).then(({data}) => {
            this.companies = data;
          });
        }, 300),

        getDepartments: debounce(function(val = '') {
          const keyword = val.trim();
          if(keyword.length === 0) {
            this.departments = [];
          }
          if(keyword.length <= this.popupSearchCharLimit) {
            return false;
          }

          let route = _ROUTES._DEPARTMENT + '?keyword=' + keyword;
          axios.get(route).then(({data}) => {
            this.departments = data;
          });
        }, 300),

        getDesignations: debounce(function(val = '') {
          const keyword = val.trim();
          if(keyword.length === 0) {
            this.designations = [];
          }
          if(keyword.length <= this.popupSearchCharLimit) {
            return false;
          }

          let route = _ROUTES._DESIGNATION + '?keyword=' + keyword;
          axios.get(route).then(({data}) => {
            this.designations = data;
          });
        }, 300),

        getLevels: debounce(function(val = '') {
          const keyword = val.trim();
          if(keyword.length === 0) {
            this.levels = [];
          }
          if(keyword.length <= this.popupSearchCharLimit) {
            return false;
          }

          let route = _ROUTES._EDUCATION_LEVEL + '?keyword=' + keyword;
          axios.get(route).then(({data}) => {
            this.levels = data;
          });
        }, 300),

        getDegrees: debounce(function(val = '') {
          const keyword = val.trim();
          if(keyword.length === 0) {
            this.degrees = [];
          }
          if(keyword.length <= this.popupSearchCharLimit) {
            return false;
          }

          let route = _ROUTES._DEGREE + '?keyword=' + keyword;
          axios.get(route).then(({data}) => {
            this.degrees = data;
          });
        }, 300),

    validate() {
      let isValid = true;
      if (this.isRequired(this.applyRules[this.tabBasicInformation], 'first_name', true)) {
        if (!this.form.first_name) {
          this.errors.first_name = this.$t("Please input first name.");
          isValid = false;
        } else {
          if (this.form.first_name.length > 50) {
            this.errors.first_name = this.$t("Please input first name within 50 characters.");
            isValid = false;
          }
        }
      }

      if (this.isRequired(this.applyRules[this.tabBasicInformation], 'last_name', true)) {
        if (!this.form.last_name) {
          this.errors.last_name = this.$t("Please input last name.");
          isValid = false;
        } else {
          if (this.form.last_name.length > 50) {
            this.errors.last_name = this.$t("Please input last name within 50 characters.");
            isValid = false;
          }
        }
      }

        if (this.isRequired(this.applyRules[this.tabBasicInformation], 'email', true)) {
            if (!this.form.email) {
                this.errors.email = this.$t("Please input email address.");
                isValid = false;
            }
        }

      if (this.isRequired(this.applyRules[this.tabBasicInformation], 'phone_number', true)) {
        if (!this.form.phone_number) {
          this.errors.phone_number = this.$t("Please input phone number.");
          isValid = false;
        } else {
          if (this.form.phone_number.length > 20) {
            this.errors.phone_number = this.$t("Please input phone number within 20 characters.");
            isValid = false;
          }
        }
      }

      if (this.isRequired(this.applyRules[this.tabBasicInformation], 'national_id', true)) {
        if (this.form.national_id && this.form.national_id.length > 100) {
          this.errors.national_id = this.$t("Please input national id within 100 characters.");
          isValid = false;
        }
      }

      if (this.isRequired(this.applyRules[this.tabBasicInformation], 'dob', true)) {
        if (!this.form.dob) {
          this.errors.dob = this.$t("Please select date of birth.");
          isValid = false;
        }
        if (new Date(this.form.dob).getTime() > new Date().getTime()) {
          this.errors.dob = this.$t("Please select valid date of birth.");
          isValid = false;
        }
      }

      if (this.isRequired(this.applyRules[this.tabBasicInformation], 'gender', true)) {
        if (!this.form.gender) {
          this.errors.gender = this.$t("Please select gender.");
          isValid = false;
        }
      }

      if (this.applyCustomFields && this.applyCustomFields.length) {
            const scriptRegex = /<script.*?>|<\/script>/i;
            const htmlRegex = /<[^>]+>/g;

            forEach(this.applyCustomFields, cf => {
              if (scriptRegex.test(cf.value) || htmlRegex.test(cf.value)) {
                  cf.error = this.$t("Please enter a valid text.");
                  isValid = false;
              } else if ((cf.value === null || cf.value.length === 0) && cf.required) {
                cf.error = this.$t("Please input {FIELD}.", {FIELD: cf.title.toLowerCase()});
                isValid = false;
              } else if (cf.value !== null && cf.value.length > 250) {
                cf.error = this.$t("Please input {FIELD} within 50 characters.", {FIELD: cf.title.toLowerCase()});
                isValid = false;
              } else {
                cf.error = '';
              }
            });
      }

      if (this.isRequired(this.applyRules[this.tabEducation], 'education.*.academy_name', true)) {
        if (this.allEducations.length === 0) {
          this.errors.education = this.$t('Please enter academic record(s).');
          isValid = false;
        } else {
          forEach(this.allEducations, (edu, key) => {
            if (!edu.level) {
              this.errors[`education.${key}.level`] = this.$t("Please select level of education.");
              isValid = false;
            }

            if (!edu.degree) {
              this.errors[`education.${key}.degree`] = this.$t("Please select exam or degree title.");
              isValid = false;
            }

            if (!edu.passing_year) {
              this.errors[`education.${key}.passing_year`] = this.$t("Please input year of passing.");
              isValid = false;
            } else if (edu.passing_year.toString().length !== 4) {
              this.errors[`education.${key}.passing_year`] = this.$t("Please select valid year of passing.");
              isValid = false;
            } else if (edu.passing_year > new Date().getFullYear()) {
              this.errors[`education.${key}.passing_year`] = this.$t("Please select valid year of passing.");
              isValid = false;
            }

            if (!edu.academy_name) {
              this.errors[`education.${key}.academy_name`] = this.$t("Please input institute name.");
              isValid = false;
            } else if (edu.academy_name.length > 100) {
              this.errors[`education.${key}.academy_name`] = this.$t("Please input institute name within 100 characters.");
              isValid = false;
            }
          });
        }
      }

      if (this.isRequired(this.applyRules[this.tabEmployment], 'employment.*.company_id', true)) {
        if (!this.noEmploymentRecord) {
          if (this.allEmployments.length === 0) {
            this.errors.employment = this.$t('Please enter employment record(s).');
            isValid = false;
          } else {
            forEach(this.allEmployments, (employment, key) => {
              if (!employment.company_name) {
                this.errors[`employment.${key}.company_name`] = this.$t("Please input company name.");
                isValid = false;
              } else if (employment.company_name.length > 100) {
                this.errors[`employment.${key}.company_name`] = this.$t("Please input company name within 100 characters.");
                isValid = false;
              }

              if (!employment.designation) {
                this.errors[`employment.${key}.designation`] = this.$t("Please input designation.");
                isValid = false;
              } else if (employment.designation.length > 100) {
                this.errors[`employment.${key}designation`] = this.$t("Please input designation within 100 characters.");
                isValid = false;
              }

              if (!employment.department) {
                this.errors[`employment.${key}.department`] = this.$t("Please select department.");
                isValid = false;
              }

              if (!employment.from) {
                this.errors[`employment.${key}.from`] = this.$t("Please input employment period.");
                isValid = false;
              }
              if (new Date(employment.from).getTime() > new Date().getTime()) {
                this.errors[`employment.${key}.from`] = this.$t("Please select valid from date.");
                isValid = false;
              }
              if (employment.to && new Date(employment.to).getTime() > new Date().getTime()) {
                this.errors[`employment.${key}.to`] = this.$t("Please select valid to date.");
                isValid = false;
              }
              if (employment.to && new Date(employment.from).getTime() > new Date(employment.to).getTime()) {
                this.errors[`employment.${key}.to`] = this.$t("Please select valid to date.");
                isValid = false;
              }

              if (employment.responsibilities && employment.responsibilities.length > 5000) {
                this.errors[`employment.${key}.responsibilities`] = this.$t("Please input responsibilities within 5000 characters.");
                isValid = false;
              }
            });
          }
        }

      }

      if (this.isRequired(this.applyRules[this.tabOthers], 'expected_salary', true)) {
        if (!this.form.expected_salary) {
          this.errors.expected_salary = this.$t("Please input expected salary.");
          isValid = false;
        } else {
          if (this.form.expected_salary.length > 20) {
            this.errors.expected_salary = this.$t("Please input expected salary within 20 characters.");
            isValid = false;
          }
        }
      }

      if (this.isRequired(this.applyRules[this.tabOthers], 'linkedin', true)) {
        if (!this.form.linkedin) {
          this.errors.linkedin = this.$t("Please input linkedin profile link.");
          isValid = false;
        } else {
          if (!this.validateURL(this.form.linkedin)) {
            this.errors.linkedin = this.$t("Please input valid linkedin profile link.");
            isValid = false;
          }

          if (!this.validateLinkedInURL(this.form.linkedin)) {
            this.errors.linkedin = this.$t("Please input valid linkedin profile link.");
            isValid = false;
          }
          if (this.form.linkedin.length > 250) {
            this.errors.linkedin = this.$t("Please input linkedin profile link within 250 characters.");
            isValid = false;
          }
        }
      }

      if (this.isRequired(this.applyRules[this.tabOthers], 'facebook', true)) {
        if (!this.form.facebook) {
          this.errors.facebook = this.$t("Please input facebook profile link.");
          isValid = false;
        } else {
          if (!this.validateURL(this.form.facebook)) {
            this.errors.facebook = this.$t("Please input valid facebook profile link.");
            isValid = false;
          }
          if (!this.validateFacebookURL(this.form.facebook)) {
            this.errors.facebook = this.$t("Please input valid facebook profile link.");
            isValid = false;
          }
          if (this.form.facebook.length > 250) {
            this.errors.facebook = this.$t("Please input facebook profile link within 250 characters.");
            isValid = false;
          }
        }
      }

      if (!isValid) {
        toastr.error(this.$t('Oops! Please check your input'));
      }

      return isValid;
    },

        async submitForm() {
          this.setDefaultError();

          if (!this.validate()) {
            return false;
          }

        if (this.candidateConsent && !this.consentChecked) {
            toastr.error(this.$t("Oops! Looks like you haven't checked the consent box."));
            return false;
        }

          this.isLoading = true;
          this.form.noEmploymentRecord = this.noEmploymentRecord;
          if (this.applyCustomFields) {
            this.form.custom_fields = this.applyCustomFields.map(cf => ({
              id: cf.id,
              type: cf.type,
              field_name: cf.field_name,
              title: cf.title,
              value: cf.value
            }));
          }

          try {
            this.serializeEducation();
            this.serializeEmployment();
            this.removeUnnecessaryData();
            let {data} = await axios.post(_ROUTES._SUBMIT_URL, this.form);

            if (data.url) {
              window.location.href = data.url;
            }

            if (data.redirect_url) {
                toastr.error(this.$t('Account found with this email. You need to login.'));

                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 700);
            }
          } catch ({response}) {
            if (response?.status === 422) {
              this.serializeErrors(response.data.message);
              toastr.error(this.$t('Oops! Please check your input'));
            } else if(response?.data?.message) {
              toastr.error(response?.data?.message);
            }

          }
          this.isLoading = false;
        },

        async refreshCsrfToken() {
            try {
                const {data: csrfToken} = await axios.get('/ajax/refresh-csrf');
                axios.defaults.headers.common['X-CSRF-TOKEN'] = csrfToken;

                const metaCsrf = document.querySelector('meta[name="csrf-token"]');
                if (metaCsrf) {
                    metaCsrf.setAttribute('content', csrfToken);
                }
            } catch (error) {
                toastr.error(this.$t(error));
            }
        },


        removeUnnecessaryData() {
          let rules = [];
          map(this.applyRules, r => {
            return map(r, (v, k) => {

              rules.push({
                field: k,
                rule: v,
              });
            });
          });

          map(keys(this.form), key => {
            let rule = find(rules, {field: key});
            /*if (rule !== undefined && rule.rule === 'nullable') {
              unset(this.form, key);
            }*/
          });
        },
        setDefaultError() {
          this.errors = {
            first_name: "",
            last_name: "",
            email: "",
            phone_number: "",
            nationality: '',
            national_id: "",
            dob: "",
            gender: '',
            expected_salary: '',
            linkedin: "",
            facebook: "",
            education: '',
            employment: '',
            step: '',
            country_id: '',
          };
        },
        serializeErrors(errors) {
          for (let key in errors) {
            let lastIndex = (errors[key].length - 1);
            let err = errors[key][lastIndex]
            this.errors[key] = err;
          }
        },
        serializeEducation() {
          if (this.allEducations.length) {
            this.form.education = this.allEducations.map(edu => {
              if (edu.level) {
                edu.level_id = edu.level.id;
              }
              if (edu.degree) {
                edu.degree_id = edu.degree.id;
              }
              return edu;
            });
          }
        },
        serializeEmployment() {
          if (this.allEmployments.length) {
            this.form.employment = this.allEmployments.map(emp => {
              if (emp.department) {
                emp.department_id = emp.department.id;
              }
              emp.company_id = emp.company_name;
              return emp;
            });
          }
        },
        setFormData() {
          let basicInfo = this.data[this.tabBasicInformation];
          merge(this.form, basicInfo);

          this.applyCustomFields = this.customFields;
          this.applyCustomFields = this.applyCustomFields.map(acf => {
            if(!basicInfo.custom_fields) {
              acf.value = '';
            } else {
              let fieldVal = basicInfo.custom_fields.find(bcf => bcf.field_name === acf.field_name);
              acf.value = fieldVal ? fieldVal.value: '';
            }
            return acf;
          });
          this.allEducations = this.data[this.tabEducation];
          this.allEmployments = this.data[this.tabEmployment];
          this.educationLevelOptions = this.levels;
          this.form.expected_salary = this.data[this.tabOthers]['expected_salary'];
          this.form.linkedin = this.data[this.tabOthers][1];
          this.form.facebook = this.data[this.tabOthers][2];

          if (this.allEducations.length) {
            this.allEducations.map((edu, key) => {
              this.educationDegreeOptions.push(
                  {
                    index: key,
                    options: this.degrees.filter(d => d.education_level_id == edu.level.id)
                  }
              )
            });
          }
        },

        isRequired(rules, field) {
          return rules[field] == 'required' ? true : false;
        },

        addNewEducation() {
          this.selectedLevel = '';
          this.selectedDegree = '';

          this.errors['education'] = '';
          this.allEducations.push({
            id: null,
            user_id: null,
            academy_name: '',
            degree_id: null,
            level_id: null,
            passing_year: null,
            result_type: null,
            result: null,
          });
        },

        removeEducation(key) {
          this.selectedLevel = '';
          this.selectedDegree = '';

          const education  = Object.values(this.allEducations[key]).filter((value)=> {
            return !!value;
          }).length;

          if(education === 0) {
            this.allEducations.splice(key, 1);
            return false
          }

          let t = this;

          let message = {
            title: this.$t('Confirm'),
            body: this.$t('do you really want to delete this educational qualification?')
          };

          this.$dialog.confirm(message, {
            okText: this.$t('Yes'),
            cancelText: this.$t('No'),
          }).then(() => {
            t.allEducations.splice(key, 1);
          });
        },

        checkEducationIsRequired() {
          return (this.applyRules['education.*.level_id'] == 'required');
        },
        addNewEmployment() {
          this.selectedCompany = '';
          this.selectedDesignation = '';
          this.selectedDepartment = '';

          this.noEmploymentRecord = false;
          this.errors['employment'] = '';
          this.allEmployments.push({
            id: null,
            user_id: null,
            from: '',
            to: '',
            department: null,
            company_id: null,
            designation: null,
            result_type: null,
            result: null,
          });
        },

        removeCompany(key) {
          this.selectedCompany = '';
          this.selectedDesignation = '';
          this.selectedDepartment = '';

          const employment  = Object.values(this.allEmployments[key]).filter((value)=> {
            return !!value;
          }).length;

          if(employment === 0) {
            this.allEmployments.splice(key, 1);
            return false
          }

          let t = this;
          let message = {
            title: this.$t('Confirm'),
            body: this.$t('Are you sure you want to delete this employment record?')
          };

          this.$dialog.confirm(message, {
            okText: this.$t('Yes'),
            cancelText: this.$t('No'),
          }).then(() => {
            t.allEmployments.splice(key, 1);
          });
        },

        checkEmploymentIsRequired() {
          return (this.applyRules['employment.*.company_id'] == 'required');
        },

        validateURL(str) {
            var url_pattern = /^(https?):\/\/(-\.)?([^\s\/?\.#-]+\.?)+(\/[^\s]*)?$/i;
            return !!str.match(url_pattern);
        },
        hasNoEmploymentRecord() {
            let t = this;
            if (this.noEmploymentRecord) {
                t.allEmployments = [];
                this.errors['employment'] = '';
            } else {
                this.addNewEmployment();
            }
        },
        isNumber(evt) {
            evt = (evt) ? evt : window.event;
            var charCode = (evt.which) ? evt.which : evt.keyCode;
            if ((charCode > 31 && (charCode < 48 || charCode > 57))) {
                evt.preventDefault();
            } else {
                return true;
            }
        },
        validateLinkedInURL(linkedin) {
            let linkedinReg=/(https?)?:?(\/\/)?(([w]{3}||\w\w)\.)?linkedin.com(\w+:{0,1}\w*@)?(\S+)(:([0-9])+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
            let regX = new RegExp(linkedinReg);
            return  regX.test(linkedin);
        },
        validateFacebookURL(facebook) {
            let facebookReg=/(?:https?:\/\/)?(?:www\.)?(mbasic.facebook|m\.facebook|facebook|fb)\.(com|me)\/(?:(?:\w\.)*#!\/)?(?:pages\/)?(?:[\w\-\.]*\/)*([\w\-\.]*)/;
            let regX = new RegExp(facebookReg);
            return  regX.test(facebook);
        },

        candidateResumeParser(data) {
            if(data?.user) {
                this.form.first_name = data.user.first_name;
                this.form.last_name = data.user.last_name;
                this.form.email = data.user.email;
                this.form.phone_number = data.user.phone;
            }

            if(data?.educations) {
                this.allEducations = data.educations;
            }

            if(data?.employments) {
                this.allEmployments = data.employments;
            }

            if(data?.resumeInfo) {
                this.resumeFileName = data.resumeInfo.resumeFileName
            }

            if(data?.resume_file) {
                this.form.resume_file = data.resume_file;
            }
        },

        connectWithResume(show = true) {
            this.showResumeParser++;
            this.showResumeModal = show;
        },

        manageResumeModal() {
            // Retrieve the object from localStorage or create an empty object
            const storageData = JSON.parse(localStorage.getItem('autofill-resume-jobs')) || {};

            this.connectWithResume(storageData[_JOB_POST_SLUG] !== false);
        },

        validateEmail: debounce(function() {
            const email = this.form.email.trim();
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            this.errors.email = '';

            if (emailRegex.test(email)) {
                this.checkExistingEmail(email);
            } else {
                this.authRedirectUrl = '';
            }
        }, 700),

        async checkExistingEmail(email) {
            try {
                let {data} = await axios.get("/ajax/candidate/unique/email", {
                    params: {
                        "email": email,
                        "user_id": this.form.user_id,
                        "slug": _JOB_POST_SLUG,
                    }
                });

                if (data.message) {
                    this.authRedirectUrl = data.message;
                } else {
                    this.authRedirectUrl = '';
                }
            } catch (e) {
            }
        },
        employmentRequiredFields(tabEmployment) {
            const requiredCount = Object.keys(tabEmployment).filter(key => tabEmployment[key] === 'required').length;
            return requiredCount > 1;
        }
    },

    watch: {
        'form.email'(newValue) {
            this.form.email = newValue.replace(/\s/g, '');
        }
    },

    async mounted() {
        await this.refreshCsrfToken();
        setInterval(this.refreshCsrfToken, 3600000);

        await this.setFormData();

        await this.setDefaultError();

        if (this.checkEducationIsRequired() && this.allEducations.length === 0) {
            this.addNewEducation();
        }

        if (this.checkEmploymentIsRequired() && this.allEmployments.length === 0) {
            this.addNewEmployment();
        }

        this.manageResumeModal();
    }
}
</script>

<style>
.content-card .checkbox-wrap {
    cursor: pointer;
    width: 100%;
    font-size: .875rem;
    font-weight: 500;
    color: #7d8091;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}
#autosuggest__input {
  outline: none;
  position: relative;
  display: block;
  border: 1px solid #616161;
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

#autosuggest__input.autosuggest__input-open {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.autosuggest__results-container {
  position: relative;
  width: 100%;
}

.autosuggest__results {
  font-weight: 300;
  margin: 0;
  position: absolute;
  z-index: 10000001;
  width: 100%;
  border: 1px solid #e0e0e0;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  background: white;
  padding: 0px;
  max-height: 320px;
  overflow: auto;
}

.autosuggest__results ul {
  list-style: none;
  padding-left: 0;
  margin: 0;
}


.autosuggest__results .autosuggest__results-item {
  cursor: pointer;
  padding: 10px 15px;
  border-bottom: 1px solid #e0e0e0;
}

#autosuggest ul:nth-child(1) > .autosuggest__results_title {
  border-top: none;
}

.autosuggest__results .autosuggest__results_title {
  color: gray;
  font-size: 11px;
  margin-left: 0;
  padding: 15px 13px 5px;
  border-top: 1px solid lightgray;
}

.autosuggest__results .autosuggest__results-item:active,
.autosuggest__results .autosuggest__results-item:hover,
.autosuggest__results .autosuggest__results-item:focus,
.autosuggest__results
.autosuggest__results-item.autosuggest__results-item--highlighted {
  background-color: #F6F6F6;
}

.multiselect__tags {
  padding-top: 0;
  padding-left: 26px;
  min-height: 44px;
  border-color: #cdd8fe;
  color: #000;
  background: #f5f7fd;
}

input.multiselect__input {
  min-height: 44px;
  line-height: 44px;
  margin-bottom: 0;
  background: transparent;
  font-size: 0.875rem;
  color: #1c2238;
  padding: 0 !important;
  border: 0 !important;
}

span.multiselect__placeholder {
  margin: 0;
  padding: 0;
  color: #1c2238;
  font-size: 0.875rem;
  font-weight: 500;
}

.multiselect__select {
  height: 44px;
}

span.multiselect__option {
  padding: 12px 26px;
}

span.multiselect__single {
  padding: 0;
  margin: 0;
  height: 44px;
  line-height: 44px;
  color: #1b2237;
  background: #f5f7fd;
}

span.multiselect__placeholder {
  height: 44px;
  line-height: 44px;
}

input.multiselect__input {
  padding: 0;
}

.multiselect__spinner {
  height: 48px;
  background: #f5f7fd;
  border-radius: 5px;
}

.multiselect.disabled {
  pointer-events: none;
}

.multiselect.disabled:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: #d8cece63;
  top: 0;
  left: 0;
  border-radius: 5px;
}

span.multiselect__tag {
  margin-top: 12px;
}

button:disabled {
  background: #999 !important;
  box-shadow: 0 10px 10px rgba(167, 177, 179, 0.3);
}

.form-note {
  color: #7d8091;
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: 500;
}

.form-note span {
  color: #597dfc;
}

.no-employment:hover {
  cursor: pointer;
}

.content-card__text {
  margin-left: 0!important;
}
.vue__suggestion__label {
  padding: 7px 15px;
  font-weight: 400;
  font-size: 14px;
  color: #597dfc;
  border-bottom: 1px solid #e3e3e3;
}
#autosuggest .autosuggest__results .autosuggest__results-item {
  border-bottom: 0;
  font-size: 0.875rem;
}

label.checkbox.no-employment {
  display: inline-flex;
  align-items: center;
}
label.checkbox.no-employment input.no-employment {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  margin-right: 5px !important;
}

label.checkbox-wrap .checkbox-text {
  display: inline-flex !important;
  align-items: center !important;
}
label.checkbox-wrap .checkbox-text:before {
  font-size: 18px !important;
  margin-right: 0 !important;
  margin-top: -3px
}
.content-card .checkbox-wrap ul {
    list-style: disc;
}
.checkbox-wrap ul,
.checkbox-wrap ol {
    margin-left: 15px;
}
@media all and (max-width: 599px) {
  .employment-title-wrap {
    flex-wrap: wrap;
    gap: 15px;
  }
}

.resume-details {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    align-items: center;
    background: #F8F8F8;
    border: 1px solid #E3E7EE;
    padding: 10px;
    border-radius: 8px;
    width: 350px;
}
.resume-name-area {
    display: flex;
    align-items: center;
    gap: 10px;
}
.resume-name {
    font-size: .87em;
    color: #344054;
    font-weight: 500;
}
.resume-details i {
    font-size: 14px;
    color: #44546F;
}

.content-card__title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.content-card__title-wrapper .content-card__title {
    margin-bottom: 0;
}

.content-card__title {
    margin-bottom: 30px;
}

.connect-resume-btn {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #597DFC;
    text-decoration: underline;
    cursor: pointer;
}

.error.text-danger.redirect__url a {
    color: #dc3545 !important;
    text-decoration: underline;
}

</style>
