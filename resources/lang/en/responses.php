<?php

return [
    'saved' => 'Saved.',
    'sorted' => 'Sorted.',
    'updated' => 'Updated.',
    'deleted' => 'Deleted.',
    'success' => 'Success',
    'logo_size_updated' => 'Logo Size Updated Successfully',
    'invalid_token' => 'Invalid token',
    'unauthorized_access' => 'Unauthorized Access',
    'email_not_verified' => 'Your email address has not been verified. Please verify it to proceed.',
    'something_went_wrong' => 'Something went wrong.',
    'something_went_wrong_try_again' => 'Something went wrong. Please try again.',
    'dont_have_permission_to_access' => 'You don\'t have permission to access this company.',
    'dont_have_permission_to_access_feature' => 'You don\'t have permission to access this feature.',
    'invitation_link_sent' => 'Invitation link sent.',
    'failed_to_update' => 'Failed to update.',
    'candidate_assessment_failed' => 'Sorry, assessment cannot be assigned to Selected or Rejected candidates. Please try again.',
    'candidate_already_assigned_assessment' => 'Candidate has already assigned the assessment.',

    'context_added' => ':context added.',
    'context_created' => ':context created.',
    'context_saved' => ':context saved.',
    'context_sorted' => ':context sorted.',
    'context_updated' => ':context updated.',
    'context_deleted' => ':context deleted.',
    'context_pinned' => ':context :pin',
    'context_removed' => ':context removed.',
    'context_drafted' => ':context drafted.',
    'context_uploaded' => ':context uploaded.',
    'context_duplicated' => ':context duplicated.',
    'context_cannot_be_added' => ':context can\'t be added.',
    'context_cannot_be_saved' => ':context can\'t be saved.',
    'context_cannot_be_sorted' => ':context can\'t be sorted.',
    'context_cannot_be_updated' => ':context can\'t be updated.',
    'context_cannot_be_deleted' => ':context can\'t be deleted.',
    'context_cannot_be_pinned' => ':context can\'t be pinned.',
    'context_cannot_be_duplicate' => ':context can\'t be duplicate.',
    'context_job_notification' => 'Notification turned :context for the job.', // @context[On, Off]
    'context_not_found' => ':context not found.', // @context [Data, User, Candidate, Applicant, Sorry image]
    'context_subscription' => 'Subscription :context.',
    'context_subscription_package' => 'Subscription package cannot be :context.',
    'context_failed_to_update' => ':context failed to update.',

    'slack' => [
        'slack_added' => 'Slack successfully connected',
        'slack_disconnect' => 'Slack successfully disconnected',
        'cannot_connect' => 'Slack not connected',
    ],
    'hackerrank' => [
        'invite_cancelled' => 'Invitation cancelled successfully.',
        'invite_cancelled_failed' => 'Invite Cancelled Failed.',
        'successfully_assigned_hackerrank_assessment' => 'Successfully Assigned HackerRank Assessment.'
    ],
    'docusign' => [
        'empty_grant_token' => 'Grant token not found.',
    ],
    'note' => [
        'not_added' => 'Note is not added.',
        'write_more' => 'Please write some note!',
        'cannot_delete' => 'Cannot delete this note.'
    ],

    'attachment' => [
        'max_size' => 'The attachment size must be under 2mb.',
        'cannot_downloaded_file' => 'Cannot downloaded file.',
    ],

    'conversation' => [
        'write_more' => 'Please write some message!',
    ],

    'auth' => [
        'please_login' => 'Please login.',
        'email_verified' => 'Email verified',
        'login_successful' => 'Successfully logged in',
        'registration_successful' => 'Registration Successful.',
        'password_reset_link_sent' => 'Password reset link sent.',
        'password_reset_successful' => 'Password reset successfully.',
        'already_have_an_account' => 'You already have an account, Please login.',
        'email_already_verified' => 'Email already verified please refresh or login again.',
        'verification_link_sent' => 'A fresh verification link has been sent to your email address.',
        'password_doesnt_match' => 'Password doesn\'t match.',
        'cant_get_email_information' => 'Can\'t get email information!',
        'email_not_exists' => 'Email not exists.',
        'invalid_reset_token' => 'The password reset link is invalid or has expired.',
        'account_not_exists' => 'account not exists this domain.',
        'email_verification_link_invalid' => 'Invalid email verification link!',
        'email_verification_link_expired' => 'Email verification link expired!',
        'email_verification_user_invalid' => 'Email verification for invalid user!',
        'invalid_email_format' => 'Invalid email format.',
    ],

    'candidate' => [
        'applicant_remove' => 'Job applicant(s) removed.',
        'application_discarded' => 'Job application discarded.',
        'applicant_already_applied' => 'Applicant already applied for this job.',
        'applicant_selected_jobs' => 'Applicant selected job found.',
        'applicant_selected_celebration' => 'Applicant selected job celebration updated.',
        'applicant_not_found' => 'Applicant Not Found.',
    ],

    'company' => [
        'already_verified' => 'Already verified.',
        'verification_complete' => 'Company verification complete.',
        'only_owner_can_delete' => 'Only owner can delete company.',
        'logged_in_with_zoom' => 'Successfully logged in with Zoom. Please submit the form again.',
        'please_update_package_to_create_company' => 'Please contact easy.jobs to upgrade your package.',
        'logged_in_with_google_calender' => 'Successfully logged in with google calender. Please submit the form again.',
    ],

    'company_setting' => [
        'company_required' => 'Sorry, company is required.',
        'image_not_saved' => 'Sorry, Image can not be saved. Internal problem.',
        'there_is_no_file' => 'Sorry, there is no file.',
        'showcase_photo_not_exists' => 'Showcase photo not exists.',
        'field_not_exits' => 'Field not exists.',

        'app_key' => [
            'generate' => 'Key generated.',
            'write_label' => 'Write a valid label.',
        ],

        'zapier_key' => [
            'generate' => 'Zapier key generated!',
            'write_label' => 'Write a valid label.',
        ],

        'email_setup' => [
            'smtp_setting_reset' => 'SMTP settings reset done.',
            'smtp_setting_reset_failed' => 'Sorry! Cannot reset SMTP setting.',
            'smtp_setting_verify_failed' => 'Sorry! cannot verify SMTP setting.',
        ],

        'user' => [
            'user_has_joined' => 'This user has already joined.',
            'user_not_exits_this_company' => 'This user not exists in this company!',
        ],
    ],

    'job' => [
        'ok' => 'ok',
        'job_duplicated' => 'Job duplicated.',
        'invalid_job_id' => 'Invalid Job ID',
        'expired' => ':job expired.',

        'evaluation' => [
            'questions_removed' => ':type questions successfully removed from job.', // @type[Screening, Quiz]
            'cant_removed_questions' => 'Cannot remove :type question.', // @type[screening, quiz]
            'quiz_not_found' => 'Quiz not found.'
        ],

        'job_pipeline' => [
            'candidate_rejected' => 'Candidate rejected.',
            'pipeline_view_mode_changed' => 'Pipeline view mode changed.',
            'remote_interview_not_supported' => 'Remote interview not supported. Please upgrade subscription package.',
        ],

        'collaborator' => [
            'job_collaborator_removed' => 'Job collaborator is removed from job.',
            'already_added_different_role' => 'User already added in a different role. Cannot be added as collaborator.',
            'already_added_as_collaborator' => 'User already added a collaborator in this job.',
            'already_added_as_collaborator_role' => 'User already added as collaborator role.'
        ],

        'candidate' => [
            'invitation_link_send_candidate' => 'Invitation link is sent to candidate.',
            'invitation_link_send_candidates' => 'Invitation link is sent to candidate(s).',
        ],
        'quiz' => [
            'already_started' => 'Applicant job quiz already started.',
            'not_started' => 'Applicant job quiz not started.',
            'completed' => 'Applicant job quiz already completed.',
            'time_over' => 'Applicant quiz time over.',
            'other_quiz_running' => 'Applicant other quiz running.'
        ]
    ],

    'assessment' => [
        'already_exits' => 'Assessment name already exists.',
        'candidate_added_assessments' => 'Candidate(s) added to assessments.',
        'cant_update_assessment' => 'Cannot update assessment. Candidate(s) already attended this assessment.',

        'candidate' => [
            'already_started' => 'Assessment already started.',
            'not_started' => 'Assessment not started.',
            'completed' => 'Assessment already completed.',
            'time_over' => 'Assessment time over.',
            'other_assessment_running' => 'Applicant other assessment running.'
        ]
    ],

    'user' => [
    'company_changed' => 'Company changed.',
    'password_change' => 'Password change.',
    'password_not_changed' => 'Sorry! Password not changed.',
    'wrong_current_password' => 'Wrong current password.',
    'check_email_otp' => 'Check email for OTP.',
    'otp_code_not_send' => 'Sorry! OTP code not send.',
    'notification_cleared' => 'Notification cleared.',
    'otp_invalid_or_expire' => 'Sorry! OTP code is invalid or expired.',

],

    'subscription' => [
    'invalid_plan' => 'Invalid plan.',
    'invalid_coupon_code' => 'Invalid coupon code.',
    'subscribed_free_packaged' => 'Subscribed to free package.',
    'package_deleted' => 'Subscription Package Deleted',
],

    'manager' => [
    'accepted' => 'Invitation accepted.',
    'rejected' => 'Invitation rejected.',
],

    'testlify' => [
        'invite_cancelled' => 'Invite cancelled.',
        'invite_cancelled_failed' => 'Invite Cancelled Failed.',
        'successfully_assigned_assessment' => 'Successfully Assigned Testlify Assessment.',
        'connect_testlify' => 'Testlify successfully connected.',
        'otp_send_testlify' => 'OTP send successfully.',
        'disconnect_testlify' => 'Testlify successfully disconnected.',
        'candidate_assessment_status_update' => 'Successfully Candidate Assigned Assessment Status.'
    ],
];
