FROM php:7.1-fpm-alpine

RUN docker-php-ext-install pdo_mysql
RUN apk update && apk add --no-cache libmcrypt-dev libmcrypt \
    && docker-php-ext-install -j$(nproc) mcrypt \
    && docker-php-ext-install -j$(nproc) pdo \
    && docker-php-ext-install -j$(nproc) bcmath \
    && docker-php-ext-configure opcache --enable-opcache \
    && docker-php-ext-install opcache

RUN apk add nano
RUN echo http://dl-2.alpinelinux.org/alpine/edge/community/ >> /etc/apk/repositories
RUN apk --no-cache add shadow

COPY . /var/www/html
WORKDIR /var/www/html

RUN usermod -a -G www-data root
RUN chgrp -R www-data storage

RUN chown -R www-data:www-data ./storage
RUN chmod -R 0777 ./storage


RUN ln -s ./secret/.env .env

RUN chmod +x ./devops/deploy/runner/run-cron

ENTRYPOINT ["./devops/deploy/runner/run-cron"]
