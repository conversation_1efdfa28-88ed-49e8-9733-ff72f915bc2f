FROM php:7.1.13-fpm-alpine

RUN apk update && apk add --no-cache \
    libmcrypt-dev \
    libmcrypt \
    zip \
    jq

RUN apk add --no-cache freetype libpng libjpeg-turbo freetype-dev libpng-dev libjpeg-turbo-dev

    RUN apk add --no-cache zlib-dev \
        icu-dev \
        libwebp-dev \
        libxpm-dev \
        g++ \
        vim

RUN docker-php-ext-install mcrypt \
         pdo \
         pdo_mysql \
         zip \
         bcmath \
         mbstring \
         tokenizer \
         exif

RUN docker-php-ext-configure gd \
        --enable-gd-native-ttf \
        --with-freetype-dir=/usr/include/freetype2 \
        --with-png-dir=/usr/include \
        --with-jpeg-dir=/usr/include \
        --with-webp-dir=/usr/include \
    && docker-php-ext-install gd \
    && docker-php-ext-enable gd \
    && docker-php-ext-configure intl \
    && docker-php-ext-install intl


RUN apk add --update --no-cache autoconf g++ make \
&& pecl install redis \
&&  docker-php-ext-enable redis

RUN docker-php-ext-configure opcache --enable-opcache \
    && docker-php-ext-install opcache

RUN apk add --no-cache imagemagick-dev

RUN set -ex \
    && apk add --no-cache --virtual .phpize-deps $PHPIZE_DEPS imagemagick-dev libtool \
    && export CFLAGS="$PHP_CFLAGS" CPPFLAGS="$PHP_CPPFLAGS" LDFLAGS="$PHP_LDFLAGS" \
    && pecl install imagick-3.4.3 \
    && docker-php-ext-enable imagick \
    && apk add --no-cache --virtual .imagick-runtime-deps imagemagick \
    && apk del .phpize-deps

RUN apk add nano
RUN apk add nginx

RUN echo http://dl-2.alpinelinux.org/alpine/edge/community/ >> /etc/apk/repositories
RUN apk --no-cache add shadow

RUN mkdir -p /run/nginx

COPY . /var/www/html
WORKDIR /var/www/html

# RUN rm /etc/nginx/sites-enabled/default

COPY ./devops/deploy/deploy.vhost.conf /etc/nginx/conf.d/default.conf
COPY ./devops/deploy/nginx.conf /etc/nginx/nginx.conf

COPY ./devops/deploy/php.ini $PHP_INI_DIR/
COPY ./devops/deploy/opcache.ini $PHP_INI_DIR/conf.d/

RUN usermod -a -G www-data root
RUN chgrp -R www-data storage

RUN chown -R www-data:www-data ./storage
RUN chown -R www-data:www-data ./public
RUN chmod -R 0777 ./storage

RUN ln -s ./secret/.env .env

RUN chmod +x ./devops/deploy/runner/run-web

ENTRYPOINT ["./devops/deploy/runner/run-web"]

EXPOSE 80
