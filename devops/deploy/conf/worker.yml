apiVersion: apps/v1beta1
kind: Deployment
metadata:
  name: worker-deployment
  namespace: easyjobs-devops
spec:
  replicas: 1
  selector:
    matchLabels:
      app: worker-app
  template:
    metadata:
      labels:
        project: easyjobs
        app: worker-app
    spec:
      containers:
        - name: worker-app
          image: nahidz/easyjobs:worker-dev-25473001
          imagePullPolicy: Always
          volumeMounts:
            - name: easyjobs-secret
              mountPath: "/var/www/html/secret"
              readOnly: true
          ports:
            - containerPort: 80
              name: web-port
              protocol: TCP
      volumes:
        - name: easyjobs-secret
          secret:
            secretName: easyjobs-secret
      imagePullSecrets:
        - name: regcred
