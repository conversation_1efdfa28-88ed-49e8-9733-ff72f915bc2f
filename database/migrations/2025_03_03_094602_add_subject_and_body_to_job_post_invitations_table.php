<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_post_invitations', function (Blueprint $table) {
            $table->string('subject')->after('applicant_email')->nullable();
            $table->text('body')->after('subject')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_post_invitations', function (Blueprint $table) {
            $table->dropColumn(['subject', 'body']);
        });
    }
};
