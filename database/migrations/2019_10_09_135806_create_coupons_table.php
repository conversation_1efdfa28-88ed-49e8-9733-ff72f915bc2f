<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCouponsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('code')->unique();
            $table->enum('scope',['appsumo','in_application'])->default('in_application');
            $table->tinyInteger('discount_type')->default(1);
            $table->decimal('amount');
            $table->boolean('used')->default(false);
            $table->decimal('min_purchased')->nullable();
            $table->date('starts_at');
            $table->date('ends_at');
            $table->text('meta')->nullable();
            $table->integer('quantity')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupons');
    }
}
