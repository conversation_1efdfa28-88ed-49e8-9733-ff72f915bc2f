<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('user_id');
            $table->string('trans_id', 50)->nullable();
            $table->tinyInteger('payment_method')->default(\App\Enums\PaymentMethod::STRIPE);
            $table->text('desc')->nullable();
            $table->tinyInteger('type')->default(\App\Enums\PaymentType::SUBSCRIPTION);
            $table->decimal('price')->default(0);
            $table->decimal('discount')->default(0);
            $table->decimal('total')->storedAs("(price - discount)");
            $table->longText('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transactions');
    }
}
