<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ShiftWorkingHoursToMeta extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('job_posts', function (Blueprint $table) {
            if (Schema::hasColumn('job_posts','working_hour_start')){
                $jobPosts = \App\Models\JobPost::all()->each(function ($jobPost){
                    if (!blank($jobPost->working_hour_start)) {
                        $meta = data_get($jobPost,'meta');
                        $meta['working_days'] = [
                            [
                                'days' => [],
                                'time' => [
                                    'starts' => \Carbon\Carbon::parse($jobPost->working_hour_start)->format('g:i A'),
                                    'ends' => \Carbon\Carbon::parse($jobPost->working_hour_end)->format('g:i A')
                                ]
                            ]
                        ];
                        $jobPost->meta = $meta;
                        $jobPost->save();
                    }
                });

                $table->dropColumn(['working_hour_start','working_hour_end']);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('job_posts', function (Blueprint $table) {
            $table->time('working_hour_start')->nullable()->after('weekly_holidays');
            $table->time('working_hour_end')->nullable()->after('weekly_holidays');
        });

        $jobPosts = \App\Models\JobPost::all()->each(function ($jobPost){
            $meta = data_get($jobPost,'meta');
            $starts = data_get($meta,'working_days.0.time.starts');
            $ends = data_get($meta,'working_days.0.time.ends');

            if (data_get($meta,'working_days')) {
                unset($meta['working_days']);
                $jobPost->meta = $meta;
            }

            if (!blank($starts)) {
                $starts = \Carbon\Carbon::parse($starts)->format("H:i:s");
                $jobPost->working_hour_start = $starts;
            }

            if (!blank($ends)) {
                $ends = \Carbon\Carbon::parse($ends)->format("H:i:s");
                $jobPost->working_hour_end = $ends;
            }

            $jobPost->save();
        });
    }
}
