<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTemplateSlugColumnToCompaniesTable extends Migration
{
    public function up()
    {

        Schema::table('companies', function (Blueprint $table) {
            $table->string('template_slug')->after('templates')->default('default');
            $table->dropColumn('template_id');

        });
    }

    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('template_slug');
            $table->unsignedBigInteger('template_id')->after('verification_code')->nullable();
        });
    }
}